#!/usr/bin/env python3
"""
Script cài đặt các công cụ bảo mật cho AutoK
"""

import subprocess
import sys
import os

def install_package(package_name, description=""):
    """Cai dat package Python"""
    print(f"Dang cai dat {package_name}...")
    if description:
        print(f"   {description}")

    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"Da cai dat {package_name}")
        return True
    except subprocess.CalledProcessError:
        print(f"Loi khi cai dat {package_name}")
        return False

def install_security_tools():
    """Cai dat cac cong cu bao mat"""

    print("Cai dat cac cong cu bao mat cho AutoK")
    print("=" * 50)

    # Danh sach packages can thiet
    packages = [
        ("pyarmor", "Cong cu obfuscation manh nhat cho Python"),
        ("nuitka", "Compiler Python thanh binary"),
        ("cryptography", "Thu vien ma hoa"),
        ("pyinstaller", "Backup compiler (tuy chon)"),
    ]

    success_count = 0

    for package, description in packages:
        if install_package(package, description):
            success_count += 1
        print()

    print("=" * 50)
    print(f"Da cai dat thanh cong {success_count}/{len(packages)} packages")

    # Huong dan cai dat UPX
    print("\nCai dat UPX (tuy chon - de nen file exe):")
    print("   Windows: Tai tu https://upx.github.io/")
    print("   Linux: sudo apt install upx-ucl")
    print("   macOS: brew install upx")

    return success_count == len(packages)

def create_security_config():
    """Tao file cau hinh bao mat"""
    config_content = '''# Cau hinh bao mat AutoK
SECURITY_ENABLED = True
OBFUSCATION_LEVEL = 2
ANTI_DEBUG = True
LICENSE_CHECK = True
INTEGRITY_CHECK = True

# PyArmor settings
PYARMOR_ADVANCED = 2
PYARMOR_RESTRICT = 4
PYARMOR_JIT = True
PYARMOR_MIX_STR = True

# Nuitka settings
NUITKA_OPTIMIZE = True
NUITKA_ONEFILE = True
NUITKA_NO_CONSOLE = True
'''

    with open("security_config.py", "w", encoding="utf-8") as f:
        f.write(config_content)

    print("Da tao file security_config.py")

def main():
    print("Setup bao mat cho AutoK")
    print("Day la script cai dat cac cong cu can thiet de build ung dung bao mat")
    print()

    # Cai dat tools
    if install_security_tools():
        print("Cai dat hoan tat!")

        # Tao config
        create_security_config()

        print("\nCac buoc tiep theo:")
        print("1. Chay: python build_secure.py")
        print("2. File exe bao mat se duoc tao trong thu muc dist_secure/")
        print("3. Test file exe truoc khi phan phoi")

        print("\nCac tinh nang bao mat da duoc kich hoat:")
        print("   Code obfuscation voi PyArmor")
        print("   Binary compilation voi Nuitka")
        print("   Anti-debugging protection")
        print("   Integrity checking")
        print("   License validation")

    else:
        print("Cai dat khong hoan tat. Vui long kiem tra lai.")
        sys.exit(1)

if __name__ == "__main__":
    main()
