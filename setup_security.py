#!/usr/bin/env python3
"""
Script cài đặt các công cụ bảo mật cho AutoK
"""

import subprocess
import sys
import os

def install_package(package_name, description=""):
    """Cài đặt package Python"""
    print(f"📦 Đang cài đặt {package_name}...")
    if description:
        print(f"   {description}")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✅ Đã cài đặt {package_name}")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ Lỗi khi cài đặt {package_name}")
        return False

def install_security_tools():
    """Cài đặt các công cụ bảo mật"""
    
    print("🔒 Cài đặt các công cụ bảo mật cho AutoK")
    print("=" * 50)
    
    # Danh sách packages cần thiết
    packages = [
        ("pyarmor", "Công cụ obfuscation mạnh nhất cho Python"),
        ("nuitka", "Compiler Python thành binary"),
        ("cryptography", "Thư viện mã hóa"),
        ("pyinstaller", "Backup compiler (tùy chọn)"),
    ]
    
    success_count = 0
    
    for package, description in packages:
        if install_package(package, description):
            success_count += 1
        print()
    
    print("=" * 50)
    print(f"✅ Đã cài đặt thành công {success_count}/{len(packages)} packages")
    
    # Hướng dẫn cài đặt UPX
    print("\n📦 Cài đặt UPX (tùy chọn - để nén file exe):")
    print("   Windows: Tải từ https://upx.github.io/")
    print("   Linux: sudo apt install upx-ucl")
    print("   macOS: brew install upx")
    
    return success_count == len(packages)

def create_security_config():
    """Tạo file cấu hình bảo mật"""
    config_content = '''# Cấu hình bảo mật AutoK
SECURITY_ENABLED = True
OBFUSCATION_LEVEL = 2
ANTI_DEBUG = True
LICENSE_CHECK = True
INTEGRITY_CHECK = True

# PyArmor settings
PYARMOR_ADVANCED = 2
PYARMOR_RESTRICT = 4
PYARMOR_JIT = True
PYARMOR_MIX_STR = True

# Nuitka settings
NUITKA_OPTIMIZE = True
NUITKA_ONEFILE = True
NUITKA_NO_CONSOLE = True
'''
    
    with open("security_config.py", "w") as f:
        f.write(config_content)
    
    print("✅ Đã tạo file security_config.py")

def main():
    print("🚀 Setup bảo mật cho AutoK")
    print("Đây là script cài đặt các công cụ cần thiết để build ứng dụng bảo mật")
    print()
    
    # Cài đặt tools
    if install_security_tools():
        print("🎉 Cài đặt hoàn tất!")
        
        # Tạo config
        create_security_config()
        
        print("\n📋 Các bước tiếp theo:")
        print("1. Chạy: python build_secure.py")
        print("2. File exe bảo mật sẽ được tạo trong thư mục dist_secure/")
        print("3. Test file exe trước khi phân phối")
        
        print("\n🔒 Các tính năng bảo mật đã được kích hoạt:")
        print("   ✅ Code obfuscation với PyArmor")
        print("   ✅ Binary compilation với Nuitka") 
        print("   ✅ Anti-debugging protection")
        print("   ✅ Integrity checking")
        print("   ✅ License validation")
        
    else:
        print("❌ Cài đặt không hoàn tất. Vui lòng kiểm tra lại.")
        sys.exit(1)

if __name__ == "__main__":
    main()
