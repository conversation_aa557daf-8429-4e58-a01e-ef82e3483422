import base64
import hashlib
import time
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

class SecurityCore:
    def __init__(self):
        self._key = None
        self._salt = b'AutoK_Security_Salt_2024'
        
    def _generate_key(self, password: str) -> bytes:
        """Tạo key từ password"""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=self._salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        return key
    
    def encrypt_sensitive_data(self, data: str, password: str) -> str:
        """Mã hóa dữ liệu nhạy cảm"""
        key = self._generate_key(password)
        f = Fernet(key)
        encrypted_data = f.encrypt(data.encode())
        return base64.urlsafe_b64encode(encrypted_data).decode()
    
    def decrypt_sensitive_data(self, encrypted_data: str, password: str) -> str:
        """Giải mã dữ liệu nh<PERSON> cảm"""
        try:
            key = self._generate_key(password)
            f = Fernet(key)
            decoded_data = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = f.decrypt(decoded_data)
            return decrypted_data.decode()
        except:
            return None
    
    def validate_license(self, license_key: str) -> bool:
        """Xác thực license - logic phức tạp"""
        # Thêm logic xác thực phức tạp ở đây
        expected_hash = "your_expected_hash_here"
        actual_hash = hashlib.sha256(license_key.encode()).hexdigest()
        return actual_hash == expected_hash
    
    def check_integrity(self) -> bool:
        """Kiểm tra tính toàn vẹn của ứng dụng"""
        # Kiểm tra checksum của file quan trọng
        import os
        current_file = __file__
        if os.path.exists(current_file):
            with open(current_file, 'rb') as f:
                content = f.read()
                file_hash = hashlib.md5(content).hexdigest()
                # So sánh với hash đã biết
                return True
        return False
    
    def anti_debug_check(self) -> bool:
        """Kiểm tra chống debug"""
        import sys
        # Kiểm tra debugger
        if hasattr(sys, 'gettrace') and sys.gettrace() is not None:
            return False
        
        # Kiểm tra timing attack
        start_time = time.time()
        time.sleep(0.01)
        end_time = time.time()
        if (end_time - start_time) > 0.05:  # Quá chậm, có thể đang debug
            return False
            
        return True

# Sử dụng decorator để bảo vệ function quan trọng
def secure_function(func):
    def wrapper(*args, **kwargs):
        security = SecurityCore()
        if not security.anti_debug_check():
            raise Exception("Security violation detected")
        if not security.check_integrity():
            raise Exception("Application integrity compromised")
        return func(*args, **kwargs)
    return wrapper
