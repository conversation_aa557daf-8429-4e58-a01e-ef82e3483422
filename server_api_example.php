<?php
/**
 * AutoK Update Server API
 * Đặt file này trên web server của bạn
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// Cấu hình
$config = [
    'latest_version' => '1.1.0',
    'download_url' => 'https://your-server.com/downloads/autok_v1.1.0.zip',
    'changelog' => "Phiên bản 1.1.0:\n- Thêm tính năng auto update\n- Cải thiện giao diện\n- Sửa lỗi minor bugs\n- Tối ưu hóa hiệu suất",
    'min_version' => '1.0.0',
    'force_update' => false
];

// Xử lý request
$action = $_GET['action'] ?? 'version';

switch ($action) {
    case 'version':
        handle_version_check();
        break;
    
    case 'download':
        handle_download();
        break;
    
    default:
        http_response_code(400);
        echo json_encode(['error' => 'Invalid action']);
        break;
}

function handle_version_check() {
    global $config;
    
    $response = [
        'version' => $config['latest_version'],
        'download_url' => $config['download_url'],
        'changelog' => $config['changelog'],
        'min_version' => $config['min_version'],
        'force_update' => $config['force_update'],
        'timestamp' => time()
    ];
    
    echo json_encode($response);
}

function handle_download() {
    global $config;
    
    // Đường dẫn đến file update
    $file_path = '/path/to/your/autok_updates/autok_v' . $config['latest_version'] . '.zip';
    
    if (!file_exists($file_path)) {
        http_response_code(404);
        echo json_encode(['error' => 'Update file not found']);
        return;
    }
    
    // Log download
    $log_data = [
        'timestamp' => date('Y-m-d H:i:s'),
        'ip' => $_SERVER['REMOTE_ADDR'],
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        'version' => $config['latest_version']
    ];
    
    file_put_contents(
        'download_log.txt', 
        json_encode($log_data) . "\n", 
        FILE_APPEND | LOCK_EX
    );
    
    // Gửi file
    header('Content-Type: application/zip');
    header('Content-Disposition: attachment; filename="autok_update.zip"');
    header('Content-Length: ' . filesize($file_path));
    
    readfile($file_path);
}

// Hàm tiện ích để cập nhật version từ admin panel
function update_version($new_version, $download_url, $changelog) {
    global $config;
    
    // Cập nhật config (trong thực tế nên lưu vào database)
    $config['latest_version'] = $new_version;
    $config['download_url'] = $download_url;
    $config['changelog'] = $changelog;
    
    // Lưu vào file config
    file_put_contents('update_config.json', json_encode($config, JSON_PRETTY_PRINT));
    
    return true;
}

// Admin endpoint để cập nhật version
if (isset($_POST['admin_action']) && $_POST['admin_action'] === 'update_version') {
    $admin_key = $_POST['admin_key'] ?? '';
    
    // Kiểm tra admin key (thay đổi key này)
    if ($admin_key === 'your_secret_admin_key_here') {
        $new_version = $_POST['version'] ?? '';
        $download_url = $_POST['download_url'] ?? '';
        $changelog = $_POST['changelog'] ?? '';
        
        if ($new_version && $download_url) {
            update_version($new_version, $download_url, $changelog);
            echo json_encode(['success' => true, 'message' => 'Version updated successfully']);
        } else {
            echo json_encode(['error' => 'Missing required fields']);
        }
    } else {
        http_response_code(403);
        echo json_encode(['error' => 'Unauthorized']);
    }
}
?>
