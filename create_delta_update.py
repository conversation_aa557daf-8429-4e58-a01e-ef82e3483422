#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script tạo Delta Update Package cho AutoK
Chỉ đóng gói các file đã thay đổi so với version trước
"""

import os
import sys
import json
import zipfile
import hashlib
from datetime import datetime
from pathlib import Path

def calculate_file_hash(file_path):
    """Tính hash của file"""
    hash_md5 = hashlib.md5()
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except Exception:
        return None

def get_file_list():
    """L<PERSON>y danh sách tất cả file cần theo dõi"""
    files_to_track = [
        # Core files
        'AutoK.py', 'Form.py', 'Form.ui',
        
        # Module files
        'addads.py', 'addwallet.py', 'chplay.py', 'closepayment.py',
        'deletecardclose.py', 'mua.py', 'password_manager.py',
        'path_manager.py', 'license_manager.py', 'LicenseManagerDialog.py',
        
        # Security files
        'google_creds', 'google_creds_manager.py', 'autok_google_auth.py',
        'crypto_utils.py', 'auto_updater.py', 'version.txt',
        
        # Config files
        'security_config.py', 'devices_config.json'
    ]
    
    # Thêm files trong thư mục
    folders_to_track = ['template', 'data', 'icon']
    
    all_files = []
    
    # Thêm files đơn lẻ
    for file_path in files_to_track:
        if os.path.exists(file_path):
            all_files.append(file_path)
    
    # Thêm files trong thư mục
    for folder in folders_to_track:
        if os.path.exists(folder):
            for root, dirs, files in os.walk(folder):
                for file in files:
                    file_path = os.path.join(root, file).replace('\\', '/')
                    all_files.append(file_path)
    
    return all_files

def create_file_manifest(version):
    """Tạo manifest của tất cả file"""
    manifest = {
        'version': version,
        'created_at': datetime.now().isoformat(),
        'files': {}
    }
    
    files = get_file_list()
    
    for file_path in files:
        if os.path.exists(file_path):
            file_hash = calculate_file_hash(file_path)
            file_size = os.path.getsize(file_path)
            file_mtime = os.path.getmtime(file_path)
            
            manifest['files'][file_path] = {
                'hash': file_hash,
                'size': file_size,
                'mtime': file_mtime
            }
    
    return manifest

def compare_manifests(old_manifest, new_manifest):
    """So sánh hai manifest để tìm file thay đổi"""
    changes = {
        'added': [],
        'modified': [],
        'deleted': []
    }
    
    old_files = old_manifest.get('files', {})
    new_files = new_manifest.get('files', {})
    
    # Tìm file mới và file đã thay đổi
    for file_path, file_info in new_files.items():
        if file_path not in old_files:
            # File mới
            changes['added'].append({
                'path': file_path,
                'action': 'add',
                'hash': file_info['hash'],
                'size': file_info['size']
            })
        elif old_files[file_path]['hash'] != file_info['hash']:
            # File đã thay đổi
            changes['modified'].append({
                'path': file_path,
                'action': 'update',
                'hash': file_info['hash'],
                'size': file_info['size'],
                'old_hash': old_files[file_path]['hash']
            })
    
    # Tìm file đã xóa
    for file_path in old_files:
        if file_path not in new_files:
            changes['deleted'].append({
                'path': file_path,
                'action': 'delete',
                'old_hash': old_files[file_path]['hash']
            })
    
    return changes

def create_delta_package(from_version, to_version, changelog=""):
    """Tạo delta update package"""
    
    print(f"🔄 Tạo Delta Update Package")
    print(f"📝 From version: {from_version}")
    print(f"📝 To version: {to_version}")
    print(f"📝 Changelog: {changelog}")
    
    # Đọc manifest cũ
    old_manifest_file = f"manifest_v{from_version}.json"
    if not os.path.exists(old_manifest_file):
        print(f"❌ Không tìm thấy manifest cho version {from_version}")
        print(f"   Vui lòng tạo manifest trước: python create_delta_update.py manifest {from_version}")
        return None
    
    with open(old_manifest_file, 'r') as f:
        old_manifest = json.load(f)
    
    # Tạo manifest mới
    new_manifest = create_file_manifest(to_version)
    
    # Lưu manifest mới
    new_manifest_file = f"manifest_v{to_version}.json"
    with open(new_manifest_file, 'w') as f:
        json.dump(new_manifest, f, indent=2)
    print(f"✅ Đã tạo manifest mới: {new_manifest_file}")
    
    # So sánh để tìm thay đổi
    changes = compare_manifests(old_manifest, new_manifest)
    
    total_changes = len(changes['added']) + len(changes['modified']) + len(changes['deleted'])
    
    if total_changes == 0:
        print("ℹ️ Không có thay đổi nào được tìm thấy")
        return None
    
    print(f"\n📊 Thống kê thay đổi:")
    print(f"   - File mới: {len(changes['added'])}")
    print(f"   - File sửa đổi: {len(changes['modified'])}")
    print(f"   - File xóa: {len(changes['deleted'])}")
    print(f"   - Tổng: {total_changes} thay đổi")
    
    # Tạo delta package
    output_filename = f"autok_delta_v{to_version}.zip"
    
    try:
        with zipfile.ZipFile(output_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
            
            # Tạo delta manifest
            delta_manifest = {
                'from_version': from_version,
                'to_version': to_version,
                'changelog': changelog,
                'created_at': datetime.now().isoformat(),
                'files': []
            }
            
            # Thêm file đã thay đổi và file mới
            for file_info in changes['added'] + changes['modified']:
                file_path = file_info['path']
                if os.path.exists(file_path):
                    arcname = f"AutoK/{file_path}"
                    zipf.write(file_path, arcname)
                    delta_manifest['files'].append(file_info)
                    print(f"✅ Added: {file_path}")
            
            # Thêm thông tin file đã xóa
            for file_info in changes['deleted']:
                delta_manifest['files'].append(file_info)
                print(f"🗑️ Marked for deletion: {file_info['path']}")
            
            # Thêm delta manifest vào package
            zipf.writestr("AutoK/delta_manifest.json", json.dumps(delta_manifest, indent=2))
            
            # Thêm metadata
            metadata = {
                'type': 'delta',
                'from_version': from_version,
                'to_version': to_version,
                'changelog': changelog,
                'created_at': datetime.now().isoformat(),
                'total_changes': total_changes
            }
            zipf.writestr("AutoK/update_metadata.json", json.dumps(metadata, indent=2))
        
        print(f"\n🎉 Delta package created: {output_filename}")
        
        # Hiển thị kích thước
        file_size = os.path.getsize(output_filename)
        file_size_kb = file_size / 1024
        print(f"📦 Package size: {file_size_kb:.2f} KB")
        
        return output_filename
        
    except Exception as e:
        print(f"❌ Error creating delta package: {e}")
        return None

def create_manifest_only(version):
    """Chỉ tạo manifest cho version hiện tại"""
    print(f"📝 Tạo manifest cho version {version}")
    
    manifest = create_file_manifest(version)
    manifest_file = f"manifest_v{version}.json"
    
    with open(manifest_file, 'w') as f:
        json.dump(manifest, f, indent=2)
    
    print(f"✅ Đã tạo manifest: {manifest_file}")
    print(f"📊 Tổng số file: {len(manifest['files'])}")
    
    return manifest_file

def main():
    """Hàm chính"""
    print("🚀 AutoK Delta Update Creator")
    print("=" * 40)
    
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python create_delta_update.py manifest <version>")
        print("  python create_delta_update.py delta <from_version> <to_version> [changelog]")
        print("\nExamples:")
        print("  python create_delta_update.py manifest 1.0.0")
        print("  python create_delta_update.py delta 1.0.0 1.1.0 \"Bug fixes\"")
        return
    
    command = sys.argv[1]
    
    if command == "manifest":
        if len(sys.argv) < 3:
            print("❌ Missing version for manifest")
            return
        
        version = sys.argv[2]
        create_manifest_only(version)
        
    elif command == "delta":
        if len(sys.argv) < 4:
            print("❌ Missing versions for delta update")
            return
        
        from_version = sys.argv[2]
        to_version = sys.argv[3]
        changelog = sys.argv[4] if len(sys.argv) > 4 else ""
        
        package_file = create_delta_package(from_version, to_version, changelog)
        
        if package_file:
            print(f"\n📋 Next steps:")
            print(f"1. Upload {package_file} to your server")
            print(f"2. Update server API config for delta updates")
            print(f"3. Test delta update process")
    
    else:
        print(f"❌ Unknown command: {command}")

if __name__ == "__main__":
    main()
