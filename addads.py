import sys
import subprocess
import random
import time
import os
import cv2
import numpy as np
import datetime
import pyotp  # Thêm thư viện pyotp để tạo mã OTP động

# Import path_manager để quản lý đường dẫn
import path_manager

# Biến toàn cục để lưu đường dẫn ADB
ADB_PATH = "adb"  # Giá trị mặc định

class add_ads:
    """
    Thực hiện các hành động mô phỏng thao tác người dùng trên thiết bị Android qua ADB.
    """

    def __init__(self, serial, num_loops=1, device_index=0, total_devices=1, adb_path=None):
        self.serial = serial
        # Khởi tạo thuộc tính password với giá trị mặc định là chuỗi rỗng
        self.password = ""
        # Khởi tạo thuộc tính email với giá trị mặc định là chuỗi rỗng
        self.email = ""
        # Số lần lặp lại quy trình thêm thẻ
        self.num_loops = num_loops
        # Biến đếm vòng lặp hiện tại
        self.current_loop = 0
        # Chỉ số thiết bị trong danh sách thiết bị (để lấy email/password đúng)
        self.device_index = device_index
        # Chỉ số thiết bị cho phân phối thẻ (giống device_index)
        self.card_index = device_index
        # Tổng số thiết bị (để phân phối thẻ)
        self.total_devices = total_devices
        # Danh sách thẻ từ file data/card.txt
        self.cards = []

        # Biến để theo dõi trạng thái hiện tại cho UI update (giống addwallet)
        self.current_card = 0
        self.total_cards = 0
        self.current_card_info = None
        self.update_status_func = None

        # In thông tin debug
        print(f"🔍 addads.__init__: serial = {serial}")
        print(f"🔍 addads.__init__: num_loops = {num_loops}")
        print(f"🔍 addads.__init__: device_index = {device_index}")
        print(f"🔍 addads.__init__: card_index = {self.card_index}")
        print(f"🔍 addads.__init__: total_devices = {total_devices}")
        print(f"🔍 addads.__init__: adb_path = {adb_path}")
        print(f"🔍 addads.__init__: sys.executable = {sys.executable}")
        print(f"🔍 addads.__init__: os.getcwd() = {os.getcwd()}")
        print(f"🔍 addads.__init__: __file__ = {__file__}")

        # Kiểm tra xem có đang chạy từ file exe không
        is_frozen = getattr(sys, 'frozen', False)
        print(f"🔍 addads.__init__: is_frozen = {is_frozen}")

        # Cập nhật biến toàn cục ADB_PATH nếu được cung cấp
        global ADB_PATH
        if adb_path:
            ADB_PATH = adb_path
            print(f"✅ Đã cập nhật đường dẫn ADB: {ADB_PATH}")

        # Đọc danh sách thẻ từ file
        self.load_cards()
        # Đọc mật khẩu từ file mail.txt
        self.load_password_from_mail()

    def load_cards(self):
        """
        Đọc danh sách thẻ từ file data/card.txt và phân phối cho các thiết bị
        Format của file: số thẻ|tháng|năm|cvv
        Ví dụ: ****************|01|25|123
        """
        try:
            # Lấy đường dẫn đến file card.txt
            try:
                # Thử sử dụng get_txt_path (phương pháp đơn giản nhất)
                card_file_path = path_manager.get_txt_path("card.txt")
                print(f"🔍 Đường dẫn file card.txt từ get_txt_path: {card_file_path}")
            except Exception as e:
                print(f"⚠️ Lỗi khi sử dụng get_txt_path: {str(e)}")
                # Sử dụng get_data_path như phương án dự phòng
                card_file_path = path_manager.get_data_path("card.txt")
                print(f"🔍 Đường dẫn file card.txt từ get_data_path: {card_file_path}")

            # Kiểm tra thư mục cha
            parent_dir = os.path.dirname(card_file_path)
            print(f"🔍 Thư mục cha: {parent_dir}")
            if os.path.exists(parent_dir):
                print(f"✅ Thư mục cha tồn tại: {parent_dir}")
                print(f"🔍 Nội dung thư mục cha:")
                try:
                    for item in os.listdir(parent_dir):
                        print(f"  - {item}")
                except Exception as e:
                    print(f"❌ Lỗi khi liệt kê nội dung thư mục cha: {str(e)}")
            else:
                print(f"❌ Thư mục cha không tồn tại: {parent_dir}")
                try:
                    os.makedirs(parent_dir, exist_ok=True)
                    print(f"✅ Đã tạo thư mục cha: {parent_dir}")
                except Exception as e:
                    print(f"❌ Lỗi khi tạo thư mục cha: {str(e)}")

            # Kiểm tra xem file có tồn tại không
            if not os.path.exists(card_file_path):
                print(f"⚠️ File card.txt không tồn tại: {card_file_path}")
                # Tạo file card.txt trống với header
                try:
                    with open(card_file_path, "w") as file:
                        file.write("# Danh sách thẻ (format: số thẻ|tháng|năm|cvv)\n")
                        file.write("# Ví dụ: ****************|01|25|123\n")
                    print(f"✅ Đã tạo file {card_file_path}")
                except Exception as e:
                    print(f"❌ Lỗi khi tạo file card.txt: {str(e)}")
                return
            else:
                print(f"✅ File card.txt tồn tại: {card_file_path}")
                # Kiểm tra kích thước file
                try:
                    file_size = os.path.getsize(card_file_path)
                    print(f"📊 Kích thước file card.txt: {file_size} bytes")
                except Exception as e:
                    print(f"⚠️ Không thể lấy kích thước file: {str(e)}")

            # Đọc danh sách thẻ từ file
            try:
                with open(card_file_path, "r") as file:
                    lines = file.readlines()
                print(f"📄 Đã đọc {len(lines)} dòng từ file card.txt")

                # In ra 5 dòng đầu tiên để debug (che giấu thông tin nhạy cảm)
                print("📄 Nội dung 5 dòng đầu tiên (đã che giấu):")
                for i, line in enumerate(lines[:5]):
                    if line.startswith("#") or not line.strip():
                        print(f"  {i+1}: {line.strip()}")
                    else:
                        parts = line.strip().split("|")
                        if len(parts) >= 1:
                            masked_card = "****" + parts[0][-4:] if len(parts[0]) >= 4 else parts[0]
                            masked_line = f"{masked_card}|{'**' if len(parts) > 1 else ''}|{'**' if len(parts) > 2 else ''}|{'***' if len(parts) > 3 else ''}"
                            if len(parts) > 4:
                                masked_line += f"|{parts[4]}"
                            print(f"  {i+1}: {masked_line}")
            except Exception as e:
                print(f"❌ Lỗi khi đọc file card.txt: {str(e)}")
                return

            # Lọc các dòng trống, dòng comment và dòng đã được đánh dấu là đã xử lý
            valid_cards = []
            for line in lines:
                line = line.strip()
                # Bỏ qua dòng trống và dòng comment
                if not line or line.startswith("#"):
                    continue
                # Bỏ qua dòng đã được đánh dấu là đã xử lý (kiểm tra hậu tố)
                # Chia thẻ thành các phần theo dấu |
                parts = line.split("|")

                if len(parts) >= 4:
                    # Phần cuối cùng có thể chứa thông tin trạng thái
                    last_part = parts[-1]

                    # Kiểm tra xem phần cuối có chứa các hậu tố không
                    if "-ok" in last_part or "-error" in last_part or "-expired" in last_part or "-used" in last_part:
                        print(f"⚠️ Bỏ qua thẻ đã được đánh dấu (phần cuối): {line}")
                        continue

                    # Kiểm tra xem tên chủ thẻ có chứa "-ok-" không (nếu có phần thứ 5)
                    if len(parts) >= 5:
                        cardholder_name = parts[4].strip()
                        if "-ok-" in cardholder_name:
                            print(f"⚠️ Bỏ qua thẻ đã được sử dụng (tên chủ thẻ): {line}")
                            continue
                else:
                    # Nếu không thể chia thành các phần, sử dụng cách kiểm tra đơn giản hơn
                    if "-ok" in line or "-error" in line or "-expired" in line or "-used" in line:
                        print(f"⚠️ Bỏ qua thẻ đã được đánh dấu (kiểm tra đơn giản): {line}")
                        continue
                # Kiểm tra xem thẻ đã được sử dụng chưa dựa vào tên chủ thẻ
                if self.is_card_used(line):
                    continue
                # Thêm thẻ vào danh sách
                valid_cards.append(line)

            # Phân phối thẻ cho các thiết bị
            if self.total_devices > 0 and len(valid_cards) > 0:
                # Phân phối thẻ theo phương pháp luân phiên (round-robin)
                # Mỗi thiết bị sẽ lấy các thẻ ở vị trí: device_index, device_index + total_devices, device_index + 2*total_devices, ...
                device_cards = []
                for i in range(len(valid_cards)):
                    if i % self.total_devices == self.card_index:
                        device_cards.append(valid_cards[i])

                self.cards = device_cards

                print(f"✅ Thiết bị {self.serial} (chỉ số {self.card_index}/{self.total_devices}): Đã phân phối {len(self.cards)} thẻ theo phương pháp luân phiên")
            else:
                # Nếu không có thông tin về tổng số thiết bị hoặc không có thẻ nào, sử dụng tất cả các thẻ
                self.cards = valid_cards

            # Cập nhật total_cards để hiển thị progress (giống addwallet)
            self.total_cards = len(self.cards)
            self.current_card = 0

            print(f"✅ Thiết bị {self.serial} (chỉ số {self.card_index}/{self.total_devices}): Đã đọc {len(self.cards)} thẻ từ tổng số {len(valid_cards)} thẻ")

        except Exception as e:
            print(f"❌ Lỗi khi đọc file card.txt: {str(e)}")
            self.cards = []

    def is_card_expired(self, month, year):
        """
        Kiểm tra xem thẻ có quá hạn hay không
        :param month: Tháng hết hạn
        :param year: Năm hết hạn
        :return: True nếu thẻ đã quá hạn, False nếu thẻ còn hạn
        """
        try:
            # Lấy thời gian hiện tại
            now = datetime.datetime.now()
            current_month = now.month
            current_year = now.year

            # Chuyển đổi tháng và năm hết hạn sang số nguyên
            exp_month = int(month)

            # Xử lý năm 2 chữ số
            if len(year) == 2:
                exp_year = 2000 + int(year)
            else:
                exp_year = int(year)

            # Kiểm tra xem thẻ có quá hạn hay không
            if exp_year < current_year:
                return True
            elif exp_year == current_year and exp_month < current_month:
                return True
            else:
                return False

        except Exception as e:
            print(f"❌ Lỗi khi kiểm tra thẻ quá hạn: {str(e)}")
            # Nếu có lỗi, coi như thẻ không quá hạn
            return False

    def is_card_used(self, card_info_str):
        """
        Kiểm tra xem thẻ đã được sử dụng chưa dựa vào thông tin thẻ
        :param card_info_str: Chuỗi thông tin thẻ từ file card.txt
        :return: True nếu thẻ đã được sử dụng, False nếu thẻ chưa được sử dụng
        """
        try:
            # Chia thẻ thành các phần theo dấu |
            parts = card_info_str.split("|")

            if len(parts) >= 4:
                # Phần cuối cùng có thể chứa thông tin trạng thái
                last_part = parts[-1]

                # Kiểm tra xem phần cuối có chứa các hậu tố không
                if "-ok" in last_part or "-error" in last_part or "-expired" in last_part or "-used" in last_part:
                    print(f"⚠️ Thẻ đã được đánh dấu là đã xử lý (phần cuối): {card_info_str}")
                    return True

                # Kiểm tra xem tên chủ thẻ có chứa "-ok-" không (nếu có phần thứ 5)
                if len(parts) >= 5:
                    cardholder_name = parts[4].strip()
                    if "-ok-" in cardholder_name:
                        print(f"⚠️ Thẻ đã được sử dụng (tên chủ thẻ: {cardholder_name})")
                        return True
            else:
                # Nếu không thể chia thành các phần, sử dụng cách kiểm tra đơn giản hơn
                if "-ok" in card_info_str or "-error" in card_info_str or "-expired" in card_info_str or "-used" in card_info_str:
                    print(f"⚠️ Thẻ đã được đánh dấu là đã xử lý (kiểm tra đơn giản): {card_info_str}")
                    return True

            return False
        except Exception as e:
            print(f"❌ Lỗi khi kiểm tra thẻ đã sử dụng: {str(e)}")
            # Nếu có lỗi, coi như thẻ chưa được sử dụng
            return False

    def peek_next_card(self):
        """
        Xem thông tin thẻ tiếp theo mà không xóa khỏi danh sách (giống addwallet)
        :return: tuple với (card_number, month, year, cvv) hoặc None nếu không có thẻ
        """
        try:
            if not self.cards:
                print("❌ Không có thẻ nào trong danh sách được phân phối cho device này")
                return None

            # Lấy thẻ đầu tiên trong danh sách NHƯNG KHÔNG XÓA
            card_info_str = self.cards[0]

            # Phân tích thông tin thẻ
            parts = card_info_str.split('|')
            if len(parts) < 4:
                print(f"❌ Định dạng thẻ không hợp lệ: {card_info_str}")
                return None

            card_number = parts[0].strip()
            month = parts[1].strip()
            year = parts[2].strip()
            cvv_status = parts[3].strip()

            # Tách CVV và status (loại bỏ các suffix cũ nếu có)
            if '-' in cvv_status:
                cvv = cvv_status.split('-')[0]
            else:
                cvv = cvv_status

            print(f"👀 Xem thẻ tiếp theo: {card_number[:4]}****{card_number[-4:]} {month}/{year}")
            return (card_number, month, year, cvv)

        except Exception as e:
            print(f"❌ Lỗi khi xem thông tin thẻ tiếp theo: {e}")
            return None

    def get_next_card(self):
        """
        Lấy thẻ tiếp theo từ danh sách thẻ
        :return: Thông tin thẻ (số thẻ, tháng, năm, cvv, cardholder_name) hoặc None nếu không có thẻ nào
        """
        if not self.cards:
            print("❌ Không có thẻ nào trong danh sách")
            return None

        # Lấy thẻ đầu tiên trong danh sách
        card_info_str = self.cards[0]

        # Xóa thẻ đã lấy khỏi danh sách
        self.cards.pop(0)

        # Cập nhật current_card để hiển thị progress (giống addwallet)
        self.current_card = self.total_cards - len(self.cards)

        # Phân tích thông tin thẻ
        try:
            parts = card_info_str.split("|")
            if len(parts) < 4:
                print(f"❌ Định dạng thẻ không hợp lệ: {card_info_str}")
                return None

            card_number = parts[0].strip()
            month = parts[1].strip()
            year = parts[2].strip()
            cvv = parts[3].strip()

            # Lấy tên chủ thẻ nếu có
            cardholder_name = ""
            if len(parts) >= 5:
                cardholder_name = parts[4].strip()

            # Kiểm tra xem thẻ có quá hạn hay không
            if self.is_card_expired(month, year):
                print(f"⚠️ Thẻ {card_number[-4:]} đã quá hạn (tháng {month}/{year})")
                # Đánh dấu thẻ là đã quá hạn
                if cardholder_name:
                    self.mark_card_as_processed((card_number, month, year, cvv, cardholder_name), "expired")
                else:
                    self.mark_card_as_processed((card_number, month, year, cvv), "expired")
                # Lấy thẻ tiếp theo
                return self.get_next_card()

            # Kiểm tra xem thẻ đã được sử dụng chưa
            if self.is_card_used(card_info_str):
                print(f"⚠️ Thẻ {card_number[-4:]} đã được sử dụng")
                # Đánh dấu thẻ là đã sử dụng
                if cardholder_name:
                    self.mark_card_as_processed((card_number, month, year, cvv, cardholder_name), "used")
                else:
                    self.mark_card_as_processed((card_number, month, year, cvv), "used")
                # Lấy thẻ tiếp theo
                return self.get_next_card()

            print(f"✅ Đã lấy thông tin thẻ: {card_number[-4:]} (tháng {month}/{year}, CVV: {cvv})")

            # Lưu thông tin thẻ hiện tại để hiển thị progress (giống addwallet)
            if cardholder_name:
                self.current_card_info = {
                    'card_number': card_number,
                    'month': month,
                    'year': year,
                    'cvv': cvv,
                    'cardholder_name': cardholder_name
                }
                print(f"✅ Tên chủ thẻ: {cardholder_name}")
                return (card_number, month, year, cvv, cardholder_name)
            else:
                self.current_card_info = {
                    'card_number': card_number,
                    'month': month,
                    'year': year,
                    'cvv': cvv
                }
                return (card_number, month, year, cvv)

        except Exception as e:
            print(f"❌ Lỗi khi phân tích thông tin thẻ: {str(e)}")
            return None

    def load_password_from_mail(self):
        """
        Đọc mật khẩu và email từ file mail.txt dựa trên chỉ số thiết bị
        Format của file: email|password|authentication
        """
        try:
            # Lấy đường dẫn đến file mail.txt
            try:
                # Thử sử dụng get_txt_path (phương pháp đơn giản nhất)
                mail_file_path = path_manager.get_txt_path("mail.txt")
                print(f"🔍 Đường dẫn file mail.txt từ get_txt_path: {mail_file_path}")
            except Exception as e:
                print(f"⚠️ Lỗi khi sử dụng get_txt_path: {str(e)}")
                # Sử dụng get_data_path như phương án dự phòng
                mail_file_path = path_manager.get_data_path("mail.txt")
                print(f"🔍 Đường dẫn file mail.txt từ get_data_path: {mail_file_path}")

            # Kiểm tra xem file có tồn tại không
            if not os.path.exists(mail_file_path):
                print(f"⚠️ Không tìm thấy file {mail_file_path}")
                print(f"⚠️ Vui lòng tạo file {mail_file_path} với định dạng: email|password|authentication")

                # Tạo file mail.txt mẫu
                try:
                    with open(mail_file_path, "w") as f:
                        f.write("# Danh sách email (format: email|password|authentication)\n")
                        f.write("# Ví dụ: <EMAIL>|password123|ABCDEFGHIJKLMNOP\n")
                    print(f"✅ Đã tạo file mail.txt mẫu tại: {mail_file_path}")
                except Exception as e:
                    print(f"❌ Lỗi khi tạo file mail.txt mẫu: {str(e)}")
                return

            # Đọc danh sách email từ file
            with open(mail_file_path, "r") as file:
                lines = file.readlines()

            # Lọc các dòng trống, dòng comment và dòng đã được đánh dấu là đã xử lý
            valid_mails = []
            for line in lines:
                line = line.strip()
                # Bỏ qua dòng trống và dòng comment
                if not line or line.startswith("#"):
                    continue
                # Thêm email vào danh sách
                valid_mails.append(line)

            # Kiểm tra xem có email nào không
            if not valid_mails:
                print(f"⚠️ Không có email nào trong file {mail_file_path}")
                return

            # Lấy email tương ứng với chỉ số thiết bị (sử dụng device_index)
            if self.device_index < len(valid_mails):
                mail_info = valid_mails[self.device_index]
            else:
                # Nếu chỉ số thiết bị lớn hơn số lượng email, sử dụng modulo để lặp lại
                mail_info = valid_mails[self.device_index % len(valid_mails)]

            print(f"🔍 Thiết bị {self.serial} (device_index {self.device_index}) sẽ sử dụng email: {mail_info}")

            # Phân tích thông tin email
            parts = mail_info.split("|")
            if len(parts) >= 2:
                # Lấy email từ cột thứ 1
                self.email = parts[0].strip()
                print(f"✅ Đã đọc email cho thiết bị {self.serial} (device_index {self.device_index}): {self.email}")

                # Lấy mật khẩu từ cột thứ 2
                self.password = parts[1].strip()
                print(f"✅ Đã đọc mật khẩu cho thiết bị {self.serial} (device_index {self.device_index}): {self.password[:2]}{'*' * (len(self.password) - 2)}")

                # Lấy chuỗi xác thực 2FA từ cột thứ 3 (nếu có)
                self.authentication_code = ""
                self.authentication_secret = ""
                if len(parts) >= 3:
                    self.authentication_secret = parts[2].strip()
                    if self.authentication_secret:
                        # Che giấu chuỗi bí mật xác thực
                        masked_secret = self.authentication_secret[:4] + '*' * (len(self.authentication_secret) - 4) if len(self.authentication_secret) > 4 else '*' * len(self.authentication_secret)
                        print(f"✅ Đã đọc chuỗi bí mật xác thực 2FA cho thiết bị {self.serial} (chỉ số {self.card_index}): {masked_secret}")

                        # Thử tạo mã OTP từ chuỗi bí mật
                        try:
                            # Xử lý chuỗi bí mật để đảm bảo định dạng đúng
                            # Loại bỏ khoảng trắng và ký tự đặc biệt
                            clean_secret = ''.join(c for c in self.authentication_secret if c.isalnum())

                            # Đảm bảo chuỗi bí mật có độ dài chia hết cho 8 (yêu cầu của base32)
                            while len(clean_secret) % 8 != 0:
                                clean_secret += '='

                            print(f"🔄 Đang tạo mã OTP từ chuỗi bí mật...")
                            totp = pyotp.TOTP(clean_secret)
                            self.authentication_code = totp.now()
                            print(f"✅ Đã tạo mã OTP động thành công: {self.authentication_code}")

                            # Kiểm tra xem mã OTP có đúng định dạng không (thường là 6 chữ số)
                            if not self.authentication_code.isdigit() or len(self.authentication_code) != 6:
                                print(f"⚠️ Mã OTP không đúng định dạng: {self.authentication_code}")
                                print(f"⚠️ Sẽ sử dụng chuỗi bí mật làm mã xác thực cố định")
                                self.authentication_code = self.authentication_secret
                        except Exception as e:
                            print(f"❌ Lỗi khi tạo mã OTP động: {str(e)}")
                            print(f"⚠️ Sẽ sử dụng chuỗi bí mật làm mã xác thực cố định")
                            self.authentication_code = self.authentication_secret
                    else:
                        print(f"ℹ️ Không có chuỗi bí mật xác thực 2FA cho thiết bị {self.serial} (chỉ số {self.card_index})")
                else:
                    print(f"ℹ️ Không có cột chuỗi bí mật xác thực 2FA trong file {mail_file_path}")
            else:
                print(f"⚠️ Định dạng email không hợp lệ: {mail_info}")
                print(f"⚠️ Vui lòng đảm bảo file {mail_file_path} có định dạng: email|password|authentication")

        except Exception as e:
            print(f"❌ Lỗi khi đọc file mail.txt: {str(e)}")

    def mark_email_as_verification_required(self):
        """Đánh dấu email hiện tại cần verification (captcha) trong file mail_used.txt"""
        try:
            if not self.email:
                print("⚠️ Không có email để đánh dấu verification")
                return

            # Lấy đường dẫn file mail_used.txt
            try:
                mail_used_file_path = path_manager.get_data_path("mail_used.txt")
            except:
                # Fallback nếu path_manager không hoạt động
                mail_used_file_path = os.path.join(os.path.dirname(__file__), "data", "mail_used.txt")

            # Tạo thư mục data nếu chưa tồn tại
            data_dir = os.path.dirname(mail_used_file_path)
            if not os.path.exists(data_dir):
                os.makedirs(data_dir)

            # Đọc file hiện tại (nếu có)
            existing_entries = []
            if os.path.exists(mail_used_file_path):
                with open(mail_used_file_path, "r", encoding="utf-8") as f:
                    existing_entries = f.readlines()

            # Kiểm tra xem email đã được đánh dấu chưa
            email_already_marked = False
            for i, line in enumerate(existing_entries):
                if line.strip().startswith(self.email):
                    # Cập nhật status thành -ver
                    existing_entries[i] = f"{self.email}|-ver\n"
                    email_already_marked = True
                    break

            # Nếu email chưa có trong file, thêm mới
            if not email_already_marked:
                existing_entries.append(f"{self.email}|-ver\n")

            # Ghi lại file
            with open(mail_used_file_path, "w", encoding="utf-8") as f:
                f.writelines(existing_entries)

            print(f"✅ Đã đánh dấu email {self.email} cần verification (-ver) trong mail_used.txt")

        except Exception as e:
            print(f"❌ Lỗi khi đánh dấu email verification: {str(e)}")

    def mark_card_as_processed(self, card_info, status="ok"):
        """
        Đánh dấu thẻ đã được xử lý trong file card.txt
        :param card_info: Thông tin thẻ (số thẻ, tháng, năm, cvv, cardholder_name)
        :param status: Trạng thái xử lý (ok, error, expired hoặc used)
        """
        try:
            # Kiểm tra thông tin thẻ
            if not card_info or len(card_info) < 4:
                print("❌ Thông tin thẻ không hợp lệ")
                return

            # Lấy số thẻ (phần tử đầu tiên của card_info)
            card_number = card_info[0]

            # Đường dẫn đến file card.txt
            try:
                # Thử sử dụng get_txt_path (phương pháp đơn giản nhất)
                card_file_path = path_manager.get_txt_path("card.txt")
                print(f"🔍 Đường dẫn file card.txt từ get_txt_path: {card_file_path}")
            except Exception as e:
                print(f"⚠️ Lỗi khi sử dụng get_txt_path: {str(e)}")
                # Sử dụng get_data_path như phương án dự phòng
                card_file_path = path_manager.get_data_path("card.txt")
                print(f"🔍 Đường dẫn file card.txt từ get_data_path: {card_file_path}")

            # Kiểm tra xem file có tồn tại không
            if not os.path.exists(card_file_path):
                print(f"❌ Không tìm thấy file {card_file_path}")
                return

            # Đọc danh sách thẻ từ file
            with open(card_file_path, "r") as file:
                lines = file.readlines()

            # Tìm và đánh dấu thẻ đã được xử lý
            card_marked = False
            for i, line in enumerate(lines):
                line = line.strip()
                # Bỏ qua dòng trống và dòng comment
                if not line or line.startswith("#"):
                    continue

                # Kiểm tra xem dòng có chứa thông tin thẻ không
                # Sử dụng phương pháp tìm kiếm chính xác hơn
                parts = line.split("|")
                if len(parts) >= 1 and parts[0] == card_number:
                    # In thông tin debug
                    print(f"🔍 Tìm thấy thẻ {card_number[-4:]} trong file card.txt (dòng {i+1}): '{line}'")

                    # Kiểm tra xem thẻ đã được đánh dấu chưa (kiểm tra hậu tố)
                    if len(parts) >= 4:
                        # Phần cuối cùng có thể chứa thông tin trạng thái
                        last_part = parts[-1]

                        # Kiểm tra xem phần cuối có chứa các hậu tố không
                        if "-ok" in last_part or "-error" in last_part or "-expired" in last_part or "-used" in last_part:
                            # Thẻ đã được đánh dấu, cập nhật trạng thái mới
                            # Lấy 4 phần đầu tiên (số thẻ, tháng, năm, cvv)
                            base_info = "|".join(parts[:4])

                            # Nếu có tên chủ thẻ (phần thứ 5) và không chứa hậu tố, thêm vào
                            if len(parts) >= 5:
                                cardholder_part = parts[4]
                                if "-ok" not in cardholder_part and "-error" not in cardholder_part and "-expired" not in cardholder_part and "-used" not in cardholder_part:
                                    base_info += "|" + cardholder_part

                            print(f"🔍 Đã xử lý thẻ với định dạng phức tạp: {line}")
                            print(f"🔍 Thông tin cơ bản: {base_info}")
                        else:
                            # Nếu phần cuối không chứa hậu tố, sử dụng toàn bộ thông tin
                            base_info = line
                            print(f"🔍 Thẻ chưa được đánh dấu, sử dụng thông tin gốc: {base_info}")
                    else:
                        # Nếu không đủ 4 phần, sử dụng cách cũ
                        last_dash_index = line.find("-ok")
                        if last_dash_index < 0:
                            last_dash_index = line.find("-error")
                        if last_dash_index < 0:
                            last_dash_index = line.find("-expired")
                        if last_dash_index < 0:
                            last_dash_index = line.find("-used")

                        if last_dash_index > 0:
                            base_info = line[:last_dash_index]
                        else:
                            base_info = line

                        print(f"🔍 Đã xử lý thẻ với định dạng đơn giản: {line}")
                        print(f"🔍 Thông tin cơ bản: {base_info}")

                    # Thêm hậu tố email nếu trạng thái là ok
                    if status == "ok" and hasattr(self, 'email') and self.email:
                        # Lấy tên người dùng từ email (phần trước @)
                        email_username = self.email.split('@')[0] if '@' in self.email else self.email
                        lines[i] = f"{base_info}-{status}-{email_username}\n"
                        print(f"🔄 Cập nhật trạng thái thẻ {card_number[-4:]} từ '{line}' thành '{base_info}-{status}-{email_username}'")
                    else:
                        lines[i] = f"{base_info}-{status}\n"
                        print(f"🔄 Cập nhật trạng thái thẻ {card_number[-4:]} từ '{line}' thành '{base_info}-{status}'")

                    card_marked = True
                    break

            # Ghi lại danh sách thẻ vào file
            if card_marked:
                with open(card_file_path, "w") as file:
                    file.writelines(lines)

                # Hiển thị thông báo phù hợp dựa trên trạng thái
                if status == "ok" and hasattr(self, 'email') and self.email:
                    email_username = self.email.split('@')[0] if '@' in self.email else self.email
                    print(f"✅ Đã đánh dấu thẻ {card_number[-4:]} là đã xử lý thành công với email {email_username}")
                else:
                    print(f"✅ Đã đánh dấu thẻ {card_number[-4:]} là đã xử lý với trạng thái {status}")

                # Ghi log về việc cập nhật thẻ
                if status == "ok":
                    print(f"📊 Đã đánh dấu thẻ {card_number[-4:]} là thành công (ok)")
                else:
                    print(f"ℹ️ Đã đánh dấu thẻ {card_number[-4:]} với trạng thái: {status}")

                # Cập nhật thống kê thẻ ngay lập tức
                try:
                    # Tìm cửa sổ chính của ứng dụng
                    from PyQt5.QtWidgets import QApplication
                    print(f"📊 Đang cập nhật thống kê thẻ sau khi đánh dấu thẻ {card_number[-4:]} là {status}...")

                    # Đợi một chút để đảm bảo file đã được ghi hoàn tất
                    time.sleep(0.5)

                    # Tìm cửa sổ chính và cập nhật thống kê thẻ
                    for widget in QApplication.topLevelWidgets():
                        if hasattr(widget, 'update_card_stats'):
                            success = widget.update_card_stats()
                            if success:
                                print(f"✅ Đã cập nhật thống kê thẻ thành công sau khi đánh dấu thẻ {card_number[-4:]}")
                            else:
                                print(f"⚠️ Cập nhật thống kê thẻ không thành công sau khi đánh dấu thẻ {card_number[-4:]}")
                            break
                except Exception as e:
                    print(f"❌ Lỗi khi cập nhật thống kê thẻ: {str(e)}")
            else:
                print(f"❌ Không tìm thấy thẻ {card_number[-4:]} trong file {card_file_path} hoặc không thể đánh dấu thẻ")

        except Exception as e:
            print(f"❌ Lỗi khi đánh dấu thẻ đã xử lý: {str(e)}")

    def press_key(self, keycode):
        """
        Gửi phím keyevent đến thiết bị.
        :param keycode: Mã phím (keycode) cần gửi
        :return: True nếu thành công, False nếu thất bại
        """
        try:
            print(f"Gửi phím keyevent {keycode} đến thiết bị {self.serial}")
            # Sử dụng biến toàn cục ADB_PATH thay vì "adb" cứng
            subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "keyevent", str(keycode)], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
            print(f"✅ Đã gửi phím keyevent {keycode}")
            return True
        except Exception as e:
            print(f"❌ Lỗi khi gửi phím keyevent {keycode}: {str(e)}")
            return False

    def swipe_up_to_close_app(self):
        """
        Vuốt lên để đóng ứng dụng trong màn hình đa nhiệm
        :return: True nếu thành công, False nếu thất bại
        """
        try:
            # Lấy kích thước màn hình
            screen_width = 1080
            screen_height = 1920

            # Tọa độ vuốt: từ giữa màn hình lên trên
            start_x = screen_width // 2
            start_y = screen_height // 2
            end_x = screen_width // 2
            end_y = screen_height // 4

            # Thực hiện vuốt
            subprocess.run([
                ADB_PATH, "-s", self.serial, "shell", "input", "swipe",
                str(start_x), str(start_y), str(end_x), str(end_y), "300"
            ], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

            return True
        except Exception as e:
            print(f"❌ Lỗi khi vuốt lên để đóng ứng dụng: {str(e)}")
            return False

    def exit_app_completely(self, check_stop_flag=None):
        """
        Mở đa nhiệm (key 187) và vuốt lên 2 lần để thoát hoàn toàn ứng dụng khi hoàn thành thêm thẻ
        :param check_stop_flag: Hàm callback để kiểm tra cờ dừng
        :return: True nếu thành công, False nếu thất bại
        """
        try:
            print("🎯 Bắt đầu thoát hoàn toàn ứng dụng sau khi hoàn thành thêm thẻ")

            # Bước 1: Mở đa nhiệm (recent apps) bằng phím KEYCODE_APP_SWITCH
            print("📱 Mở đa nhiệm (recent apps)")
            if not self.press_key(187):  # KEYCODE_APP_SWITCH = 187
                print("❌ Không thể mở đa nhiệm")
                return False

            # Đợi đa nhiệm hiển thị
            if self._random_sleep(1.5, 2.0, "Chờ đa nhiệm hiển thị", check_stop_flag):
                return False

            # Bước 2: Vuốt lên 2 lần để thoát ứng dụng
            print("🔄 Vuốt lên 2 lần để thoát hoàn toàn ứng dụng")
            for swipe_count in range(2):
                print(f"Vuốt lên lần {swipe_count + 1}/2")
                if not self.swipe_up_to_close_app():
                    print(f"❌ Không thể vuốt lên lần {swipe_count + 1}")
                    return False

                if self._random_sleep(0.8, 1.2, f"Chờ sau vuốt lên lần {swipe_count + 1}", check_stop_flag):
                    return False

            # Bước 3: Về màn hình chính
            print("🏠 Về màn hình chính sau khi thoát ứng dụng")
            self.press_key(3)  # KEYCODE_HOME

            if self._random_sleep(1.0, 1.5, "Chờ về màn hình chính", check_stop_flag):
                return False

            print("✅ Đã thoát hoàn toàn ứng dụng thành công")
            return True

        except Exception as e:
            print(f"❌ Lỗi khi thoát hoàn toàn ứng dụng: {str(e)}")
            return False

    def _random_sleep(self, min_sec=0.5, max_sec=1.0, action_desc="", check_stop_flag=None):
        """
        Tạm dừng thực thi trong khoảng thời gian ngẫu nhiên.
        :param min_sec: Thời gian tối thiểu (giây)
        :param max_sec: Thời gian tối đa (giây)
        :param action_desc: Mô tả hành động đang thực hiện
        :param check_stop_flag: Hàm callback để kiểm tra cờ dừng
        :return: True nếu đã dừng do cờ dừng, False nếu đã ngủ hết thời gian
        """
        # Điều chỉnh thời gian chờ để cân bằng giữa tốc độ và độ chính xác
        t = random.uniform(min_sec, max_sec)
        print(f"⏳ {action_desc}... (chờ {t:.2f}s)")

        # Nếu có hàm kiểm tra cờ dừng, kiểm tra mỗi 0.05 giây
        if check_stop_flag and callable(check_stop_flag):
            start_time = time.time()
            end_time = start_time + t

            while time.time() < end_time:
                # Kiểm tra cờ dừng
                if check_stop_flag():
                    print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi đang chờ")
                    # Thông báo thêm để dễ theo dõi
                    print(f"⚠️ Dừng quá trình ngủ tại {action_desc if action_desc else 'không có thông báo'}")
                    return True  # Đã dừng do cờ dừng

                # Ngủ một khoảng thời gian ngắn hơn để phản ứng nhanh hơn với cờ dừng
                time.sleep(0.05)

            return False  # Đã ngủ hết thời gian
        else:
            # Nếu không có hàm kiểm tra cờ dừng, ngủ bình thường
            time.sleep(t)
            return False

    def find_image_and_tap(self, template_path, threshold=0.8, check_stop_flag=None, scale_factor=0.5):
        """
        Tìm vị trí ảnh trên màn hình và tap vào đó với xử lý ảnh nhẹ hơn.
        :param template_path: Đường dẫn đến file ảnh template
        :param threshold: Ngưỡng độ khớp (0.0-1.0)
        :param check_stop_flag: Hàm callback để kiểm tra cờ dừng
        :param scale_factor: Hệ số thu nhỏ ảnh để tăng tốc xử lý (0.0-1.0)
        :return: True nếu tìm thấy và tap thành công, False nếu không tìm thấy
        """
        # Hàm kiểm tra cờ dừng
        def should_stop():
            if check_stop_flag and callable(check_stop_flag):
                stop_flag = check_stop_flag()
                if stop_flag:
                    print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi tìm {os.path.basename(template_path)}")
                return stop_flag
            return False

        try:
            print(f"Tìm và tap vào ảnh: {os.path.basename(template_path)}")

            # Kiểm tra cờ dừng và file template một lần duy nhất trước khi bắt đầu
            if should_stop() or not os.path.exists(template_path):
                if not os.path.exists(template_path):
                    print(f"❌ Không tìm thấy file template: {template_path}")
                return False

            # Lấy đường dẫn đến thư mục screen
            screen_dir = path_manager.get_screen_path()

            # Chụp màn hình thiết bị trực tiếp vào bộ nhớ
            screenshot_path = os.path.join(screen_dir, f"screen_{self.serial}.png")
            try:
                # Sử dụng pipe để tránh ghi file tạm
                process = subprocess.Popen(
                    [ADB_PATH, "-s", self.serial, "exec-out", "screencap", "-p"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
                )
                screenshot_data, _ = process.communicate()

                # Chuyển đổi dữ liệu ảnh thành mảng numpy
                screenshot_array = np.frombuffer(screenshot_data, dtype=np.uint8)
                screenshot = cv2.imdecode(screenshot_array, cv2.IMREAD_COLOR)

                # Lưu ảnh để debug nếu cần
                cv2.imwrite(screenshot_path, screenshot)
            except Exception as e:
                print(f"❌ Lỗi khi chụp màn hình: {str(e)}")
                return False

            if should_stop():
                return False

            # Đọc ảnh template
            template = cv2.imread(template_path)

            if template is None or screenshot is None:
                print(f"❌ Không thể đọc ảnh template hoặc ảnh màn hình")
                return False

            # Thu nhỏ ảnh để tăng tốc xử lý
            if scale_factor < 1.0:
                new_width = int(screenshot.shape[1] * scale_factor)
                new_height = int(screenshot.shape[0] * scale_factor)
                screenshot_resized = cv2.resize(screenshot, (new_width, new_height), interpolation=cv2.INTER_AREA)

                new_width = int(template.shape[1] * scale_factor)
                new_height = int(template.shape[0] * scale_factor)
                template_resized = cv2.resize(template, (new_width, new_height), interpolation=cv2.INTER_AREA)
            else:
                screenshot_resized = screenshot
                template_resized = template

            # Chuyển đổi ảnh sang thang độ xám để giảm kích thước dữ liệu
            template_gray = cv2.cvtColor(template_resized, cv2.COLOR_BGR2GRAY)
            screenshot_gray = cv2.cvtColor(screenshot_resized, cv2.COLOR_BGR2GRAY)

            # Tìm kiếm template trong ảnh màn hình với phương pháp hiệu quả
            result = cv2.matchTemplate(screenshot_gray, template_gray, cv2.TM_CCOEFF_NORMED)
            _, max_val, _, max_loc = cv2.minMaxLoc(result)

            # Nếu độ khớp lớn hơn ngưỡng, tap vào vị trí tìm thấy
            if max_val >= threshold:
                # Tính toán tọa độ trung tâm của template trên ảnh gốc
                h, w = template_gray.shape
                center_x = int(max_loc[0] / scale_factor + w / scale_factor / 2)
                center_y = int(max_loc[1] / scale_factor + h / scale_factor / 2)

                print(f"✅ Đã tìm thấy ảnh với độ khớp {max_val:.2f} tại vị trí ({center_x}, {center_y})")

                if should_stop():
                    return False

                # Tap vào vị trí tìm thấy
                subprocess.run(
                    [ADB_PATH, "-s", self.serial, "shell", "input", "tap", str(center_x), str(center_y)],
                    check=True,
                    creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
                )
                print(f"✅ Đã tap vào vị trí ({center_x}, {center_y})")

                # Đợi một chút sau khi tap
                if self._random_sleep(1.0, 2.0, "Chờ sau khi tap", check_stop_flag):
                    return False

                return True
            else:
                print(f"❌ Không tìm thấy ảnh với độ khớp đủ cao (max_val = {max_val:.2f})")
                return False

        except Exception as e:
            print(f"❌ Lỗi khi tìm và tap vào ảnh: {str(e)}")
            return False

    def find_image_on_screen(self, template_path, threshold=0.8, roi=None, check_stop_flag=None):
        """
        Tìm hình ảnh trên màn hình nhưng không tap vào.
        :param template_path: Đường dẫn đến file template.
        :param threshold: Ngưỡng độ tương đồng (0-1).
        :param roi: Region of Interest - Vùng quan tâm [x, y, width, height]. Nếu None, tìm trên toàn bộ màn hình.
        :param check_stop_flag: Hàm callback để kiểm tra cờ dừng.
        :return: (True, location) nếu tìm thấy, (False, None) nếu không.
        """
        try:
            # Kiểm tra cờ dừng nếu có
            if check_stop_flag and callable(check_stop_flag):
                if check_stop_flag():
                    print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi tìm {os.path.basename(template_path)}")
                    return False, None

            # Chụp màn hình thiết bị trực tiếp vào bộ nhớ
            try:
                process = subprocess.Popen(
                    [ADB_PATH, "-s", self.serial, "exec-out", "screencap", "-p"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
                )
                screenshot_data, _ = process.communicate()

                # Chuyển đổi dữ liệu ảnh thành mảng numpy
                screenshot_array = np.frombuffer(screenshot_data, dtype=np.uint8)
                screenshot = cv2.imdecode(screenshot_array, cv2.IMREAD_COLOR)
            except Exception as e:
                print(f"❌ Lỗi khi chụp màn hình: {str(e)}")
                return False, None

            # Kiểm tra cờ dừng sau khi chụp ảnh
            if check_stop_flag and callable(check_stop_flag):
                if check_stop_flag():
                    print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng sau khi chụp ảnh màn hình")
                    return False, None

            # Đọc ảnh template
            template = cv2.imread(template_path)

            if template is None:
                print(f"❌ Không thể đọc ảnh template: {template_path}")
                return False, None

            if screenshot is None:
                print(f"❌ Không thể đọc ảnh màn hình")
                return False, None

            # Chuyển đổi ảnh sang thang độ xám
            template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
            screenshot_gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)

            # Lấy kích thước template
            template_h, template_w = template_gray.shape

            # Nếu có ROI, cắt ảnh màn hình theo ROI
            if roi:
                x, y, w, h = roi
                screenshot_gray = screenshot_gray[y:y+h, x:x+w]
                roi_offset_x, roi_offset_y = x, y
            else:
                roi_offset_x, roi_offset_y = 0, 0

            # Tìm kiếm template trong ảnh màn hình
            result = cv2.matchTemplate(screenshot_gray, template_gray, cv2.TM_CCOEFF_NORMED)
            _, max_val, _, max_loc = cv2.minMaxLoc(result)

            # Nếu độ khớp lớn hơn ngưỡng, trả về True và vị trí
            if max_val >= threshold:
                # Tính toán tọa độ trung tâm của template trên màn hình
                center_x = roi_offset_x + max_loc[0] + template_w // 2
                center_y = roi_offset_y + max_loc[1] + template_h // 2

                print(f"✅ Đã tìm thấy {os.path.basename(template_path)} tại ({center_x}, {center_y}) (độ khớp: {max_val:.2f})")
                return True, (center_x, center_y)
            else:
                print(f"❌ Không tìm thấy {os.path.basename(template_path)} (max_val = {max_val:.2f})")
                return False, None

        except Exception as e:
            print(f"❌ Lỗi khi tìm {os.path.basename(template_path)}: {str(e)}")
            return False, None

    def execute_actions(self, check_stop_flag=None):
        """
        Thực hiện chuỗi hành động đơn giản: về màn hình chính, khóa hướng màn hình, mở Google Ads,
        tìm và tap vào fix_it.png, lặp lại theo số lần đã chỉ định.
        :param check_stop_flag: Hàm callback để kiểm tra cờ dừng từ MainForm.py
        :return: True nếu thành công, False nếu thất bại
        """
        try:
            print(f"Bắt đầu thực hiện chuỗi hành động trên thiết bị {self.serial} với {self.num_loops} vòng lặp")

            # Hàm kiểm tra cờ dừng
            def should_stop():
                if check_stop_flag and callable(check_stop_flag):
                    # Kiểm tra cờ dừng
                    stop_flag = check_stop_flag()
                    if stop_flag:
                        print("⚠️ Đã nhận tín hiệu dừng từ người dùng")
                    return stop_flag
                return False

            # Kiểm tra cờ dừng trước khi bắt đầu
            if should_stop():
                print("⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi bắt đầu thực hiện các bước")
                return False

            # Lấy đường dẫn đến thư mục template
            template_dir = path_manager.get_template_path()

            # Hiển thị thông báo nếu thư mục template vừa được tạo
            if not os.path.exists(os.path.join(template_dir, "fix_it.png")):
                print(f"⚠️ Vui lòng đặt các file template sau vào thư mục {template_dir}:")
                print(f"  - fix_it.png: Nút Fix It trong Google Ads")
                print(f"  - add_payment_ads.png: Nút Add Payment trong Google Ads")
                print(f"  - add_gredit.png: Nút Add Credit/Debit Card trong Google Ads")
                print(f"  - card_number.png: Trường nhập số thẻ trong Google Ads")
                print(f"  - save_card.png: Nút Save Card trong Google Ads")
                print(f"  - usk.png: Nút Usk trong Google Ads (tùy chọn)")
                print(f"  - unti.png: Nút Unti trong Google Ads (tùy chọn)")
                print(f"  - dau_x.png: Dấu X trong Google Ads (tùy chọn)")

            # Đường dẫn đến các file template
            unti_template_path = path_manager.get_template_path("unti.png")
            usk_template_path = path_manager.get_template_path("usk.png")
            dau_x_template_path = path_manager.get_template_path("dau_x.png")
            fix_it_template_path = path_manager.get_template_path("fix_it.png")
            add_payment_ads_template_path = path_manager.get_template_path("add_payment_ads.png")
            add_gredit_template_path = path_manager.get_template_path("add_gredit.png")
            card_number_template_path = path_manager.get_template_path("card_number.png")
            save_card_template_path = path_manager.get_template_path("save_card.png")

            # Kiểm tra xem các file template có tồn tại không
            missing_templates = []
            if not os.path.exists(fix_it_template_path):
                missing_templates.append("fix_it.png")
            if not os.path.exists(add_payment_ads_template_path):
                missing_templates.append("add_payment_ads.png")
            if not os.path.exists(add_gredit_template_path):
                missing_templates.append("add_gredit.png")
            if not os.path.exists(card_number_template_path):
                missing_templates.append("card_number.png")
            if not os.path.exists(save_card_template_path):
                missing_templates.append("save_card.png")

            # Kiểm tra các file template tùy chọn
            optional_templates = []
            if not os.path.exists(unti_template_path):
                optional_templates.append("unti.png")
            if not os.path.exists(dau_x_template_path):
                optional_templates.append("dau_x.png")

            if missing_templates:
                print(f"⚠️ Không tìm thấy các file template sau: {', '.join(missing_templates)}")
                print(f"⚠️ Vui lòng đặt các file template vào thư mục {template_dir}")

            # Bước 1: Về màn hình chính bằng cách nhấn nút Home
            print("Bước 1: Về màn hình chính bằng cách nhấn nút Home")
            subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "keyevent", "3"], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)  # KEYCODE_HOME = 3
            print("✅ Đã về màn hình chính")

            # Đợi một chút sau khi về màn hình chính
            self._random_sleep(1.0, 2.0, "Chờ sau khi về màn hình chính")

            # Bước 1.5: Khóa hướng màn hình ở chế độ dọc
            print("Bước 1.5: Khóa hướng màn hình ở chế độ dọc")
            try:
                # Tắt tự động xoay màn hình
                subprocess.run([ADB_PATH, "-s", self.serial, "shell", "settings", "put", "system", "accelerometer_rotation", "0"], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                # Đặt hướng màn hình là dọc (0 = dọc, 1 = ngang)
                subprocess.run([ADB_PATH, "-s", self.serial, "shell", "settings", "put", "system", "user_rotation", "0"], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                print("✅ Đã khóa hướng màn hình ở chế độ dọc")
            except Exception as e:
                print(f"⚠️ Không thể khóa hướng màn hình: {str(e)}")

            # Bước 2: Mở Google Ads bằng cách tìm biểu tượng ads.png trong menu
            print("Bước 2: Mở Google Ads bằng cách tìm biểu tượng ads.png trong menu")

            # Đường dẫn đến file template ads.png
            ads_template_path = os.path.join(template_dir, "ads.png")

            # Kiểm tra xem file template ads.png có tồn tại không
            if not os.path.exists(ads_template_path):
                print(f"⚠️ Không tìm thấy file template ads.png tại {ads_template_path}")
                print(f"⚠️ Vui lòng đặt file ads.png vào thư mục {template_dir}")
                print("❌ Không thể tiếp tục vì không tìm thấy file ads.png")
                return False

            # Tìm biểu tượng ads.png trong menu
            print("Tìm biểu tượng ads.png trong menu")
            ads_found = False

            # Vuốt lên 1 lần trước khi bắt đầu tìm để hiển thị menu ứng dụng
            print("Vuốt lên 1 lần để hiển thị menu ứng dụng")
            # Lấy kích thước màn hình
            screen_height = 1920  # Chiều cao màn hình mặc định
            screen_width = 1080   # Chiều rộng màn hình mặc định

            # Vuốt từ dưới lên trên
            subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "swipe",
                          str(screen_width // 2), str(screen_height * 4 // 5),
                          str(screen_width // 2), str(screen_height // 5), "300"], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
            print("✅ Đã vuốt lên để hiển thị menu ứng dụng")

            # Đợi một chút sau khi vuốt
            self._random_sleep(1.0, 2.0, "Chờ sau khi vuốt lên để hiển thị menu ứng dụng")

            # Thử tìm ads.png tối đa 5 lần với vuốt lên giữa các lần thử
            for attempt in range(5):
                print(f"Lần thử {attempt + 1}/5 tìm ads.png")
                if self.find_image_and_tap(ads_template_path, check_stop_flag=check_stop_flag):
                    ads_found = True
                    print("✅ Đã tìm thấy và tap vào ads.png")
                    break
                else:
                    print(f"❌ Không tìm thấy ads.png trong lần thử {attempt + 1}")

                    # Nếu không tìm thấy và chưa phải lần thử cuối cùng, vuốt lên và thử lại
                    if attempt < 4:
                        print("Vuốt lên để tìm tiếp")

                        # Vuốt từ dưới lên trên
                        subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "swipe",
                                      str(screen_width // 2), str(screen_height * 4 // 5),
                                      str(screen_width // 2), str(screen_height // 5), "300"], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                        print(f"✅ Đã vuốt lên lần {attempt + 1}")

                        # Đợi một chút sau khi vuốt
                        if self._random_sleep(1.0, 2.0, f"Chờ sau khi vuốt lên lần {attempt + 1}", check_stop_flag):
                            print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi vuốt lên lần {attempt + 1}")
                            return False

            # Nếu không tìm thấy ads.png sau tất cả các lần thử, thông báo lỗi và dừng
            if not ads_found:
                print("❌ Không thể tìm thấy ads.png sau 5 lần thử")
                print("❌ Không thể tiếp tục vì không tìm thấy biểu tượng Google Ads")
                return False

            # Đợi ứng dụng khởi động
            if self._random_sleep(6.0, 8.0, "Chờ Google Ads khởi động hoàn toàn", check_stop_flag):
                print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ Google Ads khởi động")
                return False

            # Bước 2.5: Tìm và tap vào nút next_ads (nếu xuất hiện)
            print("Bước 2.5: Tìm và tap vào nút next_ads (nếu xuất hiện)")
            next_ads_template_path = os.path.join(template_dir, "next_ads.png")
            next_ads_found = False

            # Kiểm tra cờ dừng trước khi tìm next_ads
            if should_stop():
                print("⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi tìm next_ads")
                return False

            # Kiểm tra xem file template next_ads.png có tồn tại không
            if os.path.exists(next_ads_template_path):
                print(f"🔍 Tìm kiếm nút next_ads (next_ads.png) trên màn hình...")
                # Thử tìm next_ads.png tối đa 3 lần
                for attempt in range(3):
                    # Kiểm tra cờ dừng trước mỗi lần thử
                    if should_stop():
                        print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong lần thử {attempt + 1}/3 tìm next_ads")
                        return False

                    print(f"Lần thử {attempt + 1}/3 tìm next_ads.png")
                    if self.find_image_and_tap(next_ads_template_path, check_stop_flag=check_stop_flag):
                        next_ads_found = True
                        print("✅ Đã tìm thấy và tap vào next_ads.png")
                        break
                    else:
                        print(f"❌ Không tìm thấy next_ads.png trong lần thử {attempt + 1}")
                        # Đợi một chút trước khi thử lại
                        if attempt < 2:  # Chỉ đợi nếu không phải lần thử cuối cùng
                            if self._random_sleep(1.0, 2.0, f"Chờ trước khi thử lại lần {attempt + 2}", check_stop_flag):
                                print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ trước khi thử lại tìm next_ads")
                                return False

                # Nếu đã tìm thấy và tap vào next_ads, đợi một chút rồi nhập mật khẩu
                if next_ads_found:
                    print("✅ Đã tìm thấy và tap vào next_ads.png")
                    if self._random_sleep(1.0, 3.0, "Chờ sau khi tap vào next_ads", check_stop_flag):
                        print("⚠️ Đã nhận tín hiệu dừng từ người dùng sau khi tap vào next_ads")
                        return False

                    # Bước 2.6: Nhập mật khẩu sau khi tap vào next_ads
                    print("Bước 2.6: Nhập mật khẩu sau khi tap vào next_ads")

                    # Kiểm tra cờ dừng trước khi nhập mật khẩu
                    if should_stop():
                        print("⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi nhập mật khẩu")
                        return False

                    # Kiểm tra xem có mật khẩu không
                    if self.password:
                        print(f"🔑 Nhập mật khẩu: {self.password[:2]}{'*' * (len(self.password) - 2)}")

                        # Clear ô nhập bằng cách xóa text cũ (giống chplay.py)
                        print("🧹 Đang clear ô nhập password...")
                        # Nhấn Backspace nhiều lần để xóa text cũ
                        for i in range(50):  # Xóa tối đa 50 ký tự
                            # Kiểm tra cờ dừng
                            if should_stop():
                                print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi clear password")
                                return False
                            subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "keyevent", "67"], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)  # KEYCODE_DEL = 67

                        # Nhập mật khẩu mới sau khi clear (sử dụng input text trực tiếp)
                        print("🔤 Đang nhập mật khẩu mới sau khi clear...")
                        try:
                            # Kiểm tra cờ dừng trước khi nhập
                            if should_stop():
                                print("⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi nhập password")
                                return False

                            # Nhập toàn bộ password một lần (giống chplay.py)
                            subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "text", self.password],
                                         check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                            print("✅ Đã nhập mật khẩu thành công")

                            # Đợi một chút sau khi nhập
                            time.sleep(1.0)

                        except Exception as e:
                            print(f"❌ Lỗi khi nhập password: {e}")
                            return False

                        # Đợi một chút sau khi nhập mật khẩu
                        if self._random_sleep(2.0, 3.0, "Chờ sau khi nhập mật khẩu", check_stop_flag):
                            print("⚠️ Đã nhận tín hiệu dừng từ người dùng sau khi nhập mật khẩu")
                            return False

                        # Nhấn Enter để xác nhận
                        print("Nhấn Enter để xác nhận mật khẩu")
                        self.press_key(66)  # KEYCODE_ENTER = 66

                        # Đợi lâu hơn sau khi nhấn Enter
                        if self._random_sleep(5.0, 7.0, "Chờ sau khi nhấn Enter để xác nhận mật khẩu", check_stop_flag):
                            print("⚠️ Đã nhận tín hiệu dừng từ người dùng sau khi nhấn Enter")
                            return False

                        # Bước 2.7: Kiểm tra xác thực 2FA
                        print("Bước 2.7: Kiểm tra xác thực 2FA (authen_ads.png)")

                        # Kiểm tra cờ dừng trước khi kiểm tra xác thực 2FA
                        if should_stop():
                            print("⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi kiểm tra xác thực 2FA")
                            return False

                        # Đường dẫn đến file template authen_ads.png
                        authen_ads_template_path = os.path.join(template_dir, "authen_ads.png")
                        authen_ads_found = False

                        # Kiểm tra xem file template authen_ads.png có tồn tại không
                        if os.path.exists(authen_ads_template_path):
                            print(f"🔍 Tìm kiếm nút xác thực 2FA (authen_ads.png) trên màn hình...")

                            # Thử tìm authen_ads.png tối đa 3 lần
                            for attempt in range(3):
                                # Kiểm tra cờ dừng trước mỗi lần thử
                                if should_stop():
                                    print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong lần thử {attempt + 1}/3 tìm authen_ads.png")
                                    return False

                                print(f"Lần thử {attempt + 1}/3 tìm authen_ads.png")
                                if self.find_image_and_tap(authen_ads_template_path, check_stop_flag=check_stop_flag):
                                    authen_ads_found = True
                                    print("✅ Đã tìm thấy và tap vào authen_ads.png")
                                    break
                                else:
                                    print(f"❌ Không tìm thấy authen_ads.png trong lần thử {attempt + 1}")
                                    # Đợi một chút trước khi thử lại
                                    if attempt < 2:  # Chỉ đợi nếu không phải lần thử cuối cùng
                                        if self._random_sleep(1.0, 2.0, f"Chờ trước khi thử lại lần {attempt + 2}", check_stop_flag):
                                            print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ trước khi thử lại tìm authen_ads.png")
                                            return False

                            # Nếu đã tìm thấy và tap vào authen_ads, đợi một chút rồi nhập mã xác thực 2FA
                            if authen_ads_found:
                                print("✅ Đã tìm thấy và tap vào authen_ads.png")
                                if self._random_sleep(1.0, 3.0, "Chờ sau khi tap vào authen_ads.png", check_stop_flag):
                                    print("⚠️ Đã nhận tín hiệu dừng từ người dùng sau khi tap vào authen_ads.png")
                                    return False

                                # Kiểm tra xem có chuỗi bí mật xác thực 2FA không
                                if hasattr(self, 'authentication_secret') and self.authentication_secret:
                                    print("🔄 Bước 2.7.1: Tạo mã OTP động mới từ chuỗi bí mật xác thực 2FA")
                                    # Tạo mã OTP động mới ngay trước khi nhập để đảm bảo mã còn hiệu lực
                                    try:
                                        # Xử lý chuỗi bí mật để đảm bảo định dạng đúng
                                        # Loại bỏ khoảng trắng và ký tự đặc biệt
                                        clean_secret = ''.join(c for c in self.authentication_secret if c.isalnum())

                                        # Đảm bảo chuỗi bí mật có độ dài chia hết cho 8 (yêu cầu của base32)
                                        while len(clean_secret) % 8 != 0:
                                            clean_secret += '='

                                        print(f"🔄 Đang tạo mã OTP từ chuỗi bí mật: {clean_secret[:4]}{'*' * (len(clean_secret) - 4)}")
                                        totp = pyotp.TOTP(clean_secret)
                                        current_otp = totp.now()
                                        print(f"🔑 Đã tạo mã OTP động mới thành công: {current_otp}")

                                        # Kiểm tra xem mã OTP có đúng định dạng không (thường là 6 chữ số)
                                        if not current_otp.isdigit() or len(current_otp) != 6:
                                            print(f"⚠️ Mã OTP không đúng định dạng: {current_otp}")
                                            print(f"⚠️ Sẽ sử dụng chuỗi bí mật làm mã xác thực cố định")
                                            current_otp = self.authentication_secret
                                    except Exception as e:
                                        print(f"❌ Lỗi khi tạo mã OTP động: {str(e)}")
                                        print(f"⚠️ Sẽ sử dụng chuỗi bí mật làm mã xác thực cố định")
                                        current_otp = self.authentication_secret

                                    print("🔄 Bước 2.7.2: Nhập mã OTP vào trường xác thực 2FA")
                                    print(f"🔑 Nhập mã xác thực 2FA: {current_otp}")

                                    # Xóa trường nhập liệu trước khi nhập mã xác thực 2FA mới
                                    for _ in range(10):  # Xóa 10 ký tự để đảm bảo trường trống
                                        # Kiểm tra cờ dừng trước mỗi lần xóa
                                        if should_stop():
                                            print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi xóa trường nhập liệu")
                                            return False

                                        subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "keyevent", "67"], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)  # KEYCODE_DEL = 67
                                        time.sleep(0.1)

                                    # Nhập từng ký tự của mã xác thực 2FA
                                    for char in current_otp:
                                        # Kiểm tra cờ dừng trước mỗi lần nhập ký tự
                                        if should_stop():
                                            print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi nhập mã xác thực 2FA")
                                            return False

                                        # Sử dụng lệnh input text cho từng ký tự
                                        subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "text", char], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                                        # Đợi lâu hơn giữa các ký tự
                                        time.sleep(0.5)
                                        print(f"  - Đã nhập ký tự: {char}")

                                    print("✅ Đã nhập mã xác thực 2FA thành công")

                                    # Đợi một chút sau khi nhập mã xác thực 2FA
                                    if self._random_sleep(2.0, 3.0, "Chờ sau khi nhập mã xác thực 2FA", check_stop_flag):
                                        print("⚠️ Đã nhận tín hiệu dừng từ người dùng sau khi nhập mã xác thực 2FA")
                                        return False

                                    # Nhấn Enter để xác nhận
                                    print("Nhấn Enter để xác nhận mã xác thực 2FA")
                                    self.press_key(66)  # KEYCODE_ENTER = 66

                                    # Đợi lâu hơn sau khi nhấn Enter
                                    if self._random_sleep(2.0, 4.0, "Chờ sau khi nhấn Enter để xác nhận mã xác thực 2FA", check_stop_flag):
                                        print("⚠️ Đã nhận tín hiệu dừng từ người dùng sau khi nhấn Enter để xác nhận mã xác thực 2FA")
                                        return False
                                else:
                                    print("⚠️ Không có mã xác thực 2FA được cung cấp, bỏ qua bước nhập mã xác thực 2FA")
                            else:
                                print("ℹ️ Không tìm thấy authen_ads.png, tiếp tục với bước tiếp theo")
                        else:
                            print(f"ℹ️ File template authen_ads.png không tồn tại tại {authen_ads_template_path}")
                            print(f"ℹ️ Vui lòng đặt file authen_ads.png vào thư mục {template_dir} nếu bạn muốn sử dụng tính năng này")
                            print("ℹ️ Bỏ qua bước tìm authen_ads.png, tiếp tục với bước tiếp theo")
                    else:
                        print("⚠️ Không có mật khẩu được cung cấp, bỏ qua bước nhập mật khẩu")
                else:
                    print("ℹ️ Không tìm thấy next_ads.png, tiếp tục với bước tiếp theo")
            else:
                print(f"ℹ️ File template next_ads.png không tồn tại tại {next_ads_template_path}")
                print(f"ℹ️ Vui lòng đặt file next_ads.png vào thư mục {template_dir} nếu bạn muốn sử dụng tính năng này")
                print("ℹ️ Bỏ qua bước tìm next_ads, tiếp tục với bước tiếp theo")


            # Thử tìm dau_x.png tối đa 2 lần
            if os.path.exists(dau_x_template_path):
                for attempt in range(2):
                    print(f"Lần thử {attempt + 1}/2 tìm dau_x.png")
                    if self.find_image_and_tap(dau_x_template_path, check_stop_flag=check_stop_flag):
                        print(f"✅ Đã tìm thấy và tap vào dau_x.png ")
                        # Đợi một chút sau khi tap vào dau_x.png
                        if self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào dau_x.png", check_stop_flag):
                            print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi tap vào dau_x.png")
                            return False
                    else:
                        print(f"❌ Không tìm thấy dau_x.png trong lần thử {attempt + 1}")
                        # Đợi một chút trước khi thử lại
                        self._random_sleep(1.0, 1.5, f"Chờ trước khi thử lại lần {attempt + 2}")
            else:
                print(f"ℹ️ File template dau_x.png không tồn tại, bỏ qua bước này")

             # Bước 3.0: Tìm và tap usk.png trước khi xử lý unti (nếu có)
            print(f"Bước 3.0: Tìm và tap usk.png trước khi xử lý unti")

            # Thử tìm usk.png tối đa 2 lần
            if os.path.exists(usk_template_path):
                usk_found = False
                for attempt in range(2):
                    print(f"Lần thử {attempt + 1}/2 tìm usk.png")
                    if self.find_image_and_tap(usk_template_path, check_stop_flag=check_stop_flag):
                        usk_found = True
                        print(f"✅ Đã tìm thấy và tap vào usk.png")
                        # Đợi một chút sau khi tap vào usk.png
                        if self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào usk.png", check_stop_flag):
                            print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi tap vào usk.png")
                            return False
                        break
                    else:
                        print(f"❌ Không tìm thấy usk.png trong lần thử {attempt + 1}")
                        # Đợi một chút trước khi thử lại
                        if attempt < 1:  # Chỉ đợi nếu không phải lần thử cuối cùng
                            self._random_sleep(1.0, 1.5, f"Chờ trước khi thử lại lần {attempt + 2}")

                if usk_found:
                    print("✅ Đã xử lý usk.png thành công")
                else:
                    print("ℹ️ Không tìm thấy usk.png - Tiếp tục quy trình")
            else:
                print(f"ℹ️ File template usk.png không tồn tại, bỏ qua bước này")

             # Bước 3.1: Tìm unti.png và chờ người dùng xử lý (nếu có)
            print(f"Bước 3.1: Tìm unti.png và chờ người dùng xử lý")

            # Thử tìm unti.png tối đa 2 lần
            if os.path.exists(unti_template_path):
                unti_detected = False
                for attempt in range(2):
                    print(f"Lần thử {attempt + 1}/2 tìm unti.png")

                    # Chỉ kiểm tra unti.png mà không tap vào
                    found, location = self.find_image_on_screen(unti_template_path, threshold=0.7, check_stop_flag=check_stop_flag)
                    if found:
                        unti_detected = True
                        print(f"✅ Phát hiện unti.png tại {location} - Chờ người dùng xử lý trong 3 phút")
                        break
                    else:
                        print(f"❌ Không tìm thấy unti.png trong lần thử {attempt + 1}")
                        # Đợi một chút trước khi thử lại
                        self._random_sleep(1.0, 1.5, f"Chờ trước khi thử lại lần {attempt + 2}")

                # Nếu phát hiện unti.png, chờ người dùng xử lý và tìm fix.png
                if unti_detected:
                    print("🔔 Phát hiện unti.png - Chờ người dùng xử lý trong 3 phút...")
                    print("⏰ Hệ thống sẽ tự động tiếp tục khi phát hiện fix.png xuất hiện")

                    # Chờ tối đa 3 phút (180 giây) và kiểm tra fix.png mỗi 3 giây
                    wait_start_time = time.time()
                    max_wait_time = 180  # 3 phút
                    check_interval = 3   # Kiểm tra mỗi 3 giây

                    while time.time() - wait_start_time < max_wait_time:
                        # Kiểm tra cờ dừng
                        if should_stop():
                            print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ xử lý unti.png")
                            return False

                        # Kiểm tra fix.png có xuất hiện không
                        fix_found, fix_location = self.find_image_on_screen(fix_it_template_path, threshold=0.7, check_stop_flag=check_stop_flag)
                        if fix_found:
                            elapsed_time = time.time() - wait_start_time
                            print(f"✅ Phát hiện fix.png sau {elapsed_time:.1f} giây - Tap vào và tiếp tục")

                            # Tap vào fix.png
                            if self.find_image_and_tap(fix_it_template_path, check_stop_flag=check_stop_flag):
                                print("✅ Đã tap vào fix.png thành công")
                                # Đợi một chút sau khi tap
                                if self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào fix.png", check_stop_flag):
                                    return False
                                break
                            else:
                                print("❌ Không thể tap vào fix.png")

                        # Hiển thị thời gian còn lại
                        remaining_time = max_wait_time - (time.time() - wait_start_time)
                        remaining_minutes = remaining_time // 60
                        remaining_seconds = remaining_time % 60
                        print(f"⏳ Chờ fix.png xuất hiện... Còn lại: {remaining_minutes:.0f}p{remaining_seconds:.0f}s")

                        # Đợi trước khi kiểm tra lại
                        time.sleep(check_interval)

                    # Kiểm tra xem có timeout không
                    total_wait_time = time.time() - wait_start_time
                    if total_wait_time >= max_wait_time:
                        print(f"⏰ Hết thời gian chờ (3 phút) - Tiếp tục quy trình")
                    else:
                        print(f"✅ Đã xử lý xong unti.png sau {total_wait_time:.1f} giây")
                else:
                    print("ℹ️ Không phát hiện unti.png - Tiếp tục quy trình bình thường")
            else:
                print(f"ℹ️ File template unti.png không tồn tại, bỏ qua bước này")



            # Bước 3.3: Tìm và tap vào fix_it.png
            print(f"Bước 3.3: Tìm và tap vào fix_it.png")
            fix_it_found = False

            # Thử tìm fix_it.png tối đa 3 lần
            for attempt in range(2):
                print(f"Lần thử {attempt + 1}/3 tìm fix_it.png")
                if os.path.exists(fix_it_template_path):
                    if self.find_image_and_tap(fix_it_template_path, check_stop_flag=check_stop_flag):
                        fix_it_found = True
                        print(f"✅ Đã tìm thấy và tap vào fix_it.png")
                        break
                    else:
                        print(f"❌ Không tìm thấy fix_it.png trong lần thử {attempt + 1}")
                        # Đợi một chút trước khi thử lại
                        self._random_sleep(1.0, 3.0, f"Chờ trước khi thử lại lần {attempt + 2}")
                else:
                    print(f"❌ Không tìm thấy file template fix_it.png tại {fix_it_template_path}")
                    break

            if not fix_it_found:
                print(f"⚠️ Không thể tìm thấy fix_it.png sau 3 lần thử, tiếp tục với bước tiếp theo")

            # Đợi một chút sau khi tap vào fix_it.png hoặc sau khi không tìm thấy
            if self._random_sleep(2.0, 3.0, f"Chờ sau khi xử lý fix_it.png", check_stop_flag):
                print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi xử lý fix_it.png")
                return False

            # ✅ BƯỚC QUAN TRỌNG: Kiểm tra đã vào được vòng lặp chưa
            print("🔍 === KIỂM TRA QUAN TRỌNG: Xác định đã vào được vòng lặp chưa ===")

            # Đường dẫn template
            add_payment_ads_template_path = os.path.join(template_dir, "add_payment_ads.png")
            visa_ads_template_path = os.path.join(template_dir, "visa_ads.png")

            # Kiểm tra add_payment_ads.png
            add_payment_found = False
            if os.path.exists(add_payment_ads_template_path):
                print("🔍 Kiểm tra add_payment_ads.png...")
                add_payment_found = self.find_image_on_screen(add_payment_ads_template_path, threshold=0.8)
                if add_payment_found:
                    print("✅ Tìm thấy add_payment_ads.png - Có thể vào vòng lặp")

            # Kiểm tra visa_ads.png nếu chưa tìm thấy add_payment_ads
            visa_ads_found = False
            if not add_payment_found and os.path.exists(visa_ads_template_path):
                print("🔍 Kiểm tra visa_ads.png...")
                visa_ads_found = self.find_image_on_screen(visa_ads_template_path, threshold=0.8)
                if visa_ads_found:
                    print("✅ Tìm thấy visa_ads.png - Có thể vào vòng lặp")

            # Quyết định có tiếp tục vào vòng lặp không
            if not add_payment_found and not visa_ads_found:
                print("❌ KHÔNG TÌM THẤY add_payment_ads.png HOẶC visa_ads.png")
                print("❌ Không thể add thẻ - Thoát màn hình chính")
                self.log_message("❌ Không thể add thẻ - Không tìm thấy điều kiện vào vòng lặp")
                return False
            else:
                print("✅ Đã xác nhận có thể vào vòng lặp - Tiếp tục thêm thẻ")

            # Khởi tạo biến đếm vòng lặp add thẻ
            self.current_loop = 0

            # Bắt đầu vòng lặp thêm thẻ
            for loop_index in range(self.num_loops):
                # Kiểm tra cờ dừng trước khi bắt đầu vòng lặp mới
                if should_stop():
                    print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi bắt đầu vòng lặp {loop_index + 1}/{self.num_loops}")
                    return False

                # Cập nhật biến current_loop ở đầu mỗi vòng lặp
                self.current_loop = loop_index + 1

                print(f"\n=== Vòng lặp {self.current_loop}/{self.num_loops} ===\n")

                # ✅ CẬP NHẬT THÔNG TIN THẺ NGAY Ở ĐẦU VÒNG LẶP (giống addwallet)
                # Lấy thông tin thẻ tiếp theo để hiển thị đúng trên UI (không xóa khỏi danh sách)
                next_card_info = self.peek_next_card()
                if next_card_info:
                    card_number, month, year, cvv = next_card_info
                    print(f"📋 Thẻ sẽ thêm: {card_number[-4:]} ({month}/{year})")
                    # Cập nhật current_card_info để hiển thị trên UI
                    self.current_card_info = {
                        'card_number': card_number,
                        'month': month,
                        'year': year,
                        'cvv': cvv
                    }
                else:
                    print("⚠️ Không có thẻ nào khả dụng để thêm")
                    break

                # Cập nhật trạng thái trong AutoK.py nếu có (với thông tin thẻ mới)
                try:
                    # Gọi hàm cập nhật trạng thái nếu có
                    if hasattr(self, 'update_status_func') and callable(self.update_status_func):
                        self.update_status_func()
                        # Gọi lại một lần nữa để đảm bảo UI được cập nhật
                        self.update_status_func()
                    else:
                        from PyQt5.QtWidgets import QApplication
                        QApplication.processEvents()
                        # Thêm một lần cập nhật nữa để đảm bảo UI được cập nhật
                        QApplication.processEvents()
                except:
                    pass

                # Bước 4: Tìm và tap vào add_payment_ads.png
                print(f"Bước 4: Tìm và tap vào add_payment_ads.png (vòng lặp {loop_index + 1})")
                add_payment_ads_template_path = os.path.join(template_dir, "add_payment_ads.png")
                add_payment_ads_found = False

                # Thử tìm add_payment_ads.png tối đa 3 lần
                for attempt in range(3):
                    print(f"Lần thử {attempt + 1}/3 tìm add_payment_ads.png")
                    if os.path.exists(add_payment_ads_template_path):
                        if self.find_image_and_tap(add_payment_ads_template_path, check_stop_flag=check_stop_flag):
                            add_payment_ads_found = True
                            print(f"✅ Đã tìm thấy và tap vào add_payment_ads.png (vòng lặp {loop_index + 1})")
                            break
                        else:
                            print(f"❌ Không tìm thấy add_payment_ads.png trong lần thử {attempt + 1}")
                            # Đợi một chút trước khi thử lại
                            self._random_sleep(1.0, 2.0, f"Chờ trước khi thử lại lần {attempt + 2}")
                    else:
                        print(f"❌ Không tìm thấy file template add_payment_ads.png tại {add_payment_ads_template_path}")
                        print(f"⚠️ Vui lòng đặt file add_payment_ads.png vào thư mục {template_dir}")
                        break

                if not add_payment_ads_found:
                    print(f"⚠️ Không thể tìm thấy add_payment_ads.png sau 3 lần thử, thử tìm visa_ads.png")

                    # Bước 4.1: Tìm và tap vào visa_ads.png nếu không tìm thấy add_payment_ads.png
                    print(f"Bước 4.1: Tìm và tap vào visa_ads.png (vòng lặp {loop_index + 1})")
                    visa_ads_template_path = os.path.join(template_dir, "visa_ads.png")
                    visa_ads_found_alt = False

                    # Thử tìm visa_ads.png tối đa 3 lần
                    for attempt in range(3):
                        print(f"Lần thử {attempt + 1}/3 tìm visa_ads.png")
                        if os.path.exists(visa_ads_template_path):
                            if self.find_image_and_tap(visa_ads_template_path, check_stop_flag=check_stop_flag):
                                visa_ads_found_alt = True
                                print(f"✅ Đã tìm thấy và tap vào visa_ads.png (vòng lặp {loop_index + 1})")
                                break
                            else:
                                print(f"❌ Không tìm thấy visa_ads.png trong lần thử {attempt + 1}")
                                # Đợi một chút trước khi thử lại
                                self._random_sleep(1.0, 2.0, f"Chờ trước khi thử lại lần {attempt + 2}")
                        else:
                            print(f"❌ Không tìm thấy file template visa_ads.png tại {visa_ads_template_path}")
                            print(f"⚠️ Vui lòng đặt file visa_ads.png vào thư mục {template_dir}")
                            break

                    if not visa_ads_found_alt:
                        print(f"⚠️ Không thể tìm thấy cả add_payment_ads.png và visa_ads.png, tiếp tục với vòng lặp tiếp theo")
                    else:
                        # Đợi một chút sau khi tap vào visa_ads.png
                        if self._random_sleep(3.0, 5.0, f"Chờ sau khi tap vào visa_ads.png (vòng lặp {loop_index + 1})", check_stop_flag):
                            print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi tap vào visa_ads.png")
                            return False

                # Đợi một chút sau khi tap vào add_payment_ads.png hoặc sau khi không tìm thấy
                if self._random_sleep(3.0, 5.0, f"Chờ sau khi xử lý add_payment_ads.png (vòng lặp {loop_index + 1})", check_stop_flag):
                    print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi xử lý add_payment_ads.png")
                    return False

                # Bước 5: Tìm và tap vào add_gredit.png
                print(f"Bước 5: Tìm và tap vào add_gredit.png (vòng lặp {loop_index + 1})")
                add_gredit_found = False

                # Thử tìm add_gredit.png tối đa 3 lần
                for attempt in range(3):
                    print(f"Lần thử {attempt + 1}/3 tìm add_gredit.png")
                    if os.path.exists(add_gredit_template_path):
                        if self.find_image_and_tap(add_gredit_template_path, check_stop_flag=check_stop_flag):
                            add_gredit_found = True
                            print(f"✅ Đã tìm thấy và tap vào add_gredit.png (vòng lặp {loop_index + 1})")
                            break
                        else:
                            print(f"❌ Không tìm thấy add_gredit.png trong lần thử {attempt + 1}")
                            # Đợi một chút trước khi thử lại
                            self._random_sleep(1.0, 2.0, f"Chờ trước khi thử lại lần {attempt + 2}")
                    else:
                        print(f"❌ Không tìm thấy file template add_gredit.png tại {add_gredit_template_path}")
                        print(f"⚠️ Vui lòng đặt file add_gredit.png vào thư mục {template_dir}")
                        break

                if not add_gredit_found:
                    print(f"⚠️ Không thể tìm thấy add_gredit.png sau 3 lần thử, tiếp tục với vòng lặp tiếp theo")
                    continue  # Bỏ qua các bước còn lại trong vòng lặp này

                # Đợi một chút sau khi tap vào add_gredit.png
                if self._random_sleep(1.0, 3.0, f"Chờ sau khi xử lý add_gredit.png (vòng lặp {loop_index + 1})", check_stop_flag):
                    print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi xử lý add_gredit.png")
                    return False

                # Bước 6: Tìm và tap vào card_number.png
                print(f"Bước 6: Tìm và tap vào card_number.png (vòng lặp {loop_index + 1})")
                card_number_found = False

                # Thử tìm card_number.png tối đa 3 lần
                for attempt in range(3):
                    print(f"Lần thử {attempt + 1}/3 tìm card_number.png")
                    if os.path.exists(card_number_template_path):
                        if self.find_image_and_tap(card_number_template_path, check_stop_flag=check_stop_flag):
                            card_number_found = True
                            print(f"✅ Đã tìm thấy và tap vào card_number.png (vòng lặp {loop_index + 1})")
                            break
                        else:
                            print(f"❌ Không tìm thấy card_number.png trong lần thử {attempt + 1}")
                            # Đợi một chút trước khi thử lại
                            self._random_sleep(2.0, 3.0, f"Chờ trước khi thử lại lần {attempt + 2}")
                    else:
                        print(f"❌ Không tìm thấy file template card_number.png tại {card_number_template_path}")
                        print(f"⚠️ Vui lòng đặt file card_number.png vào thư mục {template_dir}")
                        break

                if not card_number_found:
                    print(f"⚠️ Không thể tìm thấy card_number.png sau 3 lần thử, tiếp tục với vòng lặp tiếp theo")
                    continue  # Bỏ qua các bước còn lại trong vòng lặp này

                # Đợi một chút sau khi tap vào card_number.png
                if self._random_sleep(1.0, 2.0, f"Chờ sau khi xử lý card_number.png (vòng lặp {loop_index + 1})", check_stop_flag):
                    print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi xử lý card_number.png")
                    return False

                # Bước 7: Lấy thông tin thẻ từ file data/card.txt và nhập vào trường số thẻ
                print(f"Bước 7: Nhập thông tin thẻ (vòng lặp {loop_index + 1})")
                card_info = self.get_next_card()

                if not card_info:
                    print(f"❌ Không có thẻ nào để nhập, tiếp tục với vòng lặp tiếp theo")
                    continue  # Bỏ qua các bước còn lại trong vòng lặp này

                # Phân tích thông tin thẻ
                card_number, month, year, cvv = card_info

                # Nhập thông tin thẻ
                print(f"Nhập thông tin thẻ: {card_number}|{month}|{year}|{cvv}")

                try:
                    # Kiểm tra cờ dừng trước khi nhập thông tin thẻ
                    if should_stop():
                        print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi nhập thông tin thẻ")
                        return False

                    # Nhập số thẻ - nhập cả chuỗi một lần để tăng tốc
                    print(f"Nhập số thẻ: {card_number}")
                    text_for_adb = card_number.replace(' ', '%s')
                    subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "text", text_for_adb], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

                    # Đợi ngắn sau khi nhập số thẻ
                    if self._random_sleep(0.3, 0.7, "Chờ sau khi nhập số thẻ", check_stop_flag):
                        print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi nhập số thẻ")
                        return False

                    # Nhập tháng hết hạn (không cần nhấn Tab vì nhiều thiết bị tự động chuyển trường)
                    print(f"Nhập tháng hết hạn: {month}")
                    text_for_adb = month.replace(' ', '%s')
                    subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "text", text_for_adb], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

                    # Đợi ngắn sau khi nhập tháng
                    if self._random_sleep(0.2, 0.6, "Chờ sau khi nhập tháng hết hạn", check_stop_flag):
                        print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi nhập tháng hết hạn")
                        return False

                    # Nhập năm hết hạn (không cần nhấn Tab vì nhiều thiết bị tự động chuyển trường)
                    print(f"Nhập năm hết hạn: {year}")
                    text_for_adb = year.replace(' ', '%s')
                    subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "text", text_for_adb], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

                    # Đợi ngắn sau khi nhập năm
                    if self._random_sleep(0.2, 0.6, "Chờ sau khi nhập năm hết hạn", check_stop_flag):
                        print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi nhập năm hết hạn")
                        return False

                    # Nhập CVV (không cần nhấn Tab vì nhiều thiết bị tự động chuyển trường)
                    print(f"Nhập CVV: {cvv}")
                    text_for_adb = cvv.replace(' ', '%s')
                    subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "text", text_for_adb], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

                    # Đợi ngắn sau khi nhập CVV
                    if self._random_sleep(0.2, 0.7, "Chờ sau khi nhập CVV", check_stop_flag):
                        print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi nhập CVV")
                        return False

                    print("✅ Đã nhập thông tin thẻ thành công")

                    # Chờ 2-3 giây sau khi nhập thông tin thẻ
                    if self._random_sleep(2.0, 3.0, "Chờ sau khi nhập thông tin thẻ", check_stop_flag):
                        print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi nhập thông tin thẻ")
                        return False

                    # Tìm và tap vào save_card.png
                    print("Tìm và tap vào save_card.png")
                    save_card_found = False

                    # Thử tìm save_card.png tối đa 3 lần
                    for attempt in range(3):
                        print(f"Lần thử {attempt + 1}/3 tìm save_card.png")
                        if os.path.exists(save_card_template_path):
                            if self.find_image_and_tap(save_card_template_path, check_stop_flag=check_stop_flag):
                                save_card_found = True
                                print("✅ Đã tìm thấy và tap vào save_card.png")
                                break
                            else:
                                print(f"❌ Không tìm thấy save_card.png trong lần thử {attempt + 1}")
                                # Đợi một chút trước khi thử lại
                                self._random_sleep(1.0, 2.0, f"Chờ trước khi thử lại lần {attempt + 2}")
                        else:
                            print(f"❌ Không tìm thấy file template save_card.png tại {save_card_template_path}")
                            print(f"⚠️ Vui lòng đặt file save_card.png vào thư mục {template_dir}")
                            break

                    if not save_card_found:
                        print("⚠️ Không thể tìm thấy save_card.png sau 3 lần thử")
                        # Đánh dấu thẻ là error
                        self.mark_card_as_processed(card_info, "error")
                        # Nhấn nút Back 3 lần
                        print("Nhấn nút Back 2 lần")
                        self.press_key(4)  # KEYCODE_BACK = 4
                        self._random_sleep(1.0, 1.5, "Chờ sau khi nhấn Back lần 1")
                        self.press_key(4)  # KEYCODE_BACK = 4
                        self._random_sleep(1.0, 1.5, "Chờ sau khi nhấn Back lần 2")

                        continue  # Bỏ qua các bước còn lại trong vòng lặp này

                    # Chờ 3-5 giây sau khi tap vào save_card.png (tăng thời gian chờ)
                    if self._random_sleep(3.0, 5.0, "Chờ sau khi tap vào save_card.png", check_stop_flag):
                        print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi tap vào save_card.png")
                        return False

                    # Kiểm tra cờ dừng trước khi kiểm tra add_payment_ads.png
                    if should_stop():
                        print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi kiểm tra add_payment_ads.png")
                        return False

                    # Kiểm tra xem add_payment_ads.png có xuất hiện không
                    print("Kiểm tra xem add_payment_ads.png có xuất hiện không")
                    add_payment_ads_found = False

                    # Thử tìm add_payment_ads.png tối đa 3 lần
                    for attempt in range(3):
                        # Kiểm tra cờ dừng trước mỗi lần thử
                        if should_stop():
                            print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong lần thử {attempt + 1}/3 tìm add_payment_ads.png")
                            return False

                        print(f"Lần thử {attempt + 1}/3 tìm add_payment_ads.png")
                        if os.path.exists(add_payment_ads_template_path):
                            # Sử dụng phương thức find_image_and_tap nhưng không tap vào nó
                            # Tạo một hàm giả để kiểm tra ảnh mà không tap
                            def check_image_without_tap(template_path, threshold=0.7, scale_factor=0.5):
                                try:
                                    # Tạo thư mục screen nếu chưa tồn tại
                                    screen_dir = path_manager.get_screen_path()
                                    if not os.path.exists(screen_dir):
                                        os.makedirs(screen_dir, exist_ok=True)

                                    # Chụp màn hình thiết bị trực tiếp vào bộ nhớ
                                    screenshot_path = os.path.join(screen_dir, f"screen_{self.serial}.png")

                                    # Sử dụng pipe để tránh ghi file tạm
                                    process = subprocess.Popen(
                                        [ADB_PATH, "-s", self.serial, "exec-out", "screencap", "-p"],
                                        stdout=subprocess.PIPE,
                                        stderr=subprocess.PIPE,
                                        creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
                                    )
                                    screenshot_data, _ = process.communicate()

                                    # Chuyển đổi dữ liệu ảnh thành mảng numpy
                                    screenshot_array = np.frombuffer(screenshot_data, dtype=np.uint8)
                                    screenshot = cv2.imdecode(screenshot_array, cv2.IMREAD_COLOR)

                                    # Lưu ảnh để debug nếu cần
                                    cv2.imwrite(screenshot_path, screenshot)

                                    # Đọc ảnh template
                                    template = cv2.imread(template_path)

                                    if template is None or screenshot is None:
                                        print(f"❌ Không thể đọc ảnh template hoặc ảnh màn hình")
                                        return False, 0

                                    # Thu nhỏ ảnh để tăng tốc xử lý
                                    if scale_factor < 1.0:
                                        new_width = int(screenshot.shape[1] * scale_factor)
                                        new_height = int(screenshot.shape[0] * scale_factor)
                                        screenshot_resized = cv2.resize(screenshot, (new_width, new_height), interpolation=cv2.INTER_AREA)

                                        new_width = int(template.shape[1] * scale_factor)
                                        new_height = int(template.shape[0] * scale_factor)
                                        template_resized = cv2.resize(template, (new_width, new_height), interpolation=cv2.INTER_AREA)
                                    else:
                                        screenshot_resized = screenshot
                                        template_resized = template

                                    # Chuyển đổi ảnh sang thang độ xám để giảm kích thước dữ liệu
                                    template_gray = cv2.cvtColor(template_resized, cv2.COLOR_BGR2GRAY)
                                    screenshot_gray = cv2.cvtColor(screenshot_resized, cv2.COLOR_BGR2GRAY)

                                    # Tìm kiếm template trong ảnh màn hình với phương pháp hiệu quả
                                    result = cv2.matchTemplate(screenshot_gray, template_gray, cv2.TM_CCOEFF_NORMED)
                                    _, max_val, _, _ = cv2.minMaxLoc(result)

                                    # Trả về kết quả tìm kiếm và giá trị độ khớp
                                    return max_val >= threshold, max_val
                                except Exception as e:
                                    print(f"❌ Lỗi khi kiểm tra ảnh: {str(e)}")
                                    return False, 0

                            # Kiểm tra ảnh mà không tap
                            found, max_val = check_image_without_tap(add_payment_ads_template_path)

                            if found:
                                add_payment_ads_found = True
                                print(f"✅ Đã tìm thấy add_payment_ads.png với độ khớp {max_val:.2f}")
                                break
                            else:
                                print(f"❌ Không tìm thấy add_payment_ads.png trong lần thử {attempt + 1} (max_val = {max_val:.2f})")
                                # Đợi lâu hơn trước khi thử lại (tăng thời gian chờ)
                                self._random_sleep(2.0, 3.0, f"Chờ trước khi thử lại lần {attempt + 2}")
                        else:
                            print(f"❌ Không tìm thấy file template add_payment_ads.png tại {add_payment_ads_template_path}")
                            print(f"⚠️ Vui lòng đặt file add_payment_ads.png vào thư mục {template_dir}")
                            break

                    # Kiểm tra cờ dừng trước khi đánh dấu thẻ
                    if should_stop():
                        print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi đánh dấu thẻ")
                        return False

                    # Cập nhật trạng thái trong AutoK.py sau mỗi lần thêm thẻ
                    try:
                        # Gọi hàm cập nhật trạng thái nếu có
                        if hasattr(self, 'update_status_func') and callable(self.update_status_func):
                            self.update_status_func()
                            # Gọi lại một lần nữa để đảm bảo UI được cập nhật
                            self.update_status_func()
                        else:
                            from PyQt5.QtWidgets import QApplication
                            QApplication.processEvents()
                            # Thêm một lần cập nhật nữa để đảm bảo UI được cập nhật
                            QApplication.processEvents()
                    except:
                        pass

                    # Nếu không tìm thấy add_payment_ads.png, thử tìm visa_ads.png
                    visa_ads_found = False
                    if not add_payment_ads_found:
                        print("Không tìm thấy add_payment_ads.png, thử tìm visa_ads.png")
                        visa_ads_template_path = os.path.join(template_dir, "visa_ads.png")

                        # Thử tìm visa_ads.png tối đa 3 lần
                        for attempt in range(3):
                            # Kiểm tra cờ dừng trước mỗi lần thử
                            if should_stop():
                                print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong lần thử {attempt + 1}/3 tìm visa_ads.png")
                                return False

                            print(f"Lần thử {attempt + 1}/3 tìm visa_ads.png")
                            if os.path.exists(visa_ads_template_path):
                                # Sử dụng cùng hàm check_image_without_tap
                                found, max_val = check_image_without_tap(visa_ads_template_path)

                                if found:
                                    visa_ads_found = True
                                    print(f"✅ Đã tìm thấy visa_ads.png với độ khớp {max_val:.2f}")
                                    break
                                else:
                                    print(f"❌ Không tìm thấy visa_ads.png trong lần thử {attempt + 1} (max_val = {max_val:.2f})")
                                    # Đợi lâu hơn trước khi thử lại
                                    self._random_sleep(2.0, 3.0, f"Chờ trước khi thử lại visa_ads.png lần {attempt + 2}")
                            else:
                                print(f"❌ Không tìm thấy file template visa_ads.png tại {visa_ads_template_path}")
                                print(f"⚠️ Vui lòng đặt file visa_ads.png vào thư mục {template_dir}")
                                break

                    # Đánh dấu thẻ dựa trên kết quả kiểm tra (cả add_payment_ads.png và visa_ads.png)
                    if add_payment_ads_found or visa_ads_found:
                        if add_payment_ads_found:
                            print("✅ Thêm thẻ thành công (phát hiện add_payment_ads.png)")
                        else:
                            print("✅ Thêm thẻ thành công (phát hiện visa_ads.png)")
                        # Đánh dấu thẻ là ok
                        self.mark_card_as_processed(card_info, "ok")
                    else:
                        print("❌ Thêm thẻ thất bại (không tìm thấy add_payment_ads.png hoặc visa_ads.png)")
                        # Đánh dấu thẻ là error
                        self.mark_card_as_processed(card_info, "error")
                        # Nhấn nút Back 3 lần
                        print("Nhấn nút Back 3 lần")

                        # Kiểm tra cờ dừng trước khi nhấn Back lần 1
                        if should_stop():
                            print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi nhấn Back lần 1")
                            return False

                        self.press_key(4)  # KEYCODE_BACK = 4
                        if self._random_sleep(1.0, 1.5, "Chờ sau khi nhấn Back lần 1", check_stop_flag):
                            print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng sau khi nhấn Back lần 1")
                            return False

                        # Kiểm tra cờ dừng trước khi nhấn Back lần 2
                        if should_stop():
                            print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi nhấn Back lần 2")
                            return False

                        self.press_key(4)  # KEYCODE_BACK = 4
                        if self._random_sleep(1.0, 1.5, "Chờ sau khi nhấn Back lần 2", check_stop_flag):
                            print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng sau khi nhấn Back lần 2")
                            return False

                except Exception as e:
                    print(f"❌ Lỗi khi xử lý thẻ: {str(e)}")
                    # Đánh dấu thẻ là error
                    self.mark_card_as_processed(card_info, "error")

                # Đợi ngắn sau khi nhập thông tin thẻ
                if self._random_sleep(0.5, 0.5, "Chờ sau khi nhập thông tin thẻ", check_stop_flag):
                    print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ sau khi nhập thông tin thẻ")
                    return False

                # Nếu chưa phải vòng lặp cuối cùng, đợi thêm một chút trước khi bắt đầu vòng lặp tiếp theo
                if loop_index < self.num_loops - 1:
                    # Kiểm tra cờ dừng trước khi đợi
                    if should_stop():
                        print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi đợi để bắt đầu vòng lặp tiếp theo")
                        return False

                    if self._random_sleep(2.0, 3.0, f"Chờ trước khi bắt đầu vòng lặp tiếp theo ({loop_index + 2}/{self.num_loops})", check_stop_flag):
                        print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ để bắt đầu vòng lặp tiếp theo")
                        return False

            # Kiểm tra cờ dừng trước khi về màn hình chính
            if should_stop():
                print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi về màn hình chính")
                return False

            print(f"✅ Đã hoàn thành {self.num_loops} vòng lặp thêm thẻ")

            # Thoát hoàn toàn ứng dụng khi hoàn thành thêm thẻ
            print("🎯 Hoàn thành thêm thẻ - Thoát hoàn toàn ứng dụng")
            self.exit_app_completely(check_stop_flag)

            return True

        except Exception as e:
            print(f"❌ Lỗi khi thực hiện chuỗi hành động: {str(e)}")
            return False