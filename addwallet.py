"""
<PERSON><PERSON><PERSON><PERSON><PERSON> thêm ví (Add Wallet) cho AutoK
Thực hiện chuỗi hành động tự động để thêm ví vào Google Wallet
"""

import subprocess
import time
import random
import cv2
import os
import sys
import datetime

# Import path_manager để quản lý đường dẫn
import path_manager

class add_wallet:
    def __init__(self, serial, num_loops=1, device_index=0, total_devices=1, adb_path=None):
        """
        Khởi tạo đối tượng add_wallet.
        :param serial: Số serial của thiết bị.
        :param num_loops: Số lần lặp (mặc định là 1).
        :param device_index: Chỉ số thiết bị trong danh sách (bắt đầu từ 0).
        :param total_devices: Tổng số thiết bị.
        :param adb_path: Đường dẫn đến ADB (tùy chọn).
        """
        self.serial = serial
        self.num_loops = num_loops
        self.device_index = device_index
        self.total_devices = total_devices

        # Thiết lập ADB path
        if adb_path and adb_path != "adb":
            self.adb_path = adb_path
        else:
            # Thử tìm ADB path từ AutoK
            try:
                from AutoK import get_adb_path
                self.adb_path = get_adb_path()
            except:
                self.adb_path = "adb"  # Fallback

        # Lấy đường dẫn template
        self.template_dir = path_manager.get_template_path()

        # Device distribution parameters (giống addads)
        self.device_index = device_index
        self.total_devices = total_devices
        self.cards = []  # Danh sách thẻ được phân phối cho device này

        # Biến để theo dõi trạng thái hiện tại cho UI update
        self.current_card = 0
        self.total_cards = 0
        self.current_card_info = None
        self.update_status_func = None

        # Biến đếm thất bại liên tiếp
        self.consecutive_failures = 0
        self.max_consecutive_failures = 10  # Dừng sau 10 lần thất bại liên tiếp

        # Load và phân phối thẻ cho device này
        self.load_cards()

        # Đường dẫn đến các file template
        self.addwallet_template_path = os.path.join(self.template_dir, "addwallet.png")
        self.view_wallet_template_path = os.path.join(self.template_dir, "view_wallet.png")
        self.not_now_template_path = os.path.join(self.template_dir, "not_now.png")
        self.add_wallet_template_path = os.path.join(self.template_dir, "add_wallet.png")
        self.payment_card_template_path = os.path.join(self.template_dir, "payment_card.png")
        self.credit_wallet_template_path = os.path.join(self.template_dir, "credit_wallet.png")
        self.new_credit_wallet_template_path = os.path.join(self.template_dir, "new_credit_wallet.png")

        # Template cho nhập thẻ
        self.card_number_template_path = os.path.join(self.template_dir, "card_number.png")
        self.enter_details_template_path = os.path.join(self.template_dir, "enter_details.png")
        self.or_enter_template_path = os.path.join(self.template_dir, "or_enter.png")
        self.united_states_template_path = os.path.join(self.template_dir, "united_states.png")
        self.united_states2_template_path = os.path.join(self.template_dir, "united_states2.png")
        self.save_and_continue_template_path = os.path.join(self.template_dir, "save_and_continue.png")
        self.got_it_wallet_template_path = os.path.join(self.template_dir, "got_it_wallet.png")
        self.add_another_template_path = os.path.join(self.template_dir, "add_another.png")
        self.continue_template_path = os.path.join(self.template_dir, "continue.png")
        self.save_card_template_path = os.path.join(self.template_dir, "save_card.png")
        self.name_template_path = os.path.join(self.template_dir, "name.png")
        self.g_pay_template_path = os.path.join(self.template_dir, "g_pay.png")
        self.got_it_trang_wallet_template_path = os.path.join(self.template_dir, "got_it_trang_wallet.png")

        # Template cho các bước từ deletecardclose.py
        self.chrome_template_path = os.path.join(self.template_dir, "chrome.png")
        self.yes_im_in_template_path = os.path.join(self.template_dir, "yes_im_in.png")
        self.more_template_path = os.path.join(self.template_dir, "more.png")
        self.got_it_template_path = os.path.join(self.template_dir, "got_it.png")
        self.search_template_path = os.path.join(self.template_dir, "search.png")
        self.got_it_view_wallet_template_path = os.path.join(self.template_dir, "got_it_view_wallet.png")
        self.yes_it_was_template_path = os.path.join(self.template_dir, "yes_it_was.png")

    def _random_sleep(self, min_seconds, max_seconds, message=None, stop_flag_func=None):
        """Tạm dừng thực thi trong một khoảng thời gian ngẫu nhiên với khả năng dừng."""
        seconds = random.uniform(min_seconds, max_seconds)
        if message:
            print(f"{message} ({seconds:.2f}s)")

        # Sleep với khả năng kiểm tra cờ dừng
        if stop_flag_func:
            # Chia sleep thành các chunk nhỏ để có thể kiểm tra cờ dừng
            sleep_chunks = max(1, int(seconds * 10))  # 10 chunks per second
            chunk_time = seconds / sleep_chunks

            for i in range(sleep_chunks):
                if stop_flag_func and stop_flag_func():
                    print(f"⚠️ Đã nhận tín hiệu dừng trong khi sleep (chunk {i+1}/{sleep_chunks})")
                    return True  # Return True to indicate stop was detected
                time.sleep(chunk_time)

                # Update UI during sleep
                try:
                    from PyQt5.QtWidgets import QApplication
                    QApplication.processEvents()
                except:
                    pass
        else:
            time.sleep(seconds)

        return False  # Return False to indicate normal completion

    def load_cards(self):
        """
        Đọc danh sách thẻ từ file data/card.txt và phân phối cho các thiết bị (giống addads)
        """
        try:
            card_file_path = path_manager.get_data_path("card.txt")

            if not os.path.exists(card_file_path):
                print(f"⚠️ File card.txt không tồn tại: {card_file_path}")
                self.cards = []
                return

            # Đọc file và lọc thẻ hợp lệ
            with open(card_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            valid_cards = []
            for line in lines:
                line = line.strip()
                if not line or line.startswith('#'):
                    continue

                parts = line.split('|')
                if len(parts) >= 4:
                    # Chỉ lấy thẻ chưa được đánh dấu (không có -ok, -error, -expired)
                    cvv_status = parts[3]
                    if (not cvv_status.endswith('-ok') and
                        not cvv_status.endswith('-error') and
                        not cvv_status.endswith('-expired')):
                        valid_cards.append(line)

            # Phân phối thẻ cho các thiết bị theo phương pháp round-robin (giống addads)
            if self.total_devices > 0 and len(valid_cards) > 0:
                device_cards = []
                for i in range(len(valid_cards)):
                    if i % self.total_devices == self.device_index:
                        device_cards.append(valid_cards[i])

                self.cards = device_cards
                print(f"✅ Thiết bị {self.serial} (chỉ số {self.device_index}/{self.total_devices}): Đã phân phối {len(self.cards)} thẻ theo phương pháp luân phiên")
            else:
                # Nếu không có thông tin về tổng số thiết bị, sử dụng tất cả thẻ
                self.cards = valid_cards
                print(f"✅ Thiết bị {self.serial}: Đã load {len(self.cards)} thẻ")

        except Exception as e:
            print(f"❌ Lỗi khi load cards: {e}")
            self.cards = []

    def get_random_name(self):
        """
        Lấy tên random từ file data/name.txt
        :return: Tên random hoặc "Rivanka" nếu có lỗi
        """
        try:
            name_file_path = path_manager.get_data_path("name.txt")

            if not os.path.exists(name_file_path):
                print(f"⚠️ File name.txt không tồn tại: {name_file_path}")
                return "Rivanka"  # Fallback name

            # Đọc tất cả tên từ file
            with open(name_file_path, 'r', encoding='utf-8') as f:
                names = f.readlines()

            # Lọc tên hợp lệ (bỏ dòng trống và comment)
            valid_names = []
            for name in names:
                name = name.strip()
                if name and not name.startswith('#'):
                    valid_names.append(name)

            if not valid_names:
                print("⚠️ Không có tên hợp lệ trong file name.txt")
                return "Rivanka"  # Fallback name

            # Chọn tên random
            random_name = random.choice(valid_names)
            print(f"✅ Đã chọn tên random: {random_name}")
            return random_name

        except Exception as e:
            print(f"❌ Lỗi khi đọc file name.txt: {e}")
            return "Rivanka"  # Fallback name

    def get_random_address_number(self):
        """
        Lấy số random từ file data/address.txt
        :return: Số random hoặc "12" nếu có lỗi
        """
        try:
            address_file_path = path_manager.get_data_path("address.txt")

            if not os.path.exists(address_file_path):
                print(f"⚠️ File address.txt không tồn tại: {address_file_path}")
                return "12"  # Fallback number

            # Đọc tất cả số từ file
            with open(address_file_path, 'r', encoding='utf-8') as f:
                numbers = f.readlines()

            # Lọc số hợp lệ (bỏ dòng trống và comment)
            valid_numbers = []
            for number in numbers:
                number = number.strip()
                if number and not number.startswith('#'):
                    # Kiểm tra xem có phải là số không
                    try:
                        int(number)  # Test xem có parse được thành số không
                        valid_numbers.append(number)
                    except ValueError:
                        continue  # Bỏ qua dòng không phải số

            if not valid_numbers:
                print("⚠️ Không có số hợp lệ trong file address.txt")
                return "12"  # Fallback number

            # Chọn số random
            random_number = random.choice(valid_numbers)
            print(f"✅ Đã chọn số random từ address.txt: {random_number}")
            return random_number

        except Exception as e:
            print(f"❌ Lỗi khi đọc file address.txt: {e}")
            return "12"  # Fallback number

    def press_key(self, keycode):
        """Gửi phím đến thiết bị."""
        try:
            cmd = [self.adb_path, "-s", self.serial, "shell", "input", "keyevent", str(keycode)]
            subprocess.run(cmd, check=True,
                         creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
        except FileNotFoundError:
            print(f"❌ Không tìm thấy ADB tại: {self.adb_path}")
            raise
        except Exception as e:
            print(f"❌ Lỗi khi gửi phím: {e}")
            raise

    def tap(self, x, y):
        """Tap vào tọa độ x, y."""
        try:
            cmd = [self.adb_path, "-s", self.serial, "shell", "input", "tap", str(x), str(y)]
            subprocess.run(cmd, check=True,
                         creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
            print(f"✅ Đã tap vào tọa độ ({x}, {y})")
        except subprocess.CalledProcessError as e:
            print(f"❌ Lỗi khi tap vào ({x}, {y}): {e}")

    def swipe(self, start_x, start_y, end_x, end_y, duration=300):
        """Thực hiện thao tác vuốt trên màn hình."""
        try:
            cmd = [self.adb_path, "-s", self.serial, "shell", "input", "swipe",
                   str(start_x), str(start_y), str(end_x), str(end_y), str(duration)]
            subprocess.run(cmd, check=True,
                         creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
        except FileNotFoundError:
            print(f"❌ Không tìm thấy ADB tại: {self.adb_path}")
            raise
        except Exception as e:
            print(f"❌ Lỗi khi vuốt màn hình: {e}")
            raise

    def take_screenshot(self):
        """Chụp ảnh màn hình và trả về đường dẫn file."""
        screenshot_path = path_manager.get_screen_path(f"screen_{self.serial}.png")
        try:
            cmd = [self.adb_path, "-s", self.serial, "exec-out", "screencap", "-p"]
            with open(screenshot_path, "wb") as f:
                subprocess.run(cmd, stdout=f, check=True,
                             creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
            return screenshot_path
        except FileNotFoundError:
            print(f"❌ Không tìm thấy ADB tại: {self.adb_path}")
            raise
        except Exception as e:
            print(f"❌ Lỗi khi chụp ảnh màn hình: {e}")
            raise

    def find_image_on_screen(self, template_path, threshold=0.8, max_retries=3):
        """Tìm hình ảnh trên màn hình nhưng không tap vào."""
        for attempt in range(max_retries):
            try:
                # Cập nhật UI để tránh đóng băng
                try:
                    from PyQt5.QtWidgets import QApplication
                    QApplication.processEvents()
                except:
                    pass

                # Chụp ảnh màn hình
                screenshot_path = self.take_screenshot()

                # Đọc ảnh template và ảnh màn hình
                template = cv2.imread(template_path)
                screenshot = cv2.imread(screenshot_path)

                if template is None or screenshot is None:
                    print(f"❌ Không thể đọc ảnh template hoặc ảnh màn hình (lần thử {attempt + 1}/{max_retries})")
                    continue

                # Chuyển đổi ảnh sang thang độ xám
                template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
                screenshot_gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)

                # Tìm kiếm template trong ảnh màn hình
                result = cv2.matchTemplate(screenshot_gray, template_gray, cv2.TM_CCOEFF_NORMED)
                _, max_val, _, max_loc = cv2.minMaxLoc(result)

                # Nếu độ khớp lớn hơn ngưỡng, trả về vị trí tìm thấy
                if max_val >= threshold:
                    # Tính toán tọa độ trung tâm của template
                    h, w = template_gray.shape
                    center_x = max_loc[0] + w // 2
                    center_y = max_loc[1] + h // 2

                    print(f"✅ Đã tìm thấy {os.path.basename(template_path)} tại ({center_x}, {center_y}) (độ khớp: {max_val:.2f})")
                    return center_x, center_y
                else:
                    print(f"❌ Không tìm thấy {os.path.basename(template_path)} trong lần thử {attempt + 1}/{max_retries} (max_val = {max_val:.2f})")

            except Exception as e:
                print(f"❌ Lỗi khi tìm {os.path.basename(template_path)}: {str(e)}")

        return None

    def find_image_and_tap(self, template_path, threshold=0.7, max_retries=1):
        """Tìm hình ảnh trên màn hình và tap vào vị trí tìm thấy."""
        for attempt in range(max_retries):
            try:
                # Cập nhật UI để tránh đóng băng
                try:
                    from PyQt5.QtWidgets import QApplication
                    QApplication.processEvents()
                except:
                    pass

                # Chụp ảnh màn hình
                screenshot_path = self.take_screenshot()

                # Đọc ảnh template và ảnh màn hình
                template = cv2.imread(template_path)
                screenshot = cv2.imread(screenshot_path)

                if template is None or screenshot is None:
                    print(f"❌ Không thể đọc ảnh template hoặc ảnh màn hình (lần thử {attempt + 1}/{max_retries})")
                    continue

                # Chuyển đổi ảnh sang thang độ xám
                template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
                screenshot_gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)

                # Tìm kiếm template trong ảnh màn hình
                result = cv2.matchTemplate(screenshot_gray, template_gray, cv2.TM_CCOEFF_NORMED)
                _, max_val, _, max_loc = cv2.minMaxLoc(result)

                # Nếu độ khớp lớn hơn ngưỡng, tap vào vị trí tìm thấy
                if max_val >= threshold:
                    # Tính toán tọa độ trung tâm của template
                    h, w = template_gray.shape
                    center_x = max_loc[0] + w // 2
                    center_y = max_loc[1] + h // 2

                    # Tap vào vị trí trung tâm
                    cmd = [self.adb_path, "-s", self.serial, "shell", "input", "tap",
                           str(center_x), str(center_y)]
                    subprocess.run(cmd, check=True,
                                 creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

                    print(f"✅ Đã tìm thấy và tap vào {os.path.basename(template_path)} (độ khớp: {max_val:.2f})")
                    return True
                else:
                    print(f"❌ Không tìm thấy {os.path.basename(template_path)} trong lần thử {attempt + 1}/{max_retries} (max_val = {max_val:.2f})")

            except Exception as e:
                print(f"❌ Lỗi khi tìm và tap vào {os.path.basename(template_path)}: {str(e)}")

            # Đợi một chút trước khi thử lại với UI update
            if attempt < max_retries - 1:
                self._random_sleep(1.0, 2.0, f"Chờ trước khi thử lại lần {attempt + 2}")
                # Cập nhật UI trong khi chờ
                try:
                    from PyQt5.QtWidgets import QApplication
                    QApplication.processEvents()
                except:
                    pass

        return False

    def check_can_skip_view_wallet(self):
        """
        Kiểm tra xem Google Wallet có cho phép bỏ qua bước view_wallet không
        bằng cách tìm kiếm các template của bước tiếp theo (add_wallet, got_it, not_now)

        :return: True nếu có thể bỏ qua view_wallet, False nếu không thể
        """
        try:
            print("🔍 Đang kiểm tra các template của bước tiếp theo...")

            # Danh sách các template có thể xuất hiện khi bỏ qua view_wallet
            skip_check_templates = [
                "add_wallet.png",           # Bước chính tiếp theo
                "got_it_trang_wallet.png",  # Có thể xuất hiện trước add_wallet
                "not_now.png"               # Có thể xuất hiện trước add_wallet
            ]

            # Kiểm tra từng template
            for template_name in skip_check_templates:
                template_path = os.path.join(self.template_dir, template_name)

                if os.path.exists(template_path):
                    print(f"🔍 Kiểm tra {template_name}...")

                    # Thử tìm template với threshold thấp hơn để tăng khả năng phát hiện
                    found = self.find_image_and_tap(template_path, threshold=0.6, max_retries=2)

                    if found:
                        print(f"✅ Tìm thấy {template_name} - Google Wallet đã bỏ qua view_wallet")

                        # Nếu tìm thấy add_wallet, đánh dấu setup thành công
                        if template_name == "add_wallet.png":
                            print("🎯 Đã tap vào add_wallet - Setup thành công")

                        return True
                    else:
                        print(f"⚠️ Không tìm thấy {template_name}")
                else:
                    print(f"ℹ️ File {template_name} không tồn tại")

            print("❌ Không tìm thấy template nào của bước tiếp theo - Không thể bỏ qua view_wallet")
            return False

        except Exception as e:
            print(f"❌ Lỗi khi kiểm tra skip view_wallet: {str(e)}")
            return False

    def get_next_card(self):
        """
        Lấy thẻ tiếp theo từ danh sách thẻ đã phân phối (giống addads)
        :return: dict với card_number, month, year, cvv hoặc None nếu không có thẻ
        """
        try:
            if not self.cards:
                print("❌ Không có thẻ nào trong danh sách được phân phối cho device này")
                return None

            # Lấy thẻ đầu tiên trong danh sách
            card_info_str = self.cards[0]

            # Xóa thẻ đã lấy khỏi danh sách
            self.cards.pop(0)

            # Phân tích thông tin thẻ
            parts = card_info_str.split('|')
            if len(parts) < 4:
                print(f"❌ Định dạng thẻ không hợp lệ: {card_info_str}")
                return None

            card_number = parts[0].strip()
            month = parts[1].strip()
            year = parts[2].strip()
            cvv_status = parts[3].strip()

            # Tách CVV và status (loại bỏ các suffix cũ nếu có)
            if '-' in cvv_status:
                cvv = cvv_status.split('-')[0]
            else:
                cvv = cvv_status

            # Kiểm tra xem thẻ có quá hạn hay không
            if self.is_card_expired(month, year):
                print(f"⚠️ Thẻ {card_number[-4:]} đã quá hạn (tháng {month}/{year})")
                # Đánh dấu thẻ hết hạn và lấy thẻ tiếp theo
                self.mark_card_as_processed_in_file(card_number, month, year, cvv, "expired")
                return self.get_next_card()  # Recursive call để lấy thẻ tiếp theo

            # Tạo card info để trả về
            card_found = {
                'card_number': card_number,
                'month': month,
                'year': year,
                'cvv': cvv
            }

            # Cập nhật thông tin thẻ hiện tại
            self.current_card_info = card_found

            print(f"✅ Lấy thẻ: {card_number[:4]}****{card_number[-4:]} {month}/{year}")
            return card_found

        except Exception as e:
            print(f"❌ Lỗi khi đọc file card.txt: {e}")
            return None

    def mark_card_as_processed_in_file(self, card_number, month, year, cvv, status):
        """
        Đánh dấu thẻ trong file card.txt (giống addads)
        """
        try:
            card_file_path = path_manager.get_data_path("card.txt")

            with open(card_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # Tìm và cập nhật thẻ
            for i, line in enumerate(lines):
                if card_number in line:
                    lines[i] = f"{card_number}|{month}|{year}|{cvv}-{status}\n"
                    break

            # Ghi lại file
            with open(card_file_path, 'w', encoding='utf-8') as f:
                f.writelines(lines)

            print(f"✅ Đã đánh dấu thẻ {card_number[-4:]} với trạng thái {status}")

        except Exception as e:
            print(f"❌ Lỗi khi đánh dấu thẻ: {e}")

    def mark_card_used(self, card_info, success=True):
        """
        Đánh dấu thẻ đã được sử dụng trong file card.txt
        :param card_info: dict thông tin thẻ từ get_next_card()
        :param success: True nếu thành công, False nếu thất bại
        """
        try:
            card_file_path = path_manager.get_data_path("card.txt")

            # Đọc tất cả các dòng
            with open(card_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # Tìm dòng chứa thẻ này
            card_number = card_info['card_number']
            for i, line in enumerate(lines):
                if card_number in line:
                    parts = line.strip().split('|')
                    if len(parts) >= 4:
                        # Cập nhật status đơn giản
                        if success:
                            new_status = f"{card_info['cvv']}-ok"
                        else:
                            new_status = f"{card_info['cvv']}-error"

                        # Tạo dòng mới
                        new_line = f"{parts[0]}|{parts[1]}|{parts[2]}|{new_status}\n"
                        lines[i] = new_line

                        # Ghi lại file
                        with open(card_file_path, 'w', encoding='utf-8') as f:
                            f.writelines(lines)

                        status_text = "thành công" if success else "thất bại"
                        print(f"✅ Đã đánh dấu thẻ {card_info['card_number'][-4:]} {status_text}")

                        # Cập nhật thống kê thẻ ngay lập tức
                        self.update_card_stats_realtime(card_info['card_number'][-4:], status_text)
                        break

        except Exception as e:
            print(f"❌ Lỗi khi cập nhật file card.txt: {e}")

    def update_card_stats_realtime(self, card_last4, status):
        """
        Cập nhật thống kê thẻ theo thời gian thực giống addads
        :param card_last4: 4 số cuối của thẻ
        :param status: Trạng thái thẻ (thành công/thất bại)
        """
        try:
            # Tìm cửa sổ chính của ứng dụng
            from PyQt5.QtWidgets import QApplication
            print(f"📊 Đang cập nhật thống kê thẻ sau khi đánh dấu thẻ {card_last4} là {status}...")

            # Đợi một chút để đảm bảo file đã được ghi hoàn tất
            import time
            time.sleep(0.5)

            # Tìm cửa sổ chính và cập nhật thống kê thẻ
            for widget in QApplication.topLevelWidgets():
                if hasattr(widget, 'update_card_stats'):
                    success = widget.update_card_stats()
                    if success:
                        print(f"✅ Đã cập nhật thống kê thẻ thành công sau khi đánh dấu thẻ {card_last4}")
                    else:
                        print(f"⚠️ Cập nhật thống kê thẻ không thành công sau khi đánh dấu thẻ {card_last4}")
                    break
        except Exception as e:
            print(f"❌ Lỗi khi cập nhật thống kê thẻ: {str(e)}")

    def is_card_expired(self, month, year):
        """
        Kiểm tra xem thẻ có quá hạn hay không
        :param month: Tháng hết hạn
        :param year: Năm hết hạn
        :return: True nếu thẻ đã quá hạn, False nếu thẻ còn hạn
        """
        try:
            # Lấy thời gian hiện tại
            now = datetime.datetime.now()
            current_month = now.month
            current_year = now.year

            # Chuyển đổi tháng và năm hết hạn sang số nguyên
            exp_month = int(month)

            # Xử lý năm 2 chữ số
            if len(year) == 2:
                exp_year = 2000 + int(year)
            else:
                exp_year = int(year)

            # Kiểm tra xem thẻ có quá hạn hay không
            if exp_year < current_year:
                return True
            elif exp_year == current_year and exp_month < current_month:
                return True
            else:
                return False

        except Exception as e:
            print(f"❌ Lỗi khi kiểm tra thẻ quá hạn: {str(e)}")
            # Nếu có lỗi, coi như thẻ không quá hạn
            return False

    def mark_card_expired(self, card_info):
        """
        Đánh dấu thẻ đã hết hạn trong file card.txt
        :param card_info: dict thông tin thẻ từ get_next_card()
        """
        try:
            card_file_path = path_manager.get_data_path("card.txt")

            # Đọc tất cả các dòng
            with open(card_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # Cập nhật dòng tương ứng
            line_index = card_info['line_index']
            if line_index < len(lines):
                parts = lines[line_index].strip().split('|')
                if len(parts) >= 4:
                    # Đánh dấu thẻ hết hạn
                    new_status = f"{card_info['cvv']}-expired"

                    # Tạo dòng mới
                    new_line = f"{parts[0]}|{parts[1]}|{parts[2]}|{new_status}\n"
                    lines[line_index] = new_line

                    # Ghi lại file
                    with open(card_file_path, 'w', encoding='utf-8') as f:
                        f.writelines(lines)

                    print(f"⚠️ Đã đánh dấu thẻ {card_info['card_number'][-4:]} hết hạn")

        except Exception as e:
            print(f"❌ Lỗi khi đánh dấu thẻ hết hạn: {e}")

    def update_status_realtime(self):
        """
        Cập nhật trạng thái theo thời gian thực lên UI giống addads
        """
        try:
            # Gọi hàm cập nhật trạng thái nếu có
            if hasattr(self, 'update_status_func') and callable(self.update_status_func):
                self.update_status_func()
                # Gọi lại một lần nữa để đảm bảo UI được cập nhật
                self.update_status_func()
            else:
                from PyQt5.QtWidgets import QApplication
                QApplication.processEvents()
                # Thêm một lần cập nhật nữa để đảm bảo UI được cập nhật
                QApplication.processEvents()
        except:
            pass

    def peek_next_card(self):
        """
        Xem thông tin thẻ tiếp theo mà không xóa khỏi danh sách
        :return: dict với card_number, month, year, cvv hoặc None nếu không có thẻ
        """
        try:
            if not self.cards:
                print("❌ Không có thẻ nào trong danh sách được phân phối cho device này")
                return None

            # Lấy thẻ đầu tiên trong danh sách NHƯNG KHÔNG XÓA
            card_info_str = self.cards[0]

            # Phân tích thông tin thẻ
            parts = card_info_str.split('|')
            if len(parts) < 4:
                print(f"❌ Định dạng thẻ không hợp lệ: {card_info_str}")
                return None

            card_number = parts[0].strip()
            month = parts[1].strip()
            year = parts[2].strip()
            cvv_status = parts[3].strip()

            # Tách CVV và status (loại bỏ các suffix cũ nếu có)
            if '-' in cvv_status:
                cvv = cvv_status.split('-')[0]
            else:
                cvv = cvv_status

            # Tạo dict thông tin thẻ
            card_found = {
                'card_number': card_number,
                'month': month,
                'year': year,
                'cvv': cvv
            }

            print(f"👀 Xem thẻ tiếp theo: {card_number[:4]}****{card_number[-4:]} {month}/{year}")
            return card_found

        except Exception as e:
            print(f"❌ Lỗi khi xem thông tin thẻ tiếp theo: {e}")
            return None

    def check_wallet_result(self, stop_flag_func=None):
        """
        Kiểm tra kết quả thêm thẻ vào Google Wallet.
        Trong mỗi vòng lặp chỉ tap 1 trong 2: got_it_wallet hoặc add_another.
        Trả về True nếu thành công, False nếu thất bại hoặc không tìm thấy gì sau nhiều lần.
        """
        try:
            print("🔍 Kiểm tra kết quả thêm thẻ vào Google Wallet...")

            if stop_flag_func and stop_flag_func():
                print("⚠️ Dừng trước khi kiểm tra kết quả")
                return False

            self._random_sleep(10.0, 12.0, "⏳ Chờ kết quả thêm thẻ xuất hiện", stop_flag_func)

            max_attempts = 50  # Tăng từ 5 lên 50 lần thử
            for attempt in range(max_attempts):
                if stop_flag_func and stop_flag_func():
                    print(f"⚠️ Dừng trong lần thử {attempt + 1}/{max_attempts}")
                    return False

                # Chỉ log mỗi 5 lần để tránh spam log
                if attempt % 5 == 0 or attempt < 5:
                    print(f"🔄 Thử kiểm tra lần {attempt + 1}/{max_attempts}")

                # Thử tap got_it_wallet trước
                if self.find_image_and_tap(self.got_it_wallet_template_path):
                    print(f"✅ Tìm thấy got_it_wallet ở lần thử {attempt + 1} - Thêm thẻ thành công!")
                    self._random_sleep(5.0, 6.0, "Chờ sau khi tap got_it_wallet")
                    return True

                # Nếu không có, thử tap add_another
                elif self.find_image_and_tap(self.add_another_template_path):
                    print(f"❌ Tìm thấy add_another ở lần thử {attempt + 1} - Thêm thẻ thất bại!")
                    self._random_sleep(5.0, 6.0, "Chờ sau khi tap add_another")
                    return False

                # Nếu không thấy gì, chờ rồi thử lại (giảm thời gian chờ để tăng tốc)
                if attempt % 10 == 9:  # Log mỗi 10 lần
                    print(f"⏸️ Không thấy kết quả sau {attempt + 1} lần thử, tiếp tục...")

                # Giảm thời gian chờ để kiểm tra nhanh hơn
                self._random_sleep(1.0, 1.5, "Chờ trước lần thử tiếp theo")

            print(f"⛔️ Hết {max_attempts} lần mà không thấy kết quả - xem như thất bại")
            return False

        except Exception as e:
            print(f"❌ Lỗi khi kiểm tra kết quả: {e}")
            return False
        

        
    def input_basic_card_info(self, card_info, stop_flag_func=None):
        """
        Nhập thông tin cơ bản của thẻ (số thẻ, tháng/năm, CVV)
        :param card_info: dict thông tin thẻ
        :param stop_flag_func: Function để kiểm tra cờ dừng
        :return: True nếu thành công, False nếu thất bại
        """
        try:
            print(f"📝 Bắt đầu nhập thông tin cơ bản thẻ {card_info['card_number'][-4:]}")

            # Bước 1: Nhập số thẻ (form đã tự động focus vào trường số thẻ)
            if stop_flag_func and stop_flag_func():
                print("⚠️ Đã nhận tín hiệu dừng trước khi nhập số thẻ")
                return False

            print(f"⌨️ Bước 1: Nhập số thẻ: {card_info['card_number']}")
            cmd = [self.adb_path, "-s", self.serial, "shell", "input", "text", card_info['card_number']]
            subprocess.run(cmd, check=True,
                         creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
            if self._random_sleep(2.0, 3.0, "Chờ form tự động chuyển sang trường tháng/năm", stop_flag_func):
                return False  # Stop detected during sleep

            # Bước 2: Nhập tháng/năm (form tự động chuyển)
            if stop_flag_func and stop_flag_func():
                print("⚠️ Đã nhận tín hiệu dừng trước khi nhập tháng/năm")
                return False

            print(f"⌨️ Bước 2: Nhập tháng/năm: {card_info['month']}/{card_info['year']}")
            month_year = f"{card_info['month'].zfill(2)}/{card_info['year']}"
            cmd = [self.adb_path, "-s", self.serial, "shell", "input", "text", month_year]
            subprocess.run(cmd, check=True,
                         creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
            if self._random_sleep(2.0, 3.0, "Chờ form tự động chuyển sang trường CVV", stop_flag_func):
                return False  # Stop detected during sleep

            # Bước 3: Nhập CVV (form tự động chuyển)
            if stop_flag_func and stop_flag_func():
                print("⚠️ Đã nhận tín hiệu dừng trước khi nhập CVV")
                return False

            print(f"⌨️ Bước 3: Nhập CVV: {card_info['cvv']}")
            cmd = [self.adb_path, "-s", self.serial, "shell", "input", "text", card_info['cvv']]
            subprocess.run(cmd, check=True,
                         creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
            if self._random_sleep(2.0, 3.0, "Chờ form tự động chuyển sang trường name", stop_flag_func):
                return False  # Stop detected during sleep

            print(f"✅ Đã nhập xong thông tin cơ bản thẻ {card_info['card_number'][-4:]}")
            return True

        except Exception as e:
            print(f"❌ Lỗi khi nhập thông tin cơ bản thẻ: {e}")
            return False

    def find_image_and_tap_bottom_third(self, template_path, threshold=0.7, max_retries=2):
        """
        Tìm hình ảnh chỉ trong 1/3 màn hình phía dưới và tap vào vị trí tìm thấy.
        :param template_path: Đường dẫn đến file template
        :param threshold: Ngưỡng độ tương đồng (0-1)
        :param max_retries: Số lần thử lại tối đa
        :return: True nếu thành công, False nếu thất bại
        """
        for attempt in range(max_retries):
            try:
                # Cập nhật UI để tránh đóng băng
                try:
                    from PyQt5.QtWidgets import QApplication
                    QApplication.processEvents()
                except:
                    pass

                # Chụ ảnh màn hình
                screenshot_path = self.take_screenshot()

                # Đọc ảnh template và ảnh màn hình
                template = cv2.imread(template_path)
                screenshot = cv2.imread(screenshot_path)

                if template is None or screenshot is None:
                    print(f"❌ Không thể đọc ảnh template hoặc ảnh màn hình (lần thử {attempt + 1}/{max_retries})")
                    continue

                # Lấy kích thước màn hình
                screen_height, screen_width = screenshot.shape[:2]

                # Cắt ảnh màn hình chỉ lấy 1/3 phía dưới
                bottom_third_start = int(screen_height * 2 / 3)  # Bắt đầu từ 2/3 màn hình
                screenshot_bottom = screenshot[bottom_third_start:, :]  # Chỉ lấy 1/3 dưới

                print(f"🔍 Tìm kiếm trong vùng 1/3 màn hình phía dưới (từ y={bottom_third_start} đến y={screen_height})")

                # Chuyển đổi ảnh sang thang độ xám
                template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
                screenshot_gray = cv2.cvtColor(screenshot_bottom, cv2.COLOR_BGR2GRAY)

                # Tìm kiếm template trong ảnh màn hình (chỉ 1/3 dưới)
                result = cv2.matchTemplate(screenshot_gray, template_gray, cv2.TM_CCOEFF_NORMED)
                _, max_val, _, max_loc = cv2.minMaxLoc(result)

                print(f"🔍 Độ khớp tìm thấy: {max_val:.3f} (ngưỡng: {threshold})")

                # Nếu độ khớp lớn hơn ngưỡng, tap vào vị trí tìm thấy
                if max_val >= threshold:
                    # Tính toán tọa độ trung tâm của template
                    h, w = template_gray.shape
                    center_x = max_loc[0] + w // 2
                    center_y = max_loc[1] + h // 2 + bottom_third_start  # Cộng thêm offset của vùng 1/3 dưới

                    print(f"✅ Tìm thấy template tại ({center_x}, {center_y}) với độ khớp {max_val:.3f}")

                    # Tap vào vị trí trung tâm
                    subprocess.run([self.adb_path, "-s", self.serial, "shell", "input", "tap",
                                   str(center_x), str(center_y)], check=True,
                                   creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

                    print(f"✅ Đã tap vào vị trí ({center_x}, {center_y})")
                    return True
                else:
                    print(f"⚠️ Không tìm thấy template với độ khớp đủ cao (lần thử {attempt + 1}/{max_retries})")

            except Exception as e:
                print(f"❌ Lỗi khi tìm và tap vào template (lần thử {attempt + 1}/{max_retries}): {e}")

            # Đợi một chút trước khi thử lại
            if attempt < max_retries - 1:
                time.sleep(1)

        print(f"❌ Không thể tìm thấy và tap vào template sau {max_retries} lần thử")
        return False

    def find_and_tap_enter_details(self):
        """
        Tìm và tap vào enter_details.png hoặc or_enter.png chỉ trong 1/3 màn hình phía dưới
        :return: True nếu thành công, False nếu thất bại
        """
        try:
            print("🔍 Đang tìm nút enter details hoặc or enter trong 1/3 màn hình phía dưới...")

            if self.find_image_and_tap_bottom_third(self.enter_details_template_path):
                print("✅ Đã tìm thấy và tap vào enter_details.png trong 1/3 màn hình phía dưới")
                return True

            if self.find_image_and_tap_bottom_third(self.or_enter_template_path):
                print("✅ Đã tìm thấy và tap vào or_enter.png trong 1/3 màn hình phía dưới")
                return True

            print("⚠️ Không tìm thấy enter_details.png hoặc or_enter.png trong 1/3 màn hình phía dưới")
            return False

        except Exception as e:
            print(f"❌ Lỗi khi tìm enter details: {e}")
            return False

    
    
    def input_card_info(self, card_info, stop_flag_func=None):
        """
        Nhập thông tin thẻ vào form với tất cả các bước bổ sung
        :param card_info: dict thông tin thẻ
        :param stop_flag_func: Function để kiểm tra cờ dừng
        :return: True nếu thành công, False nếu thất bại
        """
        try:
            print(f"📝 Bắt đầu nhập thông tin thẻ {card_info['card_number'][-4:]}")

            # Kiểm tra cờ dừng trước khi bắt đầu
            if stop_flag_func and stop_flag_func():
                print("⚠️ Đã nhận tín hiệu dừng trước khi nhập thông tin thẻ")
                return False

            # Bước 1: Nhập name random từ file name.txt
            random_name = self.get_random_name()
            print(f"⌨️ Nhập name: {random_name}")
            cmd = [self.adb_path, "-s", self.serial, "shell", "input", "text", random_name]
            subprocess.run(cmd, check=True,
                         creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
            self._random_sleep(1.0, 2.0, "Chờ sau khi nhập name")

            # Bước 2: Tab 1 lần để chuyển trường
            print(f"⌨️ Tab 1 lần để chuyển trường")
            self.press_key(61)  # KEYCODE_TAB
            self._random_sleep(1.0, 2.0, "Chờ sau khi tab")

            # Bước 3: Kiểm tra united_states.png
            print(f"🔍 Bước 6: Kiểm tra united_states.png")
            united_states_found = False

            if os.path.exists(self.united_states_template_path):
                if self.find_image_on_screen(self.united_states_template_path):
                    print("✅ Tìm thấy united_states.png - Bỏ qua tap vị trí 500,937")
                    united_states_found = True
                else:
                    print("⚠️ Không tìm thấy united_states.png - Sẽ tap vào vị trí 500,937")
            else:
                print("⚠️ Template united_states.png không có - Sẽ tap vào vị trí 500,937")

            # Chỉ tap vào vị trí 500,937 nếu KHÔNG tìm thấy united_states.png để chọn quốc gia
            if not united_states_found:
                print(f"👆 Tap vào vị trí 500,937 (vì không tìm thấy united_states.png)")
                self.tap(500, 937)
                self._random_sleep(1.0, 2.0, "Chờ sau khi tap vị trí 500,937")

                # Bước 3.1: Vuốt lên 10 lần thật mạnh cho hết list (chỉ khi đã tap vị trí)
                print(f"⬆️ Bước 3.1: Vuốt lên 10 lần thật mạnh cho hết list")
                for i in range(10):
                    self.swipe(540, 800, 490, 200, 80)  # Vuốt mạnh từ dưới lên trên
                    print(f"   - Vuốt lần {i+1}/10")
                    self._random_sleep(0.7, 1.5, f"Chờ sau vuốt lần {i+1}")

                # Bước 3.2: Vuốt từ trên xuống 2 lần (chỉ khi đã tap vị trí)
                print(f"⬇️ Bước 8: Vuốt xuống 2 lần vừa đủ")
                for i in range(2):
                    self.swipe(510, 160, 540, 770, 600)  # Vuốt vừa từ trên xuống dưới
                    print(f"   - Vuốt vừa lần {i+1}/2")
                    self._random_sleep(0.5, 1.0, f"Chờ sau vuốt vừa lần {i+1}")

                # Chờ 3-4 giây sau bước 8 trước khi tìm united_states2
                self._random_sleep(3.0, 4.0, "Chờ 3-4s sau bước 8 trước khi tìm united_states2")

                # Bước 3.3: Tìm và tap vào united_states2.png (chỉ khi đã tap vị trí)
                print(f"🔍 Bước 9: Tìm và tap vào united_states2.png")
                if os.path.exists(self.united_states2_template_path):
                    if self.find_image_and_tap(self.united_states2_template_path):
                        print("✅ Đã tìm thấy và tap vào united_states2.png")
                    else:
                        print("❌ Không tìm thấy united_states2.png")
                        return False
                else:
                    print("❌ Template united_states2.png không có")
                    return False

                self._random_sleep(5.0, 6.0, "Chờ sau khi chọn country")
            else:
                print("ℹ️ Bỏ qua bước 7, 8, 9 vì đã tìm thấy united_states.png")
                self._random_sleep(1.0, 2.0, "Chờ sau khi tìm thấy united_states")

            # Bước 4: Tab 2 lần để chuyển trường
            print(f"⌨️ Tab 2 lần để chuyển trường")
            for i in range(2):
                self.press_key(61)  # KEYCODE_TAB
                print(f"   - Tab lần {i+1}/2")
                self._random_sleep(0.5, 1.0, f"Chờ sau tab lần {i+1}")

            # Kiểm tra xem có xuất hiện ô địa chỉ street_wallet.png không 
            
            # Bước 5: Kiểm tra quan trọng - street_wallet.png
            print(f"🔍 Bước 5: Kiểm tra quan trọng - street_wallet.png")
            street_wallet_template_path = os.path.join(self.template_dir, "street_wallet.png")

            if os.path.exists(street_wallet_template_path):
                if self.find_image_on_screen(street_wallet_template_path):
                    print("✅ Tìm thấy street_wallet.png - Nhập số random từ address.txt")
                    random_number = self.get_random_address_number()
                    print(f"⌨️ Nhập số random: {random_number}")
                    cmd = [self.adb_path, "-s", self.serial, "shell", "input", "text", random_number]
                    subprocess.run(cmd, check=True,
                                 creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                    self._random_sleep(4.0, 5.0, "Chờ sau khi nhập số")
                else:
                    print("❌ Không tìm thấy street_wallet.png - Thực hiện add lại thẻ add_single_card")
                    print("🔄 Gọi lại add_single_card do lần đầu chọn lại quốc gia bước 3.2")
                    if stop_flag_func and stop_flag_func():
                        print("⚠️ Đã nhận tín hiệu dừng trước khi gọi lại add_single_card")
                        return False
                    self._random_sleep(5.0, 7.0, "Chờ trước khi gọi lại add_single_card")

                    # Gọi lại input_basic_card_info
                    basic_card_success = self.add_single_card(card_info, stop_flag_func)
                    if not basic_card_success:
                        print("❌ Thất bại khi gọi lại add_single_card")
                        return False

                    print("✅ Đã hoàn thành add_single_card, tiếp tục với bước tiếp theo")
                    return True  # Kết thúc hàm input_card_info vì đã gọi lại add_single_card
            else:
                print("⚠️ Template street_wallet.png không tồn tại - Nhập số random từ address.txt")
                random_number = self.get_random_address_number()
                print(f"⌨️ Nhập số random: {random_number}")
                cmd = [self.adb_path, "-s", self.serial, "shell", "input", "text", random_number]
                subprocess.run(cmd, check=True,
                             creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                self._random_sleep(4.0, 5.0, "Chờ sau khi nhập số")

            # Bước 6: Tap vào vị trí 505, 650
            print(f"👆 Tap vào vị trí 505, 650")
            self.tap(505, 650)
            self._random_sleep(2.0, 3.0, "Chờ sau khi tap vị trí 505, 650")
                       

        except Exception as e:
            print(f"❌ Lỗi khi nhập thông tin thẻ: {e}")
            return False

    def add_single_card(self, stop_flag_func=None):
        """
        Thêm một thẻ vào Google Wallet (từ bước credit_wallet.png đến khi cập nhật thẻ thành công)
        :param stop_flag_func: Function để kiểm tra cờ dừng (optional)
        :return: True nếu thành công, False nếu thất bại
        """
        max_gpay_retries = 3  # số lần thử lại khi không tìm thấy g_pay

        for retry in range(max_gpay_retries):
            try:
                print(f"🔄 Bắt đầu quy trình thêm thẻ (lần thử {retry + 1}/{max_gpay_retries})")

                # Bước 1: Tìm và tap vào credit_wallet.png
                print("Bước 1: Tìm và tap vào credit_wallet.png")
                if stop_flag_func and stop_flag_func():
                    print("⚠️ Đã nhận tín hiệu dừng trước khi tap credit_wallet")
                    return False
                
                if self.find_image_and_tap(self.credit_wallet_template_path):
                    print("✅ Đã tìm thấy và tap vào credit_wallet.png")
                    self._random_sleep(2.0, 3.0, "Chờ sau khi tap credit_wallet")
                else:
                    print("❌ Không tìm thấy credit_wallet.png, vẫn tiếp tục...")

                # Bước 1.2: Tìm tap vào payment_card.png
                print("Bước 1.2: Tìm và tap vào payment_card.png")
                if stop_flag_func and stop_flag_func():
                    print("⚠️ Đã nhận tín hiệu dừng trước khi tap payment_card")
                    return False

                if self.find_image_and_tap(self.payment_card_template_path):
                    print("✅ Đã tìm thấy và tap vào payment_card.png")
                    self._random_sleep(2.0, 3.0, "Chờ sau khi tap payment_card")
                else:
                    print("❌ Không tìm thấy payment_card.png, vẫn tiếp tục...")

                # Bước 2: Tìm và tap vào new_credit_wallet.png
                print("Bước 2: Tìm và tap vào new_credit_wallet.png")
                if stop_flag_func and stop_flag_func():
                    print("⚠️ Đã nhận tín hiệu dừng trước khi tap new_credit_wallet")
                    return False

                if self.find_image_and_tap(self.new_credit_wallet_template_path):
                    print("✅ Đã tìm thấy và tap vào new_credit_wallet.png")
                    self._random_sleep(10.0, 12.0, "Chờ sau khi tap new_credit_wallet trong vòng lặp")
                else:
                    print("❌ Không tìm thấy new_credit_wallet.png, vẫn tiếp tục...")

                # Bước 3: Tìm và tap vào enter_details.png hoặc or_enter.png
                print("🔍 Bước 3: Tìm và tap vào enter_details.png hoặc or_enter.png")

                if stop_flag_func and stop_flag_func():
                    print("⚠️ Đã nhận tín hiệu dừng trước khi bắt đầu tìm enter_details")
                    return False

                enter_details_success = self.find_and_tap_enter_details()
                if enter_details_success:
                    print("✅ Đã tìm thấy và tap vào enter_details hoặc or_enter")
                    self._random_sleep(3.0, 4.0, "Chờ sau khi tap enter details")
                else:
                    print("❌ Không tìm thấy enter_details.png hoặc or_enter.png - Nhấn phím back để thoát hiện tượng treo")
                    self.press_key(4)  # KEYCODE_BACK = 4
                    self._random_sleep(2.0, 3.0, "Chờ sau khi nhấn back để thoát treo")
                    print("✅ Đã nhấn back, các chuỗi hành động sẽ tiếp tục")

                # Bước kiểm tra g_pay, nếu không có thì thử lại toàn bộ quy trình
                if not os.path.exists(self.g_pay_template_path):
                    print("❌ Không tìm thấy file template g_pay.png, bỏ qua xử lý thẻ")
                    return False

                if not self.find_image_and_tap(self.g_pay_template_path):
                    print(f"⚠️ Không tìm thấy g_pay trên màn hình (lần thử {retry + 1}), chờ và thử lại...")
                    self._random_sleep(5.0, 7.0, "Chờ trước khi thử lại kiểm tra g_pay")
                    if retry < max_gpay_retries - 1:
                        continue  # thử lại toàn bộ quy trình
                    else:
                        print("❌ Không tìm thấy g_pay sau nhiều lần thử, bỏ qua thẻ này")
                        return False

                print("✅ Đã tìm thấy g_pay, bắt đầu xử lý thẻ")

                # Bước 4: Lấy thẻ
                if stop_flag_func and stop_flag_func():
                    print("⚠️ Đã nhận tín hiệu dừng trước khi lấy thẻ")
                    return False

                card_info = self.get_next_card()
                if not card_info:
                    print("⚠️ Không có thẻ nào khả dụng để nhập")
                    return False
                

                # Bước 4.2: Nhập thông tin thẻ
                if stop_flag_func and stop_flag_func():
                    print("⚠️ Đã nhận tín hiệu dừng trước khi nhập thẻ")
                    return False

                card_success = self.input_basic_card_info(card_info, stop_flag_func)

                # Bước 5: nhập name và thông tin thẻ nếu thấy
                if os.path.exists(self.name_template_path):
                    if self.find_image_on_screen(self.name_template_path):
                        print("✅ Đã tìm thấy ô name, nhập thông tin")
                        if stop_flag_func and stop_flag_func():
                            print("⚠️ Đã nhận tín hiệu dừng")
                            return False
                        card_success = self.input_card_info(card_info, stop_flag_func)
                    else:
                        print("❌ Không tìm thấy ô name")

                # Bước 5.2: tap save_and_continue
                if stop_flag_func and stop_flag_func():
                    print("⚠️ Dừng trước khi tap save_and_continue")
                    return False

                if os.path.exists(self.save_and_continue_template_path):
                    if self.find_image_and_tap(self.save_and_continue_template_path):
                        print("✅ Tap save_and_continue thành công")
                        self._random_sleep(2.0, 3.0, "Chờ sau save and continue")

                        # Bước 14: kiểm tra kết quả
                        if stop_flag_func and stop_flag_func():
                            print("⚠️ Dừng trước khi kiểm tra kết quả")
                            return False

                        wallet_success = self.check_wallet_result(stop_flag_func)

                        # Logic mới: Nếu check_wallet_result thất bại, nhấn phím back để thoát treo
                        if not wallet_success:
                            print("⚠️ check_wallet_result thất bại - Nhấn phím back để thoát hiện tượng treo")
                            self.press_key(4)  # KEYCODE_BACK = 4
                            self._random_sleep(2.0, 3.0, "Chờ sau khi nhấn back để thoát treo")
                            print("✅ Đã nhấn back, các chuỗi hành động sẽ tiếp tục")

                        # Cập nhật trạng thái thẻ dựa trên kết quả kiểm tra
                        if card_info:
                            print(f"📝 Cập nhật trạng thái thẻ {card_info['card_number'][-4:]}...")
                            self.mark_card_used(card_info, wallet_success)
                            if wallet_success:
                                print(f"✅ Đã đánh dấu thẻ {card_info['card_number'][-4:]} thành công")
                            else:
                                print(f"❌ Đã đánh dấu thẻ {card_info['card_number'][-4:]} thất bại")

                        card_success = card_success and wallet_success
                    else:
                        print("❌ Không tìm thấy save_and_continue.png")
                        # Đánh dấu thẻ thất bại nếu không tìm thấy save_and_continue
                        if card_info:
                            print(f"📝 Đánh dấu thẻ {card_info['card_number'][-4:]} thất bại (không tìm thấy save_and_continue)")
                            self.mark_card_used(card_info, False)
                else:
                    print("❌ Không tồn tại template save_and_continue.png")
                    # Đánh dấu thẻ thất bại nếu không có template
                    if card_info:
                        print(f"📝 Đánh dấu thẻ {card_info['card_number'][-4:]} thất bại (không có template save_and_continue)")
                        self.mark_card_used(card_info, False)

                # Nếu thành công, trả về True
                return card_success

            except Exception as e:
                print(f"❌ Lỗi khi thêm thẻ (lần thử {retry + 1}): {str(e)}")
                # Đánh dấu thẻ thất bại nếu có lỗi exception
                if 'card_info' in locals() and card_info:
                    print(f"📝 Đánh dấu thẻ {card_info['card_number'][-4:]} thất bại (lỗi exception)")
                    self.mark_card_used(card_info, False)
                return False

        # Nếu hết vòng thử mà vẫn thất bại
        return False

    def execute_actions(self, stop_flag_func=None, num_cards=1, retry_count=0):
        """
        Thực hiện chuỗi hành động thêm ví.
        :param stop_flag_func: Function để kiểm tra cờ dừng (optional)
        :param num_cards: Số lượng thẻ cần thêm (từ spinbox)
        :return: True nếu thành công, False nếu thất bại.
        """
        try:
            # Kiểm tra số lần retry để tránh vòng lặp vô hạn
            max_retries = 2  # Tối đa 2 lần retry (tổng cộng 3 lần thử)
            if retry_count > max_retries:
                print(f"❌ Đã thử {retry_count} lần nhưng vẫn không tìm thấy add_wallet. Dừng để tránh vòng lặp vô hạn.")
                return False

            if retry_count > 0:
                print(f"🔄 Đây là lần thử thứ {retry_count + 1} (sau view_wallet_back)")

            print(f"\n=== Bắt đầu thực hiện chuỗi hành động thêm {num_cards} thẻ vào ví trên thiết bị {self.serial} (retry_count={retry_count}) ===\n")

            # Kiểm tra các file template cần thiết
            missing_templates = []
            for template_name in ["chrome.png", "continue.png", "yes_im_in.png", "more.png", "got_it.png",
                                 "addwallet.png", "view_wallet.png", "got_it_trang_wallet.png", "not_now.png", "add_wallet.png",
                                 "payment_card.png", "credit_wallet.png", "new_credit_wallet.png",
                                 "enter_details.png", "or_enter.png", "united_states.png", "united_states2.png",
                                 "save_and_continue.png", "got_it_wallet.png", "add_another.png",
                                 "save_card.png", "name.png"]:
                template_path = os.path.join(self.template_dir, template_name)
                if not os.path.exists(template_path):
                    missing_templates.append(template_name)

            if missing_templates:
                print(f"⚠️ Không tìm thấy các file template sau: {', '.join(missing_templates)}")
                print(f"⚠️ Vui lòng đặt các file template vào thư mục {self.template_dir}")

            # === CÁC BƯỚC 1-8 TỪ DELETECARDCLOSE.PY ===

            # Bước 1: Về màn hình chính bằng cách nhấn nút Home
            print("Bước 1: Về màn hình chính bằng cách nhấn nút Home")
            self.press_key(3)  # KEYCODE_HOME = 3
            print("✅ Đã về màn hình chính")

            # Đợi một chút sau khi về màn hình chính
            if self._random_sleep(2.0, 3.0, "Chờ sau khi về màn hình chính", stop_flag_func):
                print("⚠️ Đã nhận tín hiệu dừng từ người dùng sau khi về màn hình chính")
                return False

            # Kiểm tra cờ dừng trước khi vuốt lên
            if stop_flag_func and stop_flag_func():
                print("⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi vuốt lên")
                return False

            # Bước 2: Vuốt lên 1 lần để hiển thị menu ứng dụng
            print("Bước 2: Vuốt lên 1 lần để hiển thị menu ứng dụng")
            screen_height = 1920  # Chiều cao màn hình mặc định
            screen_width = 1080   # Chiều rộng màn hình mặc định

            # Vuốt từ dưới lên trên với thời gian vuốt ngắn hơn để vuốt nhanh hơn
            # Vuốt từ gần cuối màn hình lên gần đầu màn hình để vuốt dài hơn
            self.swipe(screen_width // 2, screen_height * 4 // 5, screen_width // 2, screen_height // 5, 250)

            # Đợi một chút sau khi vuốt để màn hình kịp phản hồi
            if self._random_sleep(1.0, 2.0, "Chờ sau khi vuốt lên 1 lần", stop_flag_func):
                print("⚠️ Đã nhận tín hiệu dừng từ người dùng sau khi vuốt lên 1 lần")
                return False

            # Bước 3: Tìm và tap vào biểu tượng Chrome
            print("Bước 3: Tìm và tap vào biểu tượng Chrome")
            chrome_template_path = os.path.join(self.template_dir, "chrome.png")
            chrome_found = False

            if os.path.exists(chrome_template_path):
                chrome_found = self.find_image_and_tap(chrome_template_path, max_retries=2)

            if not chrome_found:
                print("⚠️ Không tìm thấy biểu tượng Chrome")
                print("❌ Không thể mở Chrome")
                return False

            # Kiểm tra cờ dừng trước khi đợi Chrome khởi động
            if stop_flag_func and stop_flag_func():
                print("⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi đợi Chrome khởi động")
                return False

            # Đợi Chrome khởi động
            if self._random_sleep(6.0, 7.0, "Chờ Chrome khởi động hoàn toàn", stop_flag_func):
                print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi đợi Chrome khởi động")
                return False

            # === KIỂM TRA VÀ BỎ QUA BƯỚC 4–8 NẾU TÌM THẤY search.png ===
            search_template_path = os.path.join(self.template_dir, "search.png")
            skip_steps_4_to_8 = False

            if os.path.exists(search_template_path):
                found_search = self.find_image_on_screen(search_template_path)
                if found_search:
                    print("🔍 Đã tìm thấy search.png - bỏ qua bước 4-8 và chuyển đến bước 9")
                    skip_steps_4_to_8 = True

            if not skip_steps_4_to_8:
                # Bước 4: Tìm và tap vào nút Continue
                print("Bước 4: Tìm và tap vào nút Continue")
                continue_template_path = os.path.join(self.template_dir, "continue.png")
                continue_found = False

                if stop_flag_func and stop_flag_func():
                    print("⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi tìm nút Continue")
                    return False

                if os.path.exists(continue_template_path):
                    continue_found = self.find_image_and_tap(continue_template_path)

                if not continue_found:
                    print("⚠️ Không tìm thấy nút Continue, tiếp tục với bước tiếp theo")
                else:
                    if self._random_sleep(4.0, 5.0, "Chờ sau khi tap vào Continue", stop_flag_func):
                        print("⚠️ Đã nhận tín hiệu dừng từ người dùng sau khi tap vào Continue")
                        return False

                if stop_flag_func and stop_flag_func():
                    print("⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi tìm nút Yes I'm in")
                    return False

                # Bước 5: Tìm và tap vào nút Yes I'm in
                print("Bước 5: Tìm và tap vào nút Yes I'm in")
                yes_im_in_template_path = os.path.join(self.template_dir, "yes_im_in.png")
                yes_im_in_found = False

                if os.path.exists(yes_im_in_template_path):
                    yes_im_in_found = self.find_image_and_tap(yes_im_in_template_path, max_retries=2)

                if not yes_im_in_found:
                    print("⚠️ Không tìm thấy nút Yes I'm in, tiếp tục với bước tiếp theo")
                else:
                    if self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào Yes I'm in", stop_flag_func):
                        print("⚠️ Đã nhận tín hiệu dừng từ người dùng sau khi tap vào Yes I'm in")
                        return False

                if stop_flag_func and stop_flag_func():
                    print("⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi vuốt lên để tìm nút Got it")
                    return False

                # Bước 6–7: Vuốt nếu có more.png
                more_template_path = os.path.join(self.template_dir, "more.png")
                if os.path.exists(more_template_path):
                    try:
                        result = self.find_image_on_screen(more_template_path)
                        if result is not None:
                            print("✅ Tìm thấy more.png - Thực hiện vuốt lên")
                            self.swipe(screen_width // 2, screen_height * 4 // 5,
                                    screen_width // 2, screen_height // 5, 200)

                            if self._random_sleep(2.0, 3.0, "Chờ sau khi vuốt", stop_flag_func):
                                print("⚠️ Đã nhận tín hiệu dừng")
                                return False
                        else:
                            print("⚠️ Không tìm thấy more.png, tiếp tục với bước tiếp theo")
                    except Exception as e:
                        print(f"⚠️ Lỗi khi tìm more.png: {e}, tiếp tục với bước tiếp theo")
                else:
                    print("ℹ️ File more.png không tồn tại, bỏ qua bước này")

                if self._random_sleep(1.0, 2.0, "Chờ sau khi vuốt lên để tìm nút Got it", stop_flag_func):
                    print("⚠️ Đã nhận tín hiệu dừng từ người dùng sau khi vuốt lên để tìm nút Got it")
                    return False

                if stop_flag_func and stop_flag_func():
                    print("⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi tìm nút Got it")
                    return False

                # Bước 8: Tìm và tap vào nút Got it
                print("Bước 8: Tìm và tap vào nút Got it")
                got_it_template_path = os.path.join(self.template_dir, "got_it.png")
                got_it_found = False

                if os.path.exists(got_it_template_path):
                    got_it_found = self.find_image_and_tap(got_it_template_path)

                if got_it_found:
                    print("✅ Đã tìm thấy và tap vào nút Got it")
                    if self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào Got it", stop_flag_func):
                        print("⚠️ Đã nhận tín hiệu dừng từ người dùng sau khi tap vào Got it")
                        return False
                else:
                    print("⚠️ Không tìm thấy nút Got it, tiếp tục với bước tiếp theo")

            # === KẾT THÚC CÁC BƯỚC 1-8 TỪ DELETECARDCLOSE.PY ===

            # Bước 9: Về màn hình chính để bắt đầu workflow addwallet
            print("Bước 9: Về màn hình chính để bắt đầu workflow addwallet")
            self.press_key(3)  # KEYCODE_HOME = 3
            print("✅ Đã về màn hình chính")
            self._random_sleep(1.0, 2.0, "Chờ sau khi về màn hình chính")

            # Bước 10: Vuốt lên để hiển thị menu ứng dụng
            print("Bước 10: Vuốt lên để hiển thị menu ứng dụng")
            screen_height = 1920  # Chiều cao màn hình mặc định
            screen_width = 1080   # Chiều rộng màn hình mặc định

            # Vuốt từ giữa màn hình lên trên
            self.swipe(screen_width // 2, screen_height * 2 // 3, screen_width // 2, screen_height // 3, 300)
            print("✅ Đã vuốt lên để vào menu ứng dụng")
            self._random_sleep(1.0, 2.0, "Chờ sau khi vuốt vào menu ứng dụng")

            # Bước 11: Tìm Google Wallet
            print("Bước 11: Tìm và tap vào biểu tượng Google Wallet")
            wallet_found = False
            max_swipe_attempts = 3  # Tối đa 3 lần vuốt để tìm

            for attempt in range(max_swipe_attempts):
                # Kiểm tra cờ dừng
                if stop_flag_func and stop_flag_func():
                    print("⚠️ Đã nhận tín hiệu dừng - Dừng tìm kiếm Google Wallet")
                    return False

                print(f"🔍 Lần thử {attempt + 1}/{max_swipe_attempts}: Tìm kiếm Google Wallet...")

                # Cập nhật UI để tránh đóng băng
                try:
                    from PyQt5.QtWidgets import QApplication
                    QApplication.processEvents()
                except:
                    pass

                # Thử tìm addwallet.png
                if os.path.exists(self.addwallet_template_path):
                    print(f"🔍 Tìm kiếm addwallet.png...")
                    wallet_found = self.find_image_and_tap(self.addwallet_template_path)
                    if wallet_found:
                        print(f"✅ Đã tìm thấy và tap vào addwallet.png ở lần thử {attempt + 1}")
                        break

                # Nếu không tìm thấy, thử tìm wallet.png (nếu có)
                wallet_template_path = os.path.join(self.template_dir, "wallet.png")
                if not wallet_found and os.path.exists(wallet_template_path):
                    print(f"🔍 Tìm kiếm wallet.png...")
                    wallet_found = self.find_image_and_tap(wallet_template_path)
                    if wallet_found:
                        print(f"✅ Đã tìm thấy và tap vào wallet.png ở lần thử {attempt + 1}")
                        break

                # Nếu chưa tìm thấy và chưa phải lần cuối, vuốt lên để tìm tiếp
                if not wallet_found and attempt < max_swipe_attempts - 1:
                    print(f"⚠️ Chưa tìm thấy Google Wallet, vuốt lên để tìm tiếp...")
                    self.swipe(screen_width // 2, screen_height * 2 // 3, screen_width // 2, screen_height // 3, 300)
                    print(f"✅ Đã vuốt lên lần {attempt + 2}")
                    self._random_sleep(1.0, 2.0, f"Chờ sau khi vuốt lần {attempt + 2}")
                    # Cập nhật UI sau khi vuốt
                    try:
                        from PyQt5.QtWidgets import QApplication
                        QApplication.processEvents()
                    except:
                        pass
                elif not wallet_found:
                    print(f"⚠️ Đã thử {max_swipe_attempts} lần vuốt nhưng vẫn chưa tìm thấy Google Wallet")

            # Nếu vẫn không tìm thấy Google Wallet, dừng lại
            if not wallet_found:
                print("❌ THẤT BẠI: Không tìm thấy biểu tượng Google Wallet sau khi thử tìm kiếm")
                print("❌ Nguyên nhân: Thiết bị có thể chưa cài đặt Google Wallet hoặc biểu tượng bị ẩn")
                print("❌ KẾT QUẢ: Thêm thẻ thất bại - Không thể mở Google Wallet")
                return False

            # Đợi ứng dụng khởi động
            self._random_sleep(12.0, 13.0, "Chờ Google Wallet khởi động hoàn toàn")

            setup_success = False  # Cờ để xác định xem đã thực hiện setup thành công chưa

            # Thực hiện các bước setup (tối đa 2 lần)
            for repeat_index in range(3):
                print(f"\n🔁 Lặp lần {repeat_index + 1}")

                # Thực hiện các bước theo thứ tự đơn giản
                print("🔄 Thực hiện các bước setup Google Wallet...")

                # Bước 1: Thử tìm view_wallet
                print("Bước 12: Tìm và tap vào view_wallet.png")
                view_wallet_path = os.path.join(self.template_dir, "view_wallet.png")
                if os.path.exists(view_wallet_path):
                    self.find_image_and_tap(view_wallet_path)
                    self._random_sleep(2.0, 3.0, "Chờ sau khi tap view_wallet")

                # Bước 2: Thử tìm got_it
                print("Bước 12.1: Tìm và tap vào got_it_trang_wallet.png (nếu xuất hiện)")
                got_it_path = os.path.join(self.template_dir, "got_it_trang_wallet.png")
                if os.path.exists(got_it_path):
                    self.find_image_and_tap(got_it_path)
                    self._random_sleep(2.0, 3.0, "Chờ sau khi tap got_it")

                # Bước 3: Thử tìm not_now
                print("Bước 13: Tìm và tap vào not_now.png (nếu xuất hiện)")
                not_now_path = os.path.join(self.template_dir, "not_now.png")
                if os.path.exists(not_now_path):
                    self.find_image_and_tap(not_now_path)
                    self._random_sleep(2.0, 3.0, "Chờ sau khi tap not_now")
                
                # Bước 4: Tìm tap vào add_wallet.png
                print("Bước 14: Tìm và tap vào add_wallet.png")
                add_wallet_path = os.path.join(self.template_dir, "add_wallet.png")
                if os.path.exists(add_wallet_path):
                    self.find_image_and_tap(add_wallet_path)
                    self._random_sleep(2.0, 3.0, "Chờ sau khi tap add_wallet")
                                 
                        
                # Bước 5 - QUAN TRỌNG: Kiểm tra credit_wallet.png hoặc payment_card.png
                print("Bước 14: Tìm credit_wallet.png hoặc payment_card.png trên màn hình")

                credit_wallet_path = os.path.join(self.template_dir, "credit_wallet.png")
                payment_card_path = os.path.join(self.template_dir, "payment_card.png")

                setup_success = False  # Biến theo dõi trạng thái thành công

                for attempt in range(3):  # Thử tối đa 3 lần
                    found_image = False

                    # Tìm credit_wallet.png
                    if os.path.exists(credit_wallet_path):
                        if self.find_image_on_screen(credit_wallet_path):
                            print("✅ Tìm thấy credit_wallet.png trên màn hình")
                            found_image = True

                    # Nếu chưa tìm thấy credit_wallet, tìm payment_card.png
                    if not found_image and os.path.exists(payment_card_path):
                        if self.find_image_on_screen(payment_card_path):
                            print("✅ Tìm thấy payment_card.png trên màn hình")
                            found_image = True

                    if found_image:
                        setup_success = True
                        print("✅ Tìm thấy ít nhất một ảnh, không cần tap, tiếp tục workflow")
                        break  # Thoát vòng lặp vì đã thành công

                    else:
                        print("❌ Không tìm thấy credit_wallet hoặc payment_card - Gọi view_wallet_back")

                        if stop_flag_func and stop_flag_func():
                            print("⚠️ Đã nhận tín hiệu dừng - không tiếp tục")
                            return False

                        print("🔄 Gọi view_wallet_back...")
                        if self.view_wallet_back(stop_flag_func):
                            print("✅ view_wallet_back thành công - Gọi lại execute_actions")

                            if retry_count >= 3:
                                print("❌ Quá số lần thử lại - dừng")
                                return False

                            return self.execute_actions(stop_flag_func, num_cards, retry_count + 1)
                        else:
                            print("❌ view_wallet_back thất bại")                            
                            return False

                # Cập nhật UI
                try:
                    from PyQt5.QtWidgets import QApplication
                    QApplication.processEvents()
                except:
                    pass

                # Nếu setup thành công, không cần lặp nữa
                if setup_success:
                    print("✅ credit_wallet đã xuất hiện, kết thúc vòng lặp.")
                    break

            # Sau 2 vòng lặp: kiểm tra nếu thất bại hoàn toàn
            if not setup_success:
                print("❌ THẤT BẠI: Không thể vào được vòng lặp thêm thẻ sau 3 lần thử")
                print("❌ Nguyên nhân: Không tìm thấy credit_wallet hoặc payment_card để bắt đầu thêm thẻ")
                print("❌ KẾT QUẢ: Thêm thẻ thất bại - Không thể bắt đầu quá trình thêm thẻ")
                return False

            # 👉 Nếu tới đây thì nghĩa là setup thành công, tiếp tục các bước sau...
            print("➡️ Tiếp tục các bước tiếp theo sau setup...")

            # Bước 15: Tìm và tap vào new_credit_wallet.png (riêng biệt để điều chỉnh timing)
            # Setup hoàn thành, bắt đầu vòng lặp thêm thẻ

            # Vòng lặp thêm thẻ dựa trên num_cards
            print(f"\n🔄 === Bắt đầu vòng lặp thêm {num_cards} thẻ ===")
            successful_cards = 0

            # Cập nhật tổng số thẻ và reset counter thất bại
            self.total_cards = num_cards
            self.consecutive_failures = 0  # Reset counter khi bắt đầu vòng lặp mới

            for card_index in range(num_cards):
                print(f"\n🔄 === Bắt đầu thêm thẻ {card_index + 1}/{num_cards} ===")

                # Cập nhật thẻ hiện tại TRƯỚC khi thêm (để hiển thị tiến trình)
                self.current_card = card_index + 1

                # ✅ CẬP NHẬT THÔNG TIN THẺ NGAY Ở ĐẦU VÒNG LẶP
                # Lấy thông tin thẻ tiếp theo để hiển thị đúng trên UI (không xóa khỏi danh sách)
                next_card_info = self.peek_next_card()
                if next_card_info:
                    print(f"📋 Thẻ sẽ thêm: {next_card_info['card_number'][-4:]} ({next_card_info['month']}/{next_card_info['year']})")
                    # Cập nhật current_card_info để hiển thị trên UI
                    self.current_card_info = next_card_info
                else:
                    print("⚠️ Không có thẻ nào khả dụng để thêm")
                    break

                # Cập nhật trạng thái theo thời gian thực (với thông tin thẻ mới)
                self.update_status_realtime()

                # Kiểm tra cờ dừng với debug logging
                if stop_flag_func and stop_flag_func():
                    print(f"🛑 STOP SIGNAL RECEIVED tại thẻ {card_index + 1}/{num_cards}")
                    print(f"🛑 Đang dừng quá trình addwallet...")
                    break

                # Thực hiện quy trình thêm thẻ
                card_success = self.add_single_card(stop_flag_func)

                if card_success:
                    successful_cards += 1
                    self.consecutive_failures = 0  # Reset counter khi thành công
                    print(f"✅ Thành công thêm thẻ {card_index + 1}/{num_cards}")
                    print(f"🔄 Reset counter thất bại liên tiếp về 0")
                else:
                    self.consecutive_failures += 1  # Tăng counter khi thất bại
                    print(f"❌ Thất bại thêm thẻ {card_index + 1}/{num_cards}")
                    print(f"⚠️ Thất bại liên tiếp: {self.consecutive_failures}/{self.max_consecutive_failures}")

                    # Kiểm tra nếu đạt giới hạn thất bại liên tiếp
                    if self.consecutive_failures >= self.max_consecutive_failures:
                        print(f"🛑 ĐÃ ĐẠT GIỚI HẠN {self.max_consecutive_failures} LẦN THẤT BẠI LIÊN TIẾP!")
                        print(f"🛑 DỪNG QUÁ TRÌNH THÊM THẺ ĐỂ TRÁNH LÃNG PHÍ THỜI GIAN")
                        print(f"🛑 KẾT QUẢ: {successful_cards}/{num_cards} thẻ thành công trước khi dừng")
                        break  # Thoát khỏi vòng lặp

                # Cập nhật trạng thái sau khi hoàn thành thẻ
                self.update_status_realtime()

                # Nếu không phải thẻ cuối cùng, chuẩn bị cho thẻ tiếp theo
                if card_index < num_cards - 1:
                    print(f"🔄 Chuẩn bị thêm thẻ tiếp theo...")
                    # Quay lại bước credit_wallet để thêm thẻ tiếp theo
                    self._random_sleep(2.0, 3.0, "Chờ trước khi thêm thẻ tiếp theo")

            # Bước cuối: Mở recent apps và tắt app
            print("\nBước cuối: Mở recent apps và tắt app")
            self._random_sleep(4.0, 5.0, "Chờ trước khi tắt app")

            # Bước cuối.1: Mở recent apps (key 187)
            print("Bước cuối.1: Mở recent apps")
            self.press_key(187)  # KEYCODE_APP_SWITCH = 187
            self._random_sleep(1.0, 2.0, "Chờ sau khi mở recent apps")

            # Bước cuối.2: Vuốt lên 2 lần để tắt app
            print("Bước cuối.2: Vuốt lên 2 lần để tắt app")
            screen_height = 1920  # Chiều cao màn hình mặc định
            screen_width = 1080   # Chiều rộng màn hình mặc định

            # Vuốt lên lần 1
            print("Vuốt lên lần 1")
            self.swipe(screen_width // 2, screen_height * 2 // 3, screen_width // 2, screen_height // 4, 300)
            self._random_sleep(0.5, 1.0, "Chờ sau vuốt lần 1")

            # Vuốt lên lần 2
            print("Vuốt lên lần 2")
            self.swipe(screen_width // 2, screen_height * 2 // 3, screen_width // 2, screen_height // 4, 300)
            self._random_sleep(1.0, 1.5, "Chờ sau vuốt lần 2")

            # Bước cuối.3: Về màn hình chính
            print("Bước cuối.3: Về màn hình chính")
            self.press_key(3)  # KEYCODE_HOME = 3
            self._random_sleep(1.0, 2.0, "Chờ sau khi về màn hình chính")

            print(f"\n=== Hoàn thành: Đã thêm {successful_cards}/{num_cards} thẻ thành công trên thiết bị {self.serial} ===\n")

            # Thông báo kết quả chi tiết
            if self.consecutive_failures >= self.max_consecutive_failures:
                print(f"🛑 DỪNG DO THẤT BẠI LIÊN TIẾP: Đã dừng sau {self.max_consecutive_failures} lần thất bại liên tiếp")
                print(f"📊 KẾT QUẢ CUỐI CÙNG: {successful_cards}/{num_cards} thẻ thành công trước khi dừng")
                if successful_cards > 0:
                    return True  # Vẫn coi là thành công nếu có ít nhất 1 thẻ
                else:
                    return False
            elif successful_cards == num_cards:
                print(f"✅ THÀNH CÔNG HOÀN TOÀN: Đã thêm đủ {successful_cards}/{num_cards} thẻ vào ví")
                return True
            elif successful_cards > 0:
                print(f"⚠️ THÀNH CÔNG MỘT PHẦN: Đã thêm {successful_cards}/{num_cards} thẻ vào ví")
                return True
            else:
                print(f"❌ THẤT BẠI HOÀN TOÀN: Không thể thêm thẻ nào vào ví (0/{num_cards})")
                return False

        except Exception as e:
            print(f"❌ Lỗi khi thực hiện chuỗi hành động: {str(e)}")
            return False

    def view_wallet_back(self, stop_flag_func=None):
        """
        Xử lý khi view_wallet bị treo - thực hiện chuỗi hành động khôi phục

        :param stop_flag_func: Function để kiểm tra cờ dừng (optional)
        :return: True nếu thành công, False nếu thất bại
        """
        try:
            print(f"\n🔄 === Bắt đầu xử lý view_wallet_back trên thiết bị {self.serial} ===")

            # Kiểm tra cờ dừng
            if stop_flag_func and stop_flag_func():
                print("⚠️ Đã nhận tín hiệu dừng - Dừng view_wallet_back")
                return False

            # Bước 1: Mở navigation (key 187) và vuốt lên 1 lần để tắt app wallet
            print("Bước 1: Mở navigation và tắt app wallet")
            self.press_key(187)  # KEYCODE_APP_SWITCH = 187
            self._random_sleep(1.0, 2.0, "Chờ sau khi mở navigation")

            # Vuốt lên 1 lần để tắt app wallet
            screen_height = 1920  # Chiều cao màn hình mặc định
            screen_width = 1080   # Chiều rộng màn hình mặc định
            print("Vuốt lên 1 lần để tắt app wallet")
            self.swipe(screen_width // 2, screen_height * 2 // 3, screen_width // 2, screen_height // 4, 300)
            self._random_sleep(1.0, 2.0, "Chờ sau khi vuốt lên")
               # Vuốt lên 1 lần nữa để tắt app chrome
            screen_height = 1920  # Chiều cao màn hình mặc định
            screen_width = 1080   # Chiều rộng màn hình mặc định
            print("Vuốt lên 1 lần để tắt app wallet")
            self.swipe(screen_width // 2, screen_height * 2 // 3, screen_width // 2, screen_height // 4, 300)
            self._random_sleep(1.0, 2.0, "Chờ sau khi vuốt lên")


            # Kiểm tra cờ dừng
            if stop_flag_func and stop_flag_func():
                print("⚠️ Đã nhận tín hiệu dừng - Dừng view_wallet_back")
                return False

            # Bước 2: Vuốt từ trên xuống để mở thông báo (từ vị trí 479,35)
            print("Bước 2: Vuốt từ trên xuống để mở thông báo")
            start_x = 479
            start_y = 35
            end_x = 479
            end_y = screen_height // 2  # Vuốt xuống giữa màn hình
            self.swipe(start_x, start_y, end_x, end_y, 500)
            self._random_sleep(2.0, 3.0, "Chờ sau khi mở thông báo")

            # Kiểm tra cờ dừng
            if stop_flag_func and stop_flag_func():
                print("⚠️ Đã nhận tín hiệu dừng - Dừng view_wallet_back")
                return False

            # Bước 3: Tap vào vị trí 507,780 và đợi 6s
            print("Bước 3: Tap vào vị trí 507,780")
            self.tap(507, 780)
            self._random_sleep(6.0, 6.5, "Chờ 6 giây sau khi tap")

            # Kiểm tra cờ dừng
            if stop_flag_func and stop_flag_func():
                print("⚠️ Đã nhận tín hiệu dừng - Dừng view_wallet_back")
                return False
            # Tìm tap vào got_it_view.png
            print("Bước 4: Tìm và tap vào got_it_view.png")
            got_it_view_path = os.path.join(self.template_dir, "got_it_view.png")
            if os.path.exists(got_it_view_path):
                self.find_image_and_tap(got_it_view_path)
                self._random_sleep(2.0, 3.0, "Chờ sau khi tap got_it_view")
            
            # Bước 4 tìm tap vào yes_it_was nếu có
            print("Bước 5: Tìm và tap vào yes_it_was.png")
            yes_it_was_path = os.path.join(self.template_dir, "yes_it_was.png")
            if os.path.exists(yes_it_was_path):
                self.find_image_and_tap(yes_it_was_path)
                self._random_sleep(2.0, 3.0, "Chờ sau khi tap yes_it_was")
            
            # Bước cuối về trang chủ
            print("Bước 6: Về trang chủ")
            self.press_key(3)  # KEYCODE_HOME = 3
            self._random_sleep(1.0, 2.0, "Chờ sau khi về trang chủ")

            # Kiểm tra cờ dừng
            if stop_flag_func and stop_flag_func():
                print("⚠️ Đã nhận tín hiệu dừng - Dừng view_wallet_back")
                return False

            print("✅ Hoàn thành view_wallet_back - Sẽ gọi lại execute_actions")
            return True

        except Exception as e:
            print(f"❌ Lỗi trong view_wallet_back: {str(e)}")
            return False

    def set_max_consecutive_failures(self, max_failures):
        """
        Thiết lập số lần thất bại liên tiếp tối đa
        :param max_failures: Số lần thất bại liên tiếp tối đa (mặc định 10)
        """
        self.max_consecutive_failures = max_failures
        print(f"🔧 Đã thiết lập giới hạn thất bại liên tiếp: {max_failures} lần")

    def get_consecutive_failures_info(self):
        """
        Lấy thông tin về số lần thất bại liên tiếp hiện tại
        :return: tuple (consecutive_failures, max_consecutive_failures)
        """
        return (self.consecutive_failures, self.max_consecutive_failures)

    def reset_consecutive_failures(self):
        """
        Reset counter thất bại liên tiếp về 0
        """
        self.consecutive_failures = 0
        print(f"🔄 Đã reset counter thất bại liên tiếp về 0")
