"""
License Manager Dialog - <PERSON>iao diện quản lý license key tư<PERSON><PERSON> tự KRotator Pro
"""

import sys
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                             QLineEdit, QPushButton, QGroupBox, QTextEdit,
                             QApplication, QMessageBox, QCheckBox)
from PyQt5.QtCore import Qt, QSettings
from PyQt5.QtGui import QFont, QIcon
import license_manager


class LicenseManagerDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("AutoK - License Manager")
        self.setFixedSize(500, 400)
        # Thiết lập icon cho dialog
        try:
            import os
            icon_path = os.path.join("icon", "icon.ico")
            if os.path.exists(icon_path):
                self.setWindowIcon(QIcon(icon_path))
            else:
                # Fallback nếu không tì<PERSON> thấy icon
                self.setWindowIcon(QIcon())
        except Exception as e:
            print(f"⚠️ Không thể load icon: {e}")
            self.setWindowIcon(QIcon())
        
        # Thiết lập layout chính
        self.setup_ui()
        
        # Load thông tin license hiện tại
        self.load_current_license()
        
    def setup_ui(self):
        """Thiết lập giao diện người dùng"""
        layout = QVBoxLayout()
        
        # === TIÊU ĐỀ ===
        title_layout = QHBoxLayout()
        
        # Icon (có thể thêm icon sau)
        title_label = QLabel("🔐 License Key Management")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        layout.addLayout(title_layout)
        
        # === NHẬP LICENSE KEY ===
        key_group = QGroupBox("License Key Input")
        key_layout = QVBoxLayout()
        
        # Label và input
        key_input_layout = QHBoxLayout()
        key_label = QLabel("License Key:")
        self.key_input = QLineEdit()
        self.key_input.setPlaceholderText("Nhập license key của bạn...")
        
        key_input_layout.addWidget(key_label)
        key_input_layout.addWidget(self.key_input)
        
        # Button validate
        self.validate_button = QPushButton("Validate License")
        self.validate_button.clicked.connect(self.validate_license)
        self.validate_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 9px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)
        
        key_layout.addLayout(key_input_layout)
        key_layout.addWidget(self.validate_button)
        key_group.setLayout(key_layout)
        layout.addWidget(key_group)
        
        # === THÔNG TIN LICENSE ===
        info_group = QGroupBox("License Information")
        info_layout = QVBoxLayout()
        
        # Tạo text area để hiển thị thông tin
        self.info_display = QTextEdit()
        self.info_display.setReadOnly(True)
        self.info_display.setMaximumHeight(150)
        self.info_display.setStyleSheet("""
            QTextEdit {
                background-color: #f5f5f5;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 8px;
                font-family: 'Consolas', 'Monaco', monospace;
            }
        """)
        
        info_layout.addWidget(self.info_display)
        info_group.setLayout(info_layout)
        layout.addWidget(info_group)
        
        # === BUTTONS ===
        button_layout = QHBoxLayout()

        # Thêm stretch để đẩy buttons về giữa
        button_layout.addStretch()

        # Save License button
        self.save_button = QPushButton("💾 Save License")
        self.save_button.clicked.connect(self.save_license)
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 9px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 13px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)

        # Khoảng cách giữa 2 button
        button_layout.addWidget(self.save_button)
        button_layout.addSpacing(15)

        # Close button
        self.close_button = QPushButton("❌ Close")
        self.close_button.clicked.connect(self.reject)
        self.close_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 9px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 13px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
        """)

        button_layout.addWidget(self.close_button)

        # Thêm stretch để đẩy buttons về giữa
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
        # === STATUS BAR ===
        status_layout = QHBoxLayout()

        # Label "Status:"
        status_title = QLabel("Status:")
        status_title.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #333;
                padding: 4px 0px;
            }
        """)

        # Status indicator
        self.status_label = QLabel("Loaded saved license - Valid")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #4CAF50;
                padding: 4px 8px;
                font-weight: bold;
                font-size: 12px;
            }
        """)

        status_layout.addWidget(status_title)
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()  # Đẩy về bên trái

        layout.addLayout(status_layout)
        
        self.setLayout(layout)
        
    def load_current_license(self):
        """Load thông tin license hiện tại"""
        try:
            settings = QSettings("AutoK", "License")
            saved_key = settings.value("license_key", "")
            
            if saved_key:
                self.key_input.setText(saved_key)
                # Kiểm tra license
                result = license_manager.check_license(saved_key)

                # Xử lý kết quả trả về (có thể là 3 hoặc 4 phần tử)
                if len(result) == 4:
                    valid, message, features, user_info = result
                else:
                    valid, message, features = result
                    user_info = {}

                if valid:
                    self.display_license_info(saved_key, features, message, user_info)
                    self.status_label.setText("✅ License Valid")
                    self.status_label.setStyleSheet("""
                        QLabel {
                            color: #4CAF50;
                            padding: 4px 8px;
                            font-weight: bold;
                            font-size: 12px;
                        }
                    """)
                else:
                    self.info_display.setText("❌ License không hợp lệ hoặc đã hết hạn")
                    self.status_label.setText("❌ License Invalid")
                    self.status_label.setStyleSheet("""
                        QLabel {
                            color: #f44336;
                            padding: 4px 8px;
                            font-weight: bold;
                            font-size: 12px;
                        }
                    """)
            else:
                self.info_display.setText("Chưa có license key. Vui lòng nhập key để kích hoạt.")
                self.status_label.setText("⚠️ No License")
                self.status_label.setStyleSheet("""
                    QLabel {
                        color: #FF9800;
                        padding: 4px 8px;
                        font-weight: bold;
                        font-size: 12px;
                    }
                """)
                
        except Exception as e:
            self.info_display.setText(f"Lỗi khi load license: {str(e)}")
            
    def display_license_info(self, key, features, message, user_info=None):
        """Hiển thị thông tin license"""
        try:
            # Lấy UUID máy
            machine_uuid = license_manager.get_machine_uuid()

            # Sử dụng thông tin từ user_info nếu có, nếu không dùng giá trị mặc định
            if user_info:
                user_name = user_info.get('user', 'AutoK User')
                user_email = user_info.get('email', '<EMAIL>')
                max_devices = user_info.get('max_devices', 30)
                expires = user_info.get('expires', '2025-07-31')
            else:
                user_name = 'AutoK User'
                user_email = '<EMAIL>'
                max_devices = 30
                expires = '2025-07-31'

            # Tính toán số ngày còn lại
            try:
                from datetime import datetime
                if expires and expires != '2025-07-31':
                    # Thử các format ngày khác nhau
                    for date_format in ['%Y-%m-%d', '%d/%m/%Y', '%m/%d/%Y']:
                        try:
                            expiry_date = datetime.strptime(expires, date_format)
                            today = datetime.today()
                            days_left = (expiry_date - today).days
                            break
                        except ValueError:
                            continue
                    else:
                        days_left = 57  # Giá trị mặc định nếu không parse được
                else:
                    days_left = 57  # Giá trị mặc định
            except:
                days_left = 57

            # Format thông tin giống KRotator Pro với dữ liệu thực
            info_text = f"""✅ License Valid
👤 User: {user_name}
📧 Email: {user_email}
📦 Plan: {features}
🖥️ Max Devices: {max_devices}
📅 Expires: {expires}
⏰ Days Remaining: {days_left}

🔑 License Key: {key[:8]}...{key[-8:] if len(key) > 16 else key}
🆔 Machine UUID: {machine_uuid[:8]}...{machine_uuid[-8:]}

ℹ️ Status: {message}"""

            self.info_display.setText(info_text)

        except Exception as e:
            self.info_display.setText(f"Lỗi hiển thị thông tin: {str(e)}")
            
    def validate_license(self):
        """Validate license key"""
        key = self.key_input.text().strip()
        if not key:
            QMessageBox.warning(self, "Lỗi", "Vui lòng nhập license key!")
            return
            
        # Disable button và hiển thị trạng thái
        self.validate_button.setEnabled(False)
        self.validate_button.setText("Đang kiểm tra...")
        QApplication.processEvents()
        
        try:
            # Kiểm tra license
            result = license_manager.check_license(key)

            # Xử lý kết quả trả về (có thể là 3 hoặc 4 phần tử)
            if len(result) == 4:
                valid, message, features, user_info = result
            else:
                valid, message, features = result
                user_info = {}

            if valid:
                self.display_license_info(key, features, message, user_info)
                self.status_label.setText("✅ License Valid")
                self.status_label.setStyleSheet("""
                    QLabel {
                        color: #4CAF50;
                        padding: 4px 8px;
                        font-weight: bold;
                        font-size: 12px;
                    }
                """)
                QMessageBox.information(self, "Thành công", f"License hợp lệ!\n\n{message}")
            else:
                self.info_display.setText(f"❌ License không hợp lệ: {message}")
                self.status_label.setText("❌ License Invalid")
                self.status_label.setStyleSheet("""
                    QLabel {
                        color: #f44336;
                        padding: 4px 8px;
                        font-weight: bold;
                        font-size: 12px;
                    }
                """)
                QMessageBox.critical(self, "Lỗi", f"License không hợp lệ: {message}")
                
        except Exception as e:
            self.info_display.setText(f"Lỗi khi kiểm tra license: {str(e)}")
            QMessageBox.critical(self, "Lỗi", f"Lỗi khi kiểm tra license: {str(e)}")
        finally:
            # Khôi phục button
            self.validate_button.setEnabled(True)
            self.validate_button.setText("✅ Validate License")
            
    def save_license(self):
        """Lưu license key"""
        key = self.key_input.text().strip()
        if not key:
            QMessageBox.warning(self, "Lỗi", "Vui lòng nhập license key!")
            return
            
        try:
            # Kiểm tra license trước khi lưu
            result = license_manager.check_license(key)

            # Xử lý kết quả trả về (có thể là 3 hoặc 4 phần tử)
            if len(result) == 4:
                valid, message, features, user_info = result
            else:
                valid, message, features = result
                user_info = {}

            if valid:
                # Lưu vào settings
                settings = QSettings("AutoK", "License")
                settings.setValue("license_key", key)

                QMessageBox.information(self, "Thành công", "Đã lưu license key thành công!")
                self.accept()  # Đóng dialog
            else:
                QMessageBox.critical(self, "Lỗi", f"license không hợp lệ: {message}")
                
        except Exception as e:
            QMessageBox.critical(self, "Lỗi", f"Lỗi khi lưu license: {str(e)}")
            



# Test dialog
if __name__ == "__main__":
    app = QApplication(sys.argv)
    dialog = LicenseManagerDialog()
    dialog.show()
    sys.exit(app.exec_())
