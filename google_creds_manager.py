#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script để quản lý file google_creds đã được mã hóa
Tương thích với format mã hóa hiện tại
"""

import os
import json
import base64
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

class GoogleCredsManager:
    def __init__(self, password: str = None):
        """
        Khởi tạo manager với password
        """
        self.password = password
        self.creds_file = "google_creds"
        
    def _generate_key_from_password(self, password: str, salt: bytes = None) -> bytes:
        """
        Tạo key từ password (tương thích với format hiện tại)
        """
        if salt is None:
            # Sử dụng salt mặc định nếu không có
            salt = b'salt_1234567890'  # 16 bytes
            
        password_bytes = password.encode()
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password_bytes))
        return key
    
    def decrypt_creds(self, password: str = None) -> dict:
        """
        Giải mã file google_creds hiện tại
        """
        if password is None:
            password = self.password
            
        if password is None:
            raise ValueError("Cần password để giải mã!")
            
        try:
            # Đọc file đã mã hóa
            with open(self.creds_file, 'r') as f:
                encrypted_data = f.read().strip()
            
            # Thử giải mã trực tiếp (nếu đã là Fernet token)
            try:
                # Tạo key từ password
                key = self._generate_key_from_password(password)
                fernet = Fernet(key)
                
                # Giải mã
                decrypted_bytes = fernet.decrypt(encrypted_data.encode())
                decrypted_text = decrypted_bytes.decode()
                
                # Parse JSON
                return json.loads(decrypted_text)
                
            except Exception as e1:
                # Thử cách khác - có thể data đã được encode base64
                try:
                    # Decode base64 trước
                    decoded_data = base64.b64decode(encrypted_data)
                    
                    key = self._generate_key_from_password(password)
                    fernet = Fernet(key)
                    
                    decrypted_bytes = fernet.decrypt(decoded_data)
                    decrypted_text = decrypted_bytes.decode()
                    
                    return json.loads(decrypted_text)
                    
                except Exception as e2:
                    print(f"Lỗi giải mã cách 1: {e1}")
                    print(f"Lỗi giải mã cách 2: {e2}")
                    raise Exception("Không thể giải mã file. Kiểm tra lại password!")
                    
        except FileNotFoundError:
            raise Exception(f"Không tìm thấy file {self.creds_file}")
        except Exception as e:
            raise Exception(f"Lỗi khi đọc file: {e}")
    
    def encrypt_creds(self, creds_data: dict, password: str, output_file: str = None):
        """
        Mã hóa credentials data
        """
        if output_file is None:
            output_file = self.creds_file + "_new"
            
        try:
            # Chuyển dict thành JSON string
            json_string = json.dumps(creds_data, indent=2)
            
            # Tạo key từ password
            key = self._generate_key_from_password(password)
            fernet = Fernet(key)
            
            # Mã hóa
            encrypted_data = fernet.encrypt(json_string.encode())
            
            # Lưu file (giống format hiện tại)
            with open(output_file, 'w') as f:
                f.write(encrypted_data.decode())
            
            print(f"✅ Đã mã hóa thành công: {output_file}")
            return True
            
        except Exception as e:
            print(f"❌ Lỗi khi mã hóa: {e}")
            return False
    
    def re_encrypt_with_new_password(self, old_password: str, new_password: str):
        """
        Đổi password mã hóa
        """
        try:
            # Giải mã với password cũ
            creds_data = self.decrypt_creds(old_password)
            
            # Backup file cũ
            backup_file = self.creds_file + "_backup"
            if os.path.exists(self.creds_file):
                os.rename(self.creds_file, backup_file)
                print(f"📁 Đã backup file cũ: {backup_file}")
            
            # Mã hóa lại với password mới
            success = self.encrypt_creds(creds_data, new_password, self.creds_file)
            
            if success:
                print("✅ Đã đổi password thành công!")
                
                # Hỏi có muốn xóa backup không
                response = input("Xóa file backup? (y/n): ")
                if response.lower() == 'y':
                    os.remove(backup_file)
                    print("🗑️ Đã xóa file backup")
            else:
                # Khôi phục file cũ nếu thất bại
                os.rename(backup_file, self.creds_file)
                print("❌ Đổi password thất bại. Đã khôi phục file cũ.")
                
        except Exception as e:
            print(f"❌ Lỗi: {e}")
    
    def verify_password(self, password: str) -> bool:
        """
        Kiểm tra password có đúng không
        """
        try:
            self.decrypt_creds(password)
            return True
        except:
            return False
    
    def show_creds_info(self, password: str):
        """
        Hiển thị thông tin credentials (ẩn sensitive data)
        """
        try:
            creds = self.decrypt_creds(password)
            
            print("📋 Thông tin Google Credentials:")
            print("=" * 40)
            
            # Hiển thị thông tin an toàn
            safe_fields = [
                'type', 'project_id', 'client_id', 'auth_uri', 
                'token_uri', 'auth_provider_x509_cert_url'
            ]
            
            for field in safe_fields:
                if field in creds:
                    print(f"{field}: {creds[field]}")
            
            # Hiển thị các field sensitive (ẩn bớt)
            sensitive_fields = ['private_key_id', 'private_key', 'client_email']
            for field in sensitive_fields:
                if field in creds:
                    value = str(creds[field])
                    if len(value) > 20:
                        masked = value[:10] + "..." + value[-10:]
                    else:
                        masked = value[:5] + "..." + value[-3:]
                    print(f"{field}: {masked}")
                    
        except Exception as e:
            print(f"❌ Lỗi: {e}")

def main():
    """
    Hàm chính
    """
    print("🔐 Google Credentials Manager")
    print("=" * 40)
    
    manager = GoogleCredsManager()
    
    if not os.path.exists("google_creds"):
        print("❌ Không tìm thấy file google_creds")
        return
    
    while True:
        print("\nChọn hành động:")
        print("1. Kiểm tra password")
        print("2. Xem thông tin credentials")
        print("3. Đổi password")
        print("4. Tạo file mã hóa mới từ JSON")
        print("5. Thoát")
        
        choice = input("Nhập lựa chọn (1-5): ")
        
        if choice == "1":
            password = input("Nhập password: ")
            if manager.verify_password(password):
                print("✅ Password đúng!")
            else:
                print("❌ Password sai!")
                
        elif choice == "2":
            password = input("Nhập password: ")
            manager.show_creds_info(password)
            
        elif choice == "3":
            old_password = input("Nhập password cũ: ")
            new_password = input("Nhập password mới: ")
            manager.re_encrypt_with_new_password(old_password, new_password)
            
        elif choice == "4":
            json_file = input("Nhập đường dẫn file JSON: ")
            if os.path.exists(json_file):
                with open(json_file, 'r') as f:
                    creds_data = json.load(f)
                password = input("Nhập password để mã hóa: ")
                manager.encrypt_creds(creds_data, password)
            else:
                print("❌ Không tìm thấy file JSON")
                
        elif choice == "5":
            break
        else:
            print("❌ Lựa chọn không hợp lệ!")

if __name__ == "__main__":
    main()
