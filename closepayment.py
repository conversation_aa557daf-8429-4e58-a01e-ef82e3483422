import os
import sys
import subprocess
import time
import random
import cv2
import numpy as np

# Import path_manager để quản lý đường dẫn
import path_manager

# Biến toàn cục để lưu đường dẫn ADB
ADB_PATH = "adb"  # Giá trị mặc định

class DeviceActions:
    def __init__(self, serial, device_index=0, total_devices=1, adb_path=None, password=None, auth_secret=None):
        """
        Khởi tạo đối tượng DeviceActions.
        :param serial: Số serial của thiết bị.
        :param device_index: Chỉ số thiết bị trong danh sách thiết bị (để phân phối email).
        :param total_devices: Tổng số thiết bị (để phân phối email).
        :param adb_path: Đường dẫn đến file adb.exe
        :param password: Mật khẩu từ giao diện (ưu tiên cao nhất)
        :param auth_secret: M<PERSON> xá<PERSON> thực từ giao diện (ưu tiên cao nhất)
        """
        self.serial = serial
        self.password = password or ""  # Mật khẩu để nhập khi cần (ưu tiên từ giao diện)
        self.auth_secret = auth_secret or ""  # Mã xác thực từ giao diện (ưu tiên cao nhất)
        self.device_index = device_index  # Chỉ số thiết bị
        self.total_devices = total_devices  # Tổng số thiết bị

        # Kiểm tra mật khẩu từ giao diện
        if self.password:
            masked_password = self.password[:2] + '*' * (len(self.password) - 2) if len(self.password) > 2 else '*' * len(self.password)
            print(f"✅ Đã nhận mật khẩu từ giao diện: {masked_password}")
        else:
            print("ℹ️ Không có mật khẩu từ giao diện, sẽ tải từ file mail.txt")

        # Cập nhật biến toàn cục ADB_PATH nếu được cung cấp
        global ADB_PATH
        if adb_path:
            ADB_PATH = adb_path
            print(f"✅ Đã cập nhật đường dẫn ADB: {ADB_PATH}")

        # Lấy đường dẫn đến thư mục template
        template_dir = path_manager.get_template_path()

        # Tải mật khẩu từ file mail.txt nếu không có mật khẩu từ giao diện (TRƯỚC khi setup template)
        if not self.password:
            self.load_password_from_mail()

        # Đường dẫn đến các file template
        self.template_dir = template_dir
        self.chrome_template_path = os.path.join(template_dir, "chrome.png")
        self.continue_template_path = os.path.join(template_dir, "continue.png")
        self.yes_im_in_template_path = os.path.join(template_dir, "yes_im_in.png")
        self.yes_im_in_trang_template_path = os.path.join(template_dir, "yes_im_in_trang.png")
        self.got_it_template_path = os.path.join(template_dir, "got_it.png")
        self.search_template_path = os.path.join(template_dir, "search.png")
        self.ver_that_template_path = os.path.join(template_dir, "ver_that.png")
        self.menu_template_path = os.path.join(template_dir, "menu.png")
        self.setting_template_path = os.path.join(template_dir, "setting.png")
        self.close_payment_template_path = os.path.join(template_dir, "close_payment.png")
        self.continue_close_template_path = os.path.join(template_dir, "continue_close.png")
        self.close_payment_profile_template_path = os.path.join(template_dir, "close_payment_profile.png")
        self.more_template_path = os.path.join(template_dir, "more.png")
        self.search2_template_path = os.path.join(template_dir, "search2.png")

        # Các file template tùy chọn
        self.verify_it_you_template_path = os.path.join(template_dir, "verify_it_you.png")
        self.use_a_difer_template_path = os.path.join(template_dir, "use_a_difer.png")
        self.cancel_template_path = os.path.join(template_dir, "cancel.png")
        self.try_another_way_template_path = os.path.join(template_dir, "try_another_way.png")
        self.enter_your_pass_template_path = os.path.join(template_dir, "enter_your_pass.png")
        self.continue_ver_template_path = os.path.join(template_dir, "continue_ver.png")

        # Thêm các file template mới cho bước sau khi nhập mật khẩu
        self.why_are_you_template_path = os.path.join(template_dir, "why_are_you.png")
        self.i_dont_want_template_path = os.path.join(template_dir, "i_dont_want.png")
        self.i_have_had_template_path = os.path.join(template_dir, "i_have_had.png")
         # Thêm các file template mới
        self.tunit_on_template_path = os.path.join(template_dir, "tunit_on.png")
        self.git_it_trang_template_path = os.path.join(template_dir, "git_it_trang.png")
        self.signed_in_template_path = os.path.join(template_dir, "signed_in.png")
        self.authen_ads_template_path = os.path.join(template_dir, "authen_ads.png")

    def load_password_from_mail(self):
        """
        Đọc mật khẩu từ file mail.txt dựa trên chỉ số thiết bị
        Format của file: email|password|authentication
        """
        try:
            # Danh sách các vị trí có thể chứa file mail.txt
            possible_paths = []

            # Kiểm tra xem có đang chạy từ file exe không
            is_frozen = getattr(sys, 'frozen', False)
            print(f"🔍 load_password_from_mail: is_frozen = {is_frozen}")

            # Thêm đường dẫn đặc biệt cho file exe (ưu tiên cao nhất)
            if is_frozen:
                # Thêm đường dẫn tương đối với thư mục chứa file exe
                exe_dir = os.path.dirname(sys.executable)
                possible_paths.append(os.path.join(exe_dir, "data", "mail.txt"))
                possible_paths.append(os.path.join(exe_dir, "mail.txt"))

            # Thêm các đường dẫn thông thường
            base_path = os.path.abspath(".")
            possible_paths.extend([
                # Trong thư mục hiện tại
                os.path.join(base_path, "mail.txt"),
                # Trong thư mục data
                os.path.join(base_path, "data", "mail.txt"),
                # Trong thư mục cha
                os.path.join(os.path.dirname(base_path), "mail.txt"),
                # Trong thư mục data của thư mục cha
                os.path.join(os.path.dirname(base_path), "data", "mail.txt"),
            ])

            # Thử sử dụng path_manager
            try:
                # Thử sử dụng get_txt_path (phương pháp đơn giản nhất)
                mail_file_path = path_manager.get_txt_path("mail.txt")
                possible_paths.append(mail_file_path)
                print(f"🔍 Đường dẫn file mail.txt từ get_txt_path: {mail_file_path}")
            except Exception as e:
                print(f"⚠️ Lỗi khi sử dụng get_txt_path: {str(e)}")

            try:
                # Sử dụng get_data_path như phương án dự phòng
                mail_file_path = path_manager.get_data_path("mail.txt")
                possible_paths.append(mail_file_path)
                print(f"🔍 Đường dẫn file mail.txt từ get_data_path: {mail_file_path}")
            except Exception as e:
                print(f"⚠️ Lỗi khi sử dụng get_data_path: {str(e)}")

            # In thông tin debug
            print(f"🔍 Danh sách các đường dẫn có thể chứa mail.txt:")
            for idx, path in enumerate(possible_paths):
                print(f"  {idx+1}. {path}")

            # Tìm file mail.txt trong danh sách đường dẫn
            mail_file_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    mail_file_path = path
                    print(f"✅ Đã tìm thấy file mail.txt tại: {mail_file_path}")
                    break

            # Nếu không tìm thấy, tạo file mail.txt mẫu
            if mail_file_path is None:
                print(f"⚠️ Không tìm thấy file mail.txt trong tất cả các đường dẫn")

                # Chọn đường dẫn để tạo file mail.txt mẫu
                if is_frozen:
                    # Nếu đang chạy từ file exe, tạo file trong thư mục data bên cạnh file exe
                    exe_dir = os.path.dirname(sys.executable)
                    data_dir = os.path.join(exe_dir, "data")
                    if not os.path.exists(data_dir):
                        os.makedirs(data_dir, exist_ok=True)
                    mail_file_path = os.path.join(data_dir, "mail.txt")
                else:
                    # Nếu đang chạy từ script, tạo file trong thư mục data
                    data_dir = os.path.join(base_path, "data")
                    if not os.path.exists(data_dir):
                        os.makedirs(data_dir, exist_ok=True)
                    mail_file_path = os.path.join(data_dir, "mail.txt")

                # Tạo file mail.txt mẫu
                try:
                    with open(mail_file_path, "w") as f:
                        f.write("# Danh sách email (format: email|password|authentication)\n")
                        f.write("# Ví dụ: <EMAIL>|password123|ABCDEFGHIJKLMNOP\n")
                    print(f"✅ Đã tạo file mail.txt mẫu tại: {mail_file_path}")
                except Exception as e:
                    print(f"❌ Lỗi khi tạo file mail.txt mẫu: {str(e)}")
                return

            # Kiểm tra thư mục cha
            parent_dir = os.path.dirname(mail_file_path)
            print(f"🔍 Thư mục cha: {parent_dir}")
            if os.path.exists(parent_dir):
                print(f"✅ Thư mục cha tồn tại: {parent_dir}")
                print(f"🔍 Nội dung thư mục cha:")
                try:
                    for item in os.listdir(parent_dir):
                        print(f"  - {item}")
                except Exception as e:
                    print(f"❌ Lỗi khi liệt kê nội dung thư mục cha: {str(e)}")
            else:
                print(f"❌ Thư mục cha không tồn tại: {parent_dir}")
                try:
                    os.makedirs(parent_dir, exist_ok=True)
                    print(f"✅ Đã tạo thư mục cha: {parent_dir}")
                except Exception as e:
                    print(f"❌ Lỗi khi tạo thư mục cha: {str(e)}")

            print(f"✅ File mail.txt tồn tại: {mail_file_path}")

            # Đọc danh sách email từ file
            try:
                with open(mail_file_path, "r", encoding="utf-8") as file:
                    lines = file.readlines()
                print(f"✅ Đã đọc {len(lines)} dòng từ file {mail_file_path}")
            except Exception as e:
                print(f"❌ Lỗi khi đọc file {mail_file_path}: {str(e)}")
                # Thử đọc với encoding khác
                try:
                    with open(mail_file_path, "r") as file:
                        lines = file.readlines()
                    print(f"✅ Đã đọc {len(lines)} dòng từ file {mail_file_path} (không có encoding)")
                except Exception as e2:
                    print(f"❌ Lỗi khi đọc file {mail_file_path} (không có encoding): {str(e2)}")
                    return

            # In nội dung file để debug (ẩn mật khẩu)
            print(f"🔍 Nội dung file mail.txt:")
            for i, line in enumerate(lines):
                # Ẩn mật khẩu trong dòng
                if "|" in line:
                    parts = line.split("|")
                    if len(parts) >= 2:
                        masked_line = parts[0] + "|" + "*" * len(parts[1])
                        if len(parts) > 2:
                            masked_line += "|" + "|".join(parts[2:])
                        print(f"  {i+1}. {masked_line}")
                    else:
                        print(f"  {i+1}. {line.strip()}")
                else:
                    print(f"  {i+1}. {line.strip()}")

            # Lọc các dòng trống và dòng comment
            valid_lines = []
            for line in lines:
                line = line.strip()
                if not line or line.startswith("#"):
                    continue
                valid_lines.append(line)

            print(f"✅ Đã lọc được {len(valid_lines)} dòng hợp lệ từ {len(lines)} dòng")

            # Kiểm tra xem có email nào không
            if not valid_lines:
                print(f"⚠️ Không có email nào trong file {mail_file_path}")
                print(f"⚠️ Vui lòng thêm email vào file với định dạng: email|password|authentication")
                return

            # Kiểm tra xem có đủ email cho thiết bị không
            if self.device_index >= len(valid_lines):
                print(f"⚠️ Không đủ email trong file {mail_file_path} cho thiết bị {self.serial} (index {self.device_index})")
                print(f"⚠️ Số email hiện có: {len(valid_lines)}, cần ít nhất {self.device_index + 1} email")

                # Nếu không đủ email, sử dụng email theo chỉ số thiết bị mod số lượng email
                if len(valid_lines) > 0:
                    self.device_index = self.device_index % len(valid_lines)
                    print(f"ℹ️ Sử dụng email ở vị trí {self.device_index + 1} cho thiết bị {self.serial}")
                else:
                    return

            # Lấy thông tin email tương ứng với chỉ số thiết bị
            email_line = valid_lines[self.device_index]
            print(f"🔍 Dòng email được chọn: {email_line.split('|')[0]}|***")

            # Phân tích dòng email
            email_info = email_line.split('|')
            print(f"🔍 Số phần tử sau khi phân tích: {len(email_info)}")

            # Kiểm tra định dạng email
            if len(email_info) >= 2:
                # Lấy mật khẩu từ cột thứ 2
                self.password = email_info[1].strip()
                # Hiển thị 2 ký tự đầu của mật khẩu và che phần còn lại
                masked_password = self.password[:2] + '*' * (len(self.password) - 2) if len(self.password) > 2 else '*' * len(self.password)
                print(f"✅ Đã tải mật khẩu cho thiết bị {self.serial} (index {self.device_index}): {masked_password}")

                # Lấy chuỗi xác thực 2FA từ cột thứ 3 (nếu có)
                self.authentication_code = ""
                self.authentication_secret = ""
                if len(email_info) >= 3:
                    self.authentication_secret = email_info[2].strip()
                    if self.authentication_secret:
                        # Che giấu chuỗi bí mật xác thực
                        masked_secret = self.authentication_secret[:4] + '*' * (len(self.authentication_secret) - 4) if len(self.authentication_secret) > 4 else '*' * len(self.authentication_secret)
                        print(f"✅ Đã đọc chuỗi bí mật xác thực 2FA cho thiết bị {self.serial} (chỉ số {self.device_index}): {masked_secret}")
                    else:
                        print(f"ℹ️ Không có chuỗi bí mật xác thực 2FA cho thiết bị {self.serial} (chỉ số {self.device_index})")
                else:
                    print(f"ℹ️ Không có cột chuỗi bí mật xác thực 2FA trong file {mail_file_path}")
            else:
                print(f"⚠️ Định dạng email không hợp lệ: {email_line.split('|')[0]}|***")
                print(f"⚠️ Định dạng cần thiết: email|password|authentication")

        except Exception as e:
            print(f"❌ Lỗi khi tải mật khẩu từ file mail.txt: {str(e)}")

    def _random_sleep(self, min_seconds, max_seconds, message=None, check_stop_flag=None):
        """
        Tạm dừng thực thi trong một khoảng thời gian ngẫu nhiên.
        :param min_seconds: Thời gian tối thiểu (giây).
        :param max_seconds: Thời gian tối đa (giây).
        :param message: Thông báo tùy chọn để hiển thị.
        :param check_stop_flag: Hàm callback để kiểm tra cờ dừng.
        :return: True nếu đã dừng do cờ dừng, False nếu đã ngủ hết thời gian.
        """
        seconds = random.uniform(min_seconds, max_seconds)
        if message:
            print(f"{message} ({seconds:.2f}s)")

        # Nếu có hàm kiểm tra cờ dừng, kiểm tra mỗi 0.05 giây
        if check_stop_flag and callable(check_stop_flag):
            start_time = time.time()
            end_time = start_time + seconds

            while time.time() < end_time:
                # Kiểm tra cờ dừng
                if check_stop_flag():
                    print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi đang chờ")
                    # Thông báo thêm để dễ theo dõi
                    print(f"⚠️ Dừng quá trình ngủ tại {message if message else 'không có thông báo'}")
                    return True  # Đã dừng do cờ dừng

                # Ngủ một khoảng thời gian ngắn hơn để phản ứng nhanh hơn với cờ dừng
                time.sleep(0.05)

            return False  # Đã ngủ hết thời gian
        else:
            # Nếu không có hàm kiểm tra cờ dừng, ngủ bình thường
            time.sleep(seconds)
            return False

    def press_key(self, keycode):
        """
        Gửi phím đến thiết bị.
        :param keycode: Mã phím cần gửi.
        """
        subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "keyevent", str(keycode)], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

    def swipe(self, start_x, start_y, end_x, end_y, duration=200):
        """
        Thực hiện thao tác vuốt trên màn hình.
        :param start_x: Tọa độ X bắt đầu.
        :param start_y: Tọa độ Y bắt đầu.
        :param end_x: Tọa độ X kết thúc.
        :param end_y: Tọa độ Y kết thúc.
        :param duration: Thời gian vuốt (ms). Mặc định là 100ms để vuốt nhanh hơn.
        """
        try:
            # Tính toán khoảng cách vuốt
            distance = ((end_x - start_x) ** 2 + (end_y - start_y) ** 2) ** 0.5
            print(f"🔄 Thực hiện vuốt nhanh và dài: khoảng cách {distance:.0f} pixels, thời gian {duration}ms")

            # Thực hiện vuốt nhanh và dài hơn
            result = subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "swipe",
                            str(start_x), str(start_y), str(end_x), str(end_y), str(duration)],
                            check=True, capture_output=True, text=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

            if result.stdout:
                print(f"✅ Kết quả stdout: {result.stdout}")
            if result.stderr:
                print(f"⚠️ Kết quả stderr: {result.stderr}")

            print(f"✅ Đã vuốt từ ({start_x}, {start_y}) đến ({end_x}, {end_y}) trong {duration}ms")
        except subprocess.CalledProcessError as e:
            print(f"❌ Lỗi khi vuốt: {str(e)}")
            if e.stdout:
                print(f"⚠️ Kết quả stdout: {e.stdout}")
            if e.stderr:
                print(f"⚠️ Kết quả stderr: {e.stderr}")

            # Thử lại với thời gian vuốt ngắn hơn
            try:
                print(f"🔄 Thử lại với thời gian vuốt ngắn hơn: 50ms")
                subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "swipe",
                                str(start_x), str(start_y), str(end_x), str(end_y), "50"], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                print(f"✅ Đã vuốt từ ({start_x}, {start_y}) đến ({end_x}, {end_y}) trong 50ms")
            except Exception as e2:
                print(f"❌ Lỗi khi thử lại vuốt: {str(e2)}")

    def send_text(self, text, delay_range=(0.01, 0.08)):
        """
        Gửi văn bản đến thiết bị một cách chậm rãi từng ký tự.
        :param text: Văn bản cần gửi.
        :param delay_range: Tuple (min_delay, max_delay) độ trễ giữa các ký tự.
        """
        if not text:
            print("⚠️ Văn bản trống, không gửi gì cả")
            return

        print(f"🔄 Gửi chậm văn bản: {text[:2]}{'*' * (len(text) - 2)}")

        # Giữ nguyên text, không xóa ký tự |
        # text = text.replace('|', '')  # Đã comment để giữ nguyên mật khẩu

        try:
            for idx, char in enumerate(text):
                subprocess.run(
                    [ADB_PATH, "-s", self.serial, "shell", "input", "text", char],
                    check=True,
                    creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
                )
                print(f"   ⌨️ Đã gửi ký tự {idx + 1}/{len(text)}: '{char}'")
                self._random_sleep(*delay_range, "Đợi giữa các ký tự")
            print(f"✅ Đã gửi văn bản thành công: {text[:2]}{'*' * (len(text) - 2)}")
        except Exception as e:
            print(f"❌ Lỗi khi gửi văn bản từng ký tự: {e}")
            raise
    def wait_for_image_and_tap(self, template_path, threshold=0.8, timeout=30.0, check_interval=1.0, max_retries=3, check_stop_flag=None):
        """
        Đợi và tap vào hình ảnh khi nó xuất hiện.

        Args:
            template_path (str): Đường dẫn đến file template
            threshold (float): Ngưỡng độ tương đồng (0-1)
            timeout (float): Thời gian tối đa chờ đợi (giây)
            check_interval (float): Khoảng thời gian giữa các lần kiểm tra
            max_retries (int): Số lần thử lại tối đa
            check_stop_flag: Hàm callback kiểm tra cờ dừng

        Returns:
            bool: True nếu tìm thấy và tap thành công, False nếu không
        """
        start_time = time.time()

        while time.time() - start_time < timeout:
            # Kiểm tra cờ dừng
            if check_stop_flag and check_stop_flag():
                return False

            # Thử tìm và tap vào hình ảnh
            if self.find_image_and_tap(template_path, threshold, max_retries):
                return True

            # Đợi một khoảng thời gian trước khi thử lại
            time.sleep(check_interval)

        return False

    def find_image_on_screen(self, template_path, threshold=0.8):
        """
        Tìm hình ảnh trên màn hình nhưng không tap vào.
        :param template_path: Đường dẫn đến file template.
        :param threshold: Ngưỡng độ tương đồng (0-1).
        :return: True nếu tìm thấy, False nếu không.
        """
        try:
            # Chụp ảnh màn hình
            screenshot_path = path_manager.get_screen_path(f"screen_{self.serial}.png")
            subprocess.run([ADB_PATH, "-s", self.serial, "exec-out", "screencap", "-p"],
                          stdout=open(screenshot_path, "wb"), check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

            # Đọc ảnh template và ảnh màn hình
            template = cv2.imread(template_path)
            screenshot = cv2.imread(screenshot_path)

            if template is None or screenshot is None:
                print(f"❌ Không thể đọc ảnh template hoặc ảnh màn hình")
                return False

            # Chuyển đổi ảnh sang thang độ xám
            template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
            screenshot_gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)

            # Tìm kiếm template trong ảnh màn hình
            result = cv2.matchTemplate(screenshot_gray, template_gray, cv2.TM_CCOEFF_NORMED)
            _, max_val, _, _ = cv2.minMaxLoc(result)

            # Nếu độ khớp lớn hơn ngưỡng, trả về True
            if max_val >= threshold:
                print(f"✅ Đã tìm thấy {os.path.basename(template_path)} (độ khớp: {max_val:.2f})")
                return True
            else:
                print(f"❌ Không tìm thấy {os.path.basename(template_path)} (max_val = {max_val:.2f})")
                return False

        except Exception as e:
            print(f"❌ Lỗi khi tìm {os.path.basename(template_path)}: {str(e)}")
            return False

    def find_image_and_tap(self, template_path, threshold=0.8, max_retries=2, check_stop_flag=None):
        """
        Tìm hình ảnh trên màn hình và tap vào vị trí tìm thấy.
        :param template_path: Đường dẫn đến file template.
        :param threshold: Ngưỡng độ tương đồng (0-1).
        :param max_retries: Số lần thử lại tối đa.
        :param check_stop_flag: Hàm callback để kiểm tra cờ dừng.
        :return: True nếu tìm thấy và tap thành công, False nếu không.
        """
        # Hàm kiểm tra cờ dừng
        def should_stop():
            if check_stop_flag and callable(check_stop_flag):
                # Kiểm tra cờ dừng
                stop_flag = check_stop_flag()
                if stop_flag:
                    print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi tìm {os.path.basename(template_path)}")
                return stop_flag
            return False
        for attempt in range(max_retries):
            # Kiểm tra cờ dừng trước khi bắt đầu mỗi lần thử
            if should_stop():
                return False

            try:
                # Chụp ảnh màn hình
                screenshot_path = path_manager.get_screen_path(f"screen_{self.serial}.png")
                try:
                    # Mở file để ghi với tham số creationflags chỉ áp dụng cho subprocess.run, không phải cho open()
                    with open(screenshot_path, "wb") as f:
                        subprocess.run([ADB_PATH, "-s", self.serial, "exec-out", "screencap", "-p"],
                                      stdout=f,
                                      check=True,
                                      creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                    print(f"✅ Đã chụp màn hình thiết bị {self.serial} và lưu vào {screenshot_path}")
                except Exception as e:
                    print(f"❌ Lỗi khi chụp màn hình: {str(e)}")
                    return False

                # Kiểm tra cờ dừng sau khi chụp ảnh màn hình
                if should_stop():
                    print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng sau khi chụp ảnh màn hình")
                    return False

                # Đọc ảnh template và ảnh màn hình
                template = cv2.imread(template_path)
                screenshot = cv2.imread(screenshot_path)

                if template is None or screenshot is None:
                    print(f"❌ Không thể đọc ảnh template hoặc ảnh màn hình (lần thử {attempt + 1}/{max_retries})")
                    continue

                # Chuyển đổi ảnh sang thang độ xám
                template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
                screenshot_gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)

                # Kiểm tra cờ dừng trước khi tìm kiếm template
                if should_stop():
                    print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi tìm kiếm template")
                    return False

                # Tìm kiếm template trong ảnh màn hình
                result = cv2.matchTemplate(screenshot_gray, template_gray, cv2.TM_CCOEFF_NORMED)
                _, max_val, _, max_loc = cv2.minMaxLoc(result)

                # Kiểm tra cờ dừng sau khi tìm kiếm template
                if should_stop():
                    print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng sau khi tìm kiếm template")
                    return False

                # Nếu độ khớp lớn hơn ngưỡng, tap vào vị trí tìm thấy
                if max_val >= threshold:
                    # Tính toán tọa độ trung tâm của template
                    h, w = template_gray.shape
                    center_x = max_loc[0] + w // 2
                    center_y = max_loc[1] + h // 2

                    # Kiểm tra cờ dừng trước khi tap
                    if should_stop():
                        print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi tap")
                        return False

                    # Tap vào vị trí trung tâm
                    subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "tap",
                                   str(center_x), str(center_y)], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

                    print(f"✅ Đã tìm thấy và tap vào {os.path.basename(template_path)} (độ khớp: {max_val:.2f})")
                    return True
                else:
                    print(f"❌ Không tìm thấy {os.path.basename(template_path)} trong lần thử {attempt + 1}/{max_retries} (max_val = {max_val:.2f})")

            except Exception as e:
                print(f"❌ Lỗi khi tìm và tap vào {os.path.basename(template_path)}: {str(e)}")

            # Đợi một chút trước khi thử lại
            if attempt < max_retries - 1:
                # Sử dụng _random_sleep với kiểm tra cờ dừng
                if self._random_sleep(1.0, 2.0, f"Chờ trước khi thử lại lần {attempt + 2}", check_stop_flag):
                    print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ thử lại")
                    return False

        return False

    def handle_ver_that_step(self, should_stop=None):
        """
        Xử lý bước Ver That - tìm và tap vào nút Ver That, nhập mật khẩu
        :param should_stop: Function để kiểm tra cờ dừng
        :return: True nếu thành công, False nếu thất bại
        """
        try:
            print("🔍 === Bắt đầu xử lý bước Ver That ===")

            # Kiểm tra cờ dừng
            if should_stop and should_stop():
                print("⚠️ Đã nhận tín hiệu dừng trước khi xử lý Ver That")
                return False

            # Tìm và tap vào nút Ver That
            print("Bước Ver That: Đang chờ nút Ver That xuất hiện")

            # Thử nhiều lần với ngưỡng khác nhau
            ver_that_found = False

            # Kiểm tra file tồn tại
            if os.path.exists(self.ver_that_template_path):
                print(f"✅ File ver_that.png tồn tại: {self.ver_that_template_path}")
                ver_that_found = self.wait_for_image_and_tap(
                    self.ver_that_template_path,
                    threshold=0.7,  # Giảm ngưỡng xuống 0.7
                    timeout=15.0,   # Giảm thời gian chờ
                    check_interval=1.0,
                    max_retries=3,
                    check_stop_flag=should_stop
                    )
            else:
                print(f"❌ File ver_that.png không tồn tại: {self.ver_that_template_path}")

            if ver_that_found:
                print("✅ Đã tìm thấy và tap vào nút Ver That")
                self._random_sleep(8.0, 10.0, "Chờ sau khi tap vào Ver That trước khi nhập mật khẩu")

                # Nhập mật khẩu ngay sau khi tìm thấy Ver That
                print(f"🔍 Kiểm tra mật khẩu: {self.password if self.password else 'Không có'}")
                if self.password:
                    print(f"🔑 Nhập mật khẩu sau khi tap vào Ver That: {self.password[:2]}{'*' * (len(self.password) - 2)}")

                    # Xóa trường nhập liệu trước khi nhập mật khẩu mới (sau ver_that)
                    print("🔄 Xóa trường nhập liệu trước khi nhập mật khẩu (sau ver_that)")
                    for i in range(30):  # Tăng lên 30 ký tự để đảm bảo trường trống hoàn toàn
                        subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "keyevent", "67"], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)  # KEYCODE_DEL = 67
                        time.sleep(0.05)  # Giảm thời gian chờ để nhanh hơn
                    print(f"✅ Đã xóa 30 ký tự từ trường nhập liệu (sau ver_that)")

                    # Sử dụng cách nhập mật khẩu giống chplay (sau ver_that)
                    print(f"🔑 Nhập mật khẩu (độ dài: {len(self.password)} ký tự): {self.password[:2]}{'*' * (len(self.password) - 2)}")

                    # Nhập mật khẩu bằng cách giống chplay
                    if self.input_text_chplay_style(self.password):
                        print("✅ Đã nhập mật khẩu thành công (cách chplay)")

                        # Nhấn phím Enter giống chplay
                        time.sleep(1)  # Đợi một chút trước khi nhấn Enter
                        if self.press_key(66):  # KEYCODE_ENTER = 66
                            print("✅ Đã nhấn phím Enter sau khi nhập mật khẩu")
                        else:
                            print("❌ Không thể nhấn phím Enter")
                    else:
                        print("❌ Không thể nhập mật khẩu (cách chplay)")
                        return False

                    # Đợi lâu hơn sau khi nhập mật khẩu và trước khi nhấn Enter
                    self._random_sleep(5.0, 7.0, "Chờ sau khi nhập mật khẩu và trước khi nhấn Enter")

                    # Nhấn Enter để xác nhận
                    try:
                        print(f"🔄 Thực hiện lệnh: adb -s {self.serial} shell input keyevent 66")
                        result = subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "keyevent", "66"],
                                              check=True, capture_output=True, text=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

                        if result.stdout:
                            print(f"✅ Kết quả stdout: {result.stdout}")
                        if result.stderr:
                            print(f"⚠️ Kết quả stderr: {result.stderr}")

                        print("✅ Đã nhấn Enter để xác nhận mật khẩu")
                    except subprocess.CalledProcessError as e:
                        print(f"❌ Lỗi khi nhấn Enter: {str(e)}")
                        if e.stdout:
                            print(f"⚠️ Kết quả stdout: {e.stdout}")
                        if e.stderr:
                            print(f"⚠️ Kết quả stderr: {e.stderr}")

                    # Đợi xác thực lâu hơn và đợi nút Continue Ver xuất hiện
                    self._random_sleep(10.0, 15.0, "Chờ xác thực mật khẩu và đợi nút Continue Ver xuất hiện")

                    # Kiểm tra xem có xuất hiện authen_ads.png không (yêu cầu 2FA)
                    print("🔍 Kiểm tra xem có yêu cầu 2FA không...")

                    if os.path.exists(self.authen_ads_template_path):
                        authen_ads_found = self.find_image_on_screen(self.authen_ads_template_path, threshold=0.7)

                        if authen_ads_found:
                            print("✅ Đã tìm thấy authen_ads.png - Yêu cầu nhập code 2FA")

                            # Lấy mã xác thực từ giao diện (ưu tiên) hoặc từ authentication_secret (giống chplay)
                            auth_secret_to_use = None

                            # Ưu tiên 1: Từ giao diện
                            if self.auth_secret:
                                auth_secret_to_use = self.auth_secret
                                print(f"✅ Sử dụng mã xác thực từ giao diện: {self.auth_secret[:4]}{'*' * (len(self.auth_secret) - 4) if len(self.auth_secret) > 4 else '*' * len(self.auth_secret)}")
                            # Ưu tiên 2: Từ authentication_secret đã có sẵn
                            elif hasattr(self, 'authentication_secret') and self.authentication_secret:
                                auth_secret_to_use = self.authentication_secret
                                print(f"✅ Sử dụng authentication_secret đã có: {self.authentication_secret[:4]}{'*' * (len(self.authentication_secret) - 4) if len(self.authentication_secret) > 4 else '*' * len(self.authentication_secret)}")

                            if auth_secret_to_use:
                                # Tạo mã OTP từ secret (giống chplay)
                                twofa_code = self.generate_otp_from_secret(auth_secret_to_use)

                                if twofa_code:
                                    print(f"🔐 Nhập code 2FA: {twofa_code}")
                                else:
                                    print("❌ Không thể tạo mã OTP từ secret, bỏ qua 2FA")
                            else:
                                print("❌ Không có authentication secret, bỏ qua 2FA")
                                twofa_code = None

                            # Chỉ nhập 2FA nếu có code hợp lệ
                            if twofa_code:
                                # Xóa trường nhập liệu trước khi nhập code 2FA
                                print("🔄 Xóa trường nhập liệu trước khi nhập code 2FA")
                                for i in range(10):  # Xóa 10 ký tự cho code 2FA
                                    subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "keyevent", "67"], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                                    time.sleep(0.05)
                                print(f"✅ Đã xóa 10 ký tự từ trường nhập liệu (2FA)")

                                # Nhập code 2FA bằng cách giống chplay
                                if self.input_text_chplay_style(twofa_code):
                                    print("✅ Đã nhập code 2FA thành công (cách chplay)")

                                    # Nhấn phím Enter để xác nhận 2FA
                                    time.sleep(1)
                                    if self.press_key(66):  # KEYCODE_ENTER = 66
                                        print("✅ Đã nhấn phím Enter sau khi nhập code 2FA")
                                        self._random_sleep(5.0, 7.0, "Chờ xác thực code 2FA")
                                    else:
                                        print("❌ Lỗi khi nhấn phím Enter sau khi nhập code 2FA")
                                else:
                                    print("❌ Lỗi khi nhập code 2FA (cách chplay)")
                            else:
                                print("⚠️ Bỏ qua nhập 2FA vì không có code hợp lệ")
                        else:
                            print("ℹ️ Không tìm thấy authen_ads.png - Không cần 2FA")
                    else:
                        print("⚠️ File authen_ads.png không tồn tại - Bỏ qua kiểm tra 2FA")

                    # Tìm và tap vào nút Continue Ver (nếu xuất hiện)
                    print("Tìm và tap vào nút Continue Ver (nếu xuất hiện)")
                    continue_ver_found = False

                    if os.path.exists(self.continue_ver_template_path):
                        # Tăng số lần thử lại và giảm ngưỡng độ tương đồng khi tìm continue_ver.png
                        continue_ver_found = self.wait_for_image_and_tap(self.continue_ver_template_path, threshold=0.7, max_retries=2, check_stop_flag=should_stop)

                    if continue_ver_found:
                        print("✅ Đã tìm thấy và tap vào nút Continue Ver")
                        # Đợi một chút sau khi tap vào Continue Ver
                        self._random_sleep(8.0, 12.0, "Chờ sau khi tap vào Continue Ver")
                else:
                    print("⚠️ Không có mật khẩu được cung cấp")

                print("✅ Hoàn thành xử lý bước Ver That")
                return True
            else:
                print("⚠️ Không tìm thấy nút Ver That sau thời gian chờ")
                return False

        except Exception as e:
            print(f"❌ Lỗi khi xử lý bước Ver That: {e}")
            return False

    def execute_actions(self, check_stop_flag=None):
        """
        Thực hiện chuỗi hành động đóng hồ sơ.
        :param check_stop_flag: Hàm callback để kiểm tra cờ dừng từ MainForm.py
        :return: True nếu thành công, False nếu thất bại.
        """
        try:
            print(f"\n=== Bắt đầu thực hiện chuỗi hành động đóng hồ sơ trên thiết bị {self.serial} ===\n")

            # Định nghĩa kích thước màn hình mặc định
            screen_width = 1080   # Chiều rộng màn hình mặc định
            screen_height = 1920  # Chiều cao màn hình mặc định

            # Hàm kiểm tra cờ dừng
            def should_stop():
                if check_stop_flag and callable(check_stop_flag):
                    # Kiểm tra cờ dừng
                    stop_flag = check_stop_flag()
                    if stop_flag:
                        print("⚠️ Đã nhận tín hiệu dừng từ người dùng")
                    return stop_flag
                return False

            # Kiểm tra các file template cần thiết
            missing_templates = []
            for template_name in ["chrome.png", "continue.png", "got_it.png",
                                 "search.png", "ver_that.png", "menu.png", "setting.png",
                                 "close_payment.png", "continue_close.png", "close_payment_profile.png"]:
                template_path = os.path.join(self.template_dir, template_name)
                if not os.path.exists(template_path):
                    missing_templates.append(template_name)

            # Kiểm tra xem có ít nhất một trong hai file yes_im_in.png hoặc yes_im_in_trang.png
            yes_im_in_path = os.path.join(self.template_dir, "yes_im_in.png")
            yes_im_in_trang_path = os.path.join(self.template_dir, "yes_im_in_trang.png")
            if not os.path.exists(yes_im_in_path) and not os.path.exists(yes_im_in_trang_path):
                missing_templates.append("yes_im_in.png hoặc yes_im_in_trang.png")

            if missing_templates:
                print(f"⚠️ Không tìm thấy các file template sau: {', '.join(missing_templates)}")
                print(f"⚠️ Vui lòng đặt các file template vào thư mục {self.template_dir}")

            # Kiểm tra cờ dừng trước khi bắt đầu
            if should_stop():
                print("⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi bắt đầu thực hiện các bước")
                return False

            # Bước 1: Về màn hình chính bằng cách nhấn nút Home
            print("Bước 1: Về màn hình chính bằng cách nhấn nút Home")
            subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "keyevent", "3"], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)  # KEYCODE_HOME = 3
            print("✅ Đã về màn hình chính")

            # Đợi một chút sau khi về màn hình chính
            if self._random_sleep(2.0, 3.0, "Chờ sau khi về màn hình chính", check_stop_flag):
                print("⚠️ Đã nhận tín hiệu dừng từ người dùng sau khi về màn hình chính")
                return False

            # Kiểm tra cờ dừng trước khi vuốt lên
            if should_stop():
                print("⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi vuốt lên")
                return False

            # Bước 2: Vuốt lên 1 lần để hiển thị menu ứng dụng
            print("Bước 2: Vuốt lên 1 lần để hiển thị menu ứng dụng")
            screen_height = 1920  # Chiều cao màn hình mặc định
            screen_width = 1080   # Chiều rộng màn hình mặc định

            # Vuốt từ dưới lên trên với thời gian vuốt ngắn hơn để vuốt nhanh hơn
            # Vuốt từ gần cuối màn hình lên gần đầu màn hình để vuốt dài hơn
            self.swipe(screen_width // 2, screen_height * 4 // 5, screen_width // 2, screen_height // 5, 250)

            # Đợi một chút sau khi vuốt để màn hình kịp phản hồi
            if self._random_sleep(1.0, 2.0, "Chờ sau khi vuốt lên 1 lần", check_stop_flag):
                print("⚠️ Đã nhận tín hiệu dừng từ người dùng sau khi vuốt lên 1 lần")
                return False

            # Bước 3: Tìm và tap vào biểu tượng Chrome
            print("Bước 3: Tìm và tap vào biểu tượng Chrome")
            chrome_found = False

            if os.path.exists(self.chrome_template_path):
                chrome_found = self.find_image_and_tap(self.chrome_template_path,max_retries=2, check_stop_flag=check_stop_flag)

            if not chrome_found:
                print("⚠️ Không tìm thấy biểu tượng Chrome")
                print("❌ Không thể mở Chrome")
                return False

            # Kiểm tra cờ dừng trước khi đợi Chrome khởi động
            if should_stop():
                print("⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi đợi Chrome khởi động")
                return False

            # Đợi Chrome khởi động
            if self._random_sleep(5.0, 7.0, "Chờ Chrome khởi động hoàn toàn", check_stop_flag):
                print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi đợi Chrome khởi động")
                return False

            # Bước 4: Tìm và tap vào nút Continue
            print("Bước 4: Tìm và tap vào nút Continue")
            continue_found = False

            # Kiểm tra cờ dừng trước khi tìm nút Continue
            if should_stop():
                print("⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi tìm nút Continue")
                return False

            if os.path.exists(self.continue_template_path):
                continue_found = self.find_image_and_tap(self.continue_template_path, check_stop_flag=check_stop_flag)

            if not continue_found:
                print("⚠️ Không tìm thấy nút Continue, tiếp tục với bước tiếp theo")
            else:
                # Đợi một chút sau khi tap vào Continue
                if self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào Continue", check_stop_flag):
                    print("⚠️ Đã nhận tín hiệu dừng từ người dùng sau khi tap vào Continue")
                    return False

            # Kiểm tra cờ dừng trước khi tìm nút Yes I'm in
            if should_stop():
                print("⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi tìm nút Yes I'm in")
                return False

            # Bước 6: Tìm và tap vào nút Yes I'm in (thử yes_im_in_trang.png trước)
            print("Bước 6: Tìm và tap vào nút Yes I'm in")
            yes_im_in_found = False

            # Bước 6.1: Thử tìm yes_im_in_trang.png trước
            print("Bước 6.1: Tìm và tap vào yes_im_in_trang.png")
            if os.path.exists(self.yes_im_in_trang_template_path):
                yes_im_in_found = self.find_image_and_tap(self.yes_im_in_trang_template_path, max_retries=2, check_stop_flag=check_stop_flag)
                if yes_im_in_found:
                    print("✅ Đã tìm thấy và tap vào yes_im_in_trang.png")
                else:
                    print("⚠️ Không tìm thấy yes_im_in_trang.png, thử tìm yes_im_in.png")
            else:
                print("⚠️ File yes_im_in_trang.png không tồn tại, thử tìm yes_im_in.png")

            # Bước 6.2: Nếu không tìm thấy yes_im_in_trang.png, thử yes_im_in.png
            if not yes_im_in_found:
                print("Bước 6.2: Tìm và tap vào yes_im_in.png")
                if os.path.exists(self.yes_im_in_template_path):
                    yes_im_in_found = self.find_image_and_tap(self.yes_im_in_template_path, max_retries=2, check_stop_flag=check_stop_flag)
                    if yes_im_in_found:
                        print("✅ Đã tìm thấy và tap vào yes_im_in.png")
                    else:
                        print("⚠️ Không tìm thấy yes_im_in.png")
                else:
                    print("⚠️ File yes_im_in.png không tồn tại")

            if not yes_im_in_found:
                print("⚠️ Không tìm thấy cả yes_im_in_trang.png và yes_im_in.png, tiếp tục với bước tiếp theo")
            else:
                # Đợi một chút sau khi tap vào Yes I'm in
                if self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào Yes I'm in", check_stop_flag):
                    print("⚠️ Đã nhận tín hiệu dừng từ người dùng sau khi tap vào Yes I'm in")
                    return False

            # Kiểm tra cờ dừng trước khi vuốt lên để tìm nút Got it
            if should_stop():
                print("⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi vuốt lên để tìm nút Got it")
                return False
            else:
                print("Không dừng, tiếp tục vuốt lên để tìm nút Got it")

            # Nếu thấy more thì vuốt lên
            more_found = False
            if os.path.exists(self.more_template_path):
                more_found = self.find_image_on_screen(self.more_template_path)

                if more_found:
                    # Vuốt khi tìm thấy more
                    self.swipe(screen_width // 2, screen_height * 4 // 5,
                            screen_width // 2, screen_height // 5, 300)

                    # Đợi sau khi vuốt
                    if self._random_sleep(2.0, 3.0, "Chờ sau khi vuốt", check_stop_flag):
                        print("⚠️ Đã nhận tín hiệu dừng ")
                        return False
                else:
                    print("⚠️ Không tìm thấy more, tiếp tục với bước tiếp theo")


            # Đợi một chút sau khi vuốt để màn hình kịp phản hồi
            if self._random_sleep(1.0, 2.0, "Chờ sau khi vuốt lên để tìm nút Got it", check_stop_flag):
                print("⚠️ Đã nhận tín hiệu dừng từ người dùng sau khi vuốt lên để tìm nút Got it")
                return False

            # Kiểm tra cờ dừng trước khi tìm nút Got it
            if should_stop():
                print("⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi tìm nút Got it")
                return False

            # Bước 8: Tìm và tap vào nút Got it
            print("Bước 8: Tìm và tap vào nút Got it")
            got_it_found = False

            if os.path.exists(self.got_it_template_path):
                got_it_found = self.wait_for_image_and_tap(self.got_it_template_path, check_stop_flag=check_stop_flag)

            if not got_it_found:
                print("⚠️ Không tìm thấy nút Got it, tiếp tục với bước 8.1 và 8.2")

                # Bước 8.1: Tìm và tap vào nút tunit_on (chỉ khi bước 8 không thành công)
                print("Bước 8.1: Tìm và tap vào nút tunit_on (vì bước 8 không thành công)")
                tunit_on_found = False
                if os.path.exists(self.tunit_on_template_path):
                    tunit_on_found = self.find_image_and_tap(self.tunit_on_template_path, check_stop_flag=should_stop)

                    if tunit_on_found:
                        print("✅ Đã tìm thấy và tap vào nút tunit_on")
                        # Đợi một chút sau khi tap vào tunit_on
                        self.swipe(screen_width // 2, screen_height * 4 // 5, screen_width // 2, screen_height // 5, 300)
                        if self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào tunit_on", should_stop):
                            print("⚠️ Đã nhận tín hiệu dừng từ người dùng sau khi tap vào tunit_on")
                            return False

                        # Bước 8.2: Tìm và tap vào nút git_it_trang (chỉ khi tìm thấy tunit_on)
                        print("Bước 8.2: Tìm và tap vào nút git_it_trang")
                        git_it_trang_found = False
                        if os.path.exists(self.git_it_trang_template_path):
                            git_it_trang_found = self.find_image_and_tap(self.git_it_trang_template_path, check_stop_flag=should_stop)

                        if git_it_trang_found:
                            print("✅ Đã tìm thấy và tap vào nút git_it_trang")
                            # Đợi một chút sau khi tap vào git_it_trang
                            if self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào git_it_trang", should_stop):
                                print("⚠️ Đã nhận tín hiệu dừng từ người dùng sau khi tap vào git_it_trang")
                                return False
                        else:
                            print("⚠️ Không tìm thấy nút git_it_trang, tiếp tục với bước tiếp theo")
                    else:
                        print("⚠️ Không tìm thấy nút tunit_on, tiếp tục với bước tiếp theo")
                else:
                    print("⚠️ File tunit_on.png không tồn tại, bỏ qua bước 8.1")
            else:
                # Đợi một chút sau khi tap vào Got it
                if self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào Got it", check_stop_flag):
                    print("⚠️ Đã nhận tín hiệu dừng từ người dùng sau khi tap vào Got it")
                    return False

                print("✅ Bước 8 thành công - Bỏ qua bước 8.1 và 8.2")



            # Bước 9: Tìm và tap vào ô tìm kiếm
            print("Bước 9: Tìm và tap vào ô tìm kiếm")
            search_found = False

            if os.path.exists(self.search_template_path):
                search_found = self.wait_for_image_and_tap(self.search_template_path, threshold=0.7, max_retries=2, check_stop_flag=should_stop)

            if not search_found:
                print("⚠️ Không tìm thấy ô tìm kiếm, thử tìm ô tìm kiếm phụ (search2)")

                # Bước 9.2: Tìm và tap vào ô tìm kiếm phụ
                print("Bước 9.2: Tìm và tap vào ô tìm kiếm phụ")
                search2_found = False

                if os.path.exists(self.search2_template_path):
                    search2_found = self.find_image_and_tap(self.search2_template_path, check_stop_flag=should_stop)

                if not search2_found:
                    print("⚠️ Không tìm thấy ô tìm kiếm phụ, tiếp tục với bước tiếp theo")
            else:
                # Đợi một chút sau khi tap vào ô tìm kiếm
                if self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào ô tìm kiếm", check_stop_flag):
                    print("⚠️ Đã nhận tín hiệu dừng từ người dùng sau khi tap vào ô tìm kiếm")
                    return False

            # Bước 10: Nhập URL payments.google.com (chỉ khi đã tìm thấy ô tìm kiếm)
            if search_found or search2_found:
                print("Bước 10: Nhập URL payments.google.com")
                try:
                    self.input_text_chplay_style("payments.google.com")
                    print("✅ Đã nhập URL payments.google.com")

                    # Đợi một chút sau khi nhập URL
                    if self._random_sleep(2.0, 3.0, "Chờ sau khi nhập URL", check_stop_flag=should_stop):
                        print("⚠️ Đã nhận tín hiệu dừng từ người dùng sau khi nhập URL")
                        return False

                    # Nhấn Enter để tìm kiếm
                    self.press_key(66)  # KEYCODE_ENTER = 66
                    print("✅ Đã nhấn Enter để tìm kiếm")
                except Exception as e:
                    print(f"❌ Lỗi khi nhập URL: {str(e)}")
                    # Không return False nữa, tiếp tục thực hiện các bước tiếp theo
            else:
                print("⚠️ Bỏ qua bước nhập URL vì không tìm thấy ô tìm kiếm")

            # Đợi trang web tải
            if self._random_sleep(10.0, 14.0, "Chờ trang web tải", check_stop_flag=should_stop):
                print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ trang web tải")
                return False

           # Bước 11: Xử lý Ver That (sử dụng hàm riêng)
            print("Bước 11: Gọi hàm xử lý Ver That")
            ver_that_success = self.handle_ver_that_step(should_stop)

            if not ver_that_success:
                print("⚠️ Xử lý Ver That không thành công, tiếp tục với bước tiếp theo")
            
            # Kiểm tra signed_in.png có xuất hiện không nếu có tap vào vị trí 444, 440
            print("🔍 Kiểm tra signed_in.png sau khi Chrome khởi động")
            if os.path.exists(self.signed_in_template_path):
                signed_in_found = self.find_image_on_screen(self.signed_in_template_path, threshold=0.7)
                if signed_in_found:
                    print("✅ Đã tìm thấy signed_in.png - Tap vào vị trí 444, 440")
                    if self.tap_at_position(444, 440):
                        print("✅ Đã tap vào vị trí 444, 440 thành công")
                        # Đợi một chút sau khi tap
                        self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào signed_in")
                    else:
                        print("❌ Không thể tap vào vị trí 444, 440")
                else:
                    print("ℹ️ Không tìm thấy signed_in.png - Tiếp tục bình thường")
            else:
                print("⚠️ File signed_in.png không tồn tại - Bỏ qua kiểm tra")

            # Bước 12: Tìm menu.png với retry logic (3 lần thử quay lại bước 9)
            print("Bước 12: Tìm menu.png với retry logic")
            menu_found = False
            menu_retry_count = 0
            max_menu_retries = 3  # 3 lần thử quay lại bước 9

            while not menu_found and menu_retry_count < max_menu_retries:
                print(f"\n=== Lần thử tìm menu.png: {menu_retry_count + 1}/{max_menu_retries} ===")

                # Kiểm tra file tồn tại và tìm tap vào menu
                if os.path.exists(self.menu_template_path):
                    print(f"✅ File menu.png tồn tại: {self.menu_template_path}")
                    menu_found = self.wait_for_image_and_tap(
                        self.menu_template_path,
                        threshold=0.7,  # Giảm ngưỡng xuống 0.7
                        timeout=7.0,   # Giảm thời gian chờ
                        check_interval=1.0,
                        max_retries=3,
                        check_stop_flag=should_stop
                    )
                else:
                    print(f"❌ Không tìm thấy file menu.png")

                if menu_found:
                    print("✅ Đã tìm thấy và tap vào nút Menu")
                    # Đợi một chút sau khi tap vào Menu
                    self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào Menu")
                    break
                else:
                    print(f"⚠️ Không tìm thấy menu.png lần thử {menu_retry_count + 1}")
                    menu_retry_count += 1

                    if menu_retry_count < max_menu_retries:
                        print("🔄 Thực hiện quy trình khôi phục: mở key 187 -> vuốt lên 1 lần -> mở chrome -> thực hiện lại từ bước 9")


                        # Bước 1: Mở key 187 (navigation)
                        print("🔄 Mở key 187 (navigation)")
                        self.press_key(187)  # KEYCODE_NAVIGATION = 187
                        time.sleep(2)

                        # Bước 2: Vuốt lên 1 lần
                        print("🔄 Vuốt lên 1 lần")
                        screen_width = 1080   # Chiều rộng màn hình mặc định
                        screen_height = 1920  # Chiều cao màn hình mặc định
                        self.swipe(screen_width // 2, screen_height * 2 // 3, screen_width // 2, screen_height // 4, 300)
                        time.sleep(2)

                        # Bước 3: Mở Chrome
                        print("🔄 Mở Chrome")
                        chrome_found = False
                        if os.path.exists(self.chrome_template_path):
                            chrome_found = self.find_image_and_tap(self.chrome_template_path, max_retries=3)

                        if chrome_found:
                            print("✅ Đã mở Chrome thành công")
                            # Đợi Chrome khởi động
                            self._random_sleep(6.0, 7.0, "Chờ Chrome khởi động")
                            print("✅ Chrome đã khởi động")

                            # Kiểm tra và xử lý signed_in.png nếu xuất hiện
                            print("🔍 Kiểm tra signed_in.png sau khi Chrome khởi động")
                            if os.path.exists(self.signed_in_template_path):
                                signed_in_found = self.find_image_on_screen(self.signed_in_template_path, threshold=0.7)
                                if signed_in_found:
                                    print("✅ Đã tìm thấy signed_in.png - Tap vào vị trí 444, 440")
                                    if self.tap_at_position(444, 440):
                                        print("✅ Đã tap vào vị trí 444, 440 thành công")
                                        # Đợi một chút sau khi tap
                                        self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào signed_in")
                                    else:
                                        print("❌ Không thể tap vào vị trí 444, 440")
                                else:
                                    print("ℹ️ Không tìm thấy signed_in.png - Tiếp tục bình thường")
                            else:
                                print("⚠️ File signed_in.png không tồn tại - Bỏ qua kiểm tra")

                            # Bước 4: Thực hiện lại từ bước 9 (tìm kiếm, nhập URL, Ver That)
                            print("🔄 Thực hiện lại từ bước 9")
                            # Tiếp tục vòng lặp while để thực hiện lại từ bước 9
                            continue  # Tiếp tục vòng lặp while để tìm menu
                        else:
                            print("❌ Không thể mở Chrome, bỏ qua retry này")
                            print(f"❌ Retry thất bại lần {menu_retry_count}")
                    else:
                        print("❌ Đã hết số lần retry cho menu.png")

            # Kiểm tra kết quả cuối cùng
            if not menu_found:
                print("❌ Không thể tìm thấy menu.png sau 3 lần thử quay lại bước 9")
                print("❌ Đóng hồ sơ thất bại - Không tìm thấy menu")
                return False

            # Bước 13: Tìm và tap vào nút Setting
            print("Bước 13: Tìm và tap vào nút Setting")
            setting_found = False

            if os.path.exists(self.setting_template_path):
                setting_found = self.find_image_and_tap(self.setting_template_path)

            if not setting_found:
                print("⚠️ Không tìm thấy nút Setting, tiếp tục với bước tiếp theo")
                # Không return False nữa, tiếp tục thực hiện các bước tiếp theo
            else:
                # Đợi một chút sau khi tap vào Setting
                self._random_sleep(7.0, 10.0, "Chờ sau khi tap vào Setting")

            # Bước 14: Vuốt lên 3 lần để tìm nút Close Payment
            print("Bước 14: Vuốt lên 3 lần để tìm nút Close Payment")
            for i in range(3):
                # Vuốt từ dưới lên trên với thời gian vuốt ngắn hơn để vuốt nhanh hơn
                # Vuốt từ gần cuối màn hình lên gần đầu màn hình để vuốt dài hơn
                self.swipe(screen_width // 2, screen_height * 4 // 5, screen_width // 2, screen_height // 5, 300)
                print(f"✅ Đã vuốt lên lần thứ {i + 1}")
                # Đợi một chút sau khi vuốt để màn hình kịp phản hồi
                self._random_sleep(1.0, 2.0, f"Chờ sau khi vuốt lên lần thứ {i + 1}")

            # Bước 15: Tìm và tap vào nút Close Payment
            print("Bước 15: Tìm và tap vào nút Close Payment")
            close_payment_found = False

            if os.path.exists(self.close_payment_template_path):
                close_payment_found = self.find_image_and_tap(self.close_payment_template_path)

            if not close_payment_found:
                print("⚠️ Không tìm thấy nút Close Payment, tiếp tục với bước tiếp theo")
                # Không return False nữa, tiếp tục thực hiện các bước tiếp theo
            else:
                # Đợi lâu hơn sau khi tap vào Close Payment trước khi nhập mật khẩu
                self._random_sleep(8.0, 10.0, "Chờ sau khi tap vào Close Payment trước khi nhập mật khẩu")

                # Nhập mật khẩu ngay lập tức sau khi tap vào Close Payment
                print(f"🔍 Kiểm tra mật khẩu sau khi tap vào Close Payment: {self.password if self.password else 'Không có'}")
                if self.password:
                    print(f"🔑 Nhập mật khẩu sau khi tap vào Close Payment: {self.password[:2]}{'*' * (len(self.password) - 2)}")

                    # Xóa trường nhập liệu trước khi nhập mật khẩu mới (sau close_payment)
                    print("🔄 Xóa trường nhập liệu trước khi nhập mật khẩu (sau close_payment)")
                    for i in range(30):  # Tăng lên 30 ký tự để đảm bảo trường trống hoàn toàn
                        subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "keyevent", "67"], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)  # KEYCODE_DEL = 67
                        time.sleep(0.05)  # Giảm thời gian chờ để nhanh hơn
                    print(f"✅ Đã xóa 30 ký tự từ trường nhập liệu (sau close_payment)")

                    # Sử dụng cách nhập mật khẩu giống chplay (sau close_payment)
                    print(f"🔑 Nhập mật khẩu (độ dài: {len(self.password)} ký tự): {self.password[:2]}{'*' * (len(self.password) - 2)}")

                    # Nhập mật khẩu bằng cách giống chplay
                    if self.input_text_chplay_style(self.password):
                        print("✅ Đã nhập mật khẩu thành công (cách chplay)")

                        # Nhấn phím Enter giống chplay
                        time.sleep(1)  # Đợi một chút trước khi nhấn Enter
                        if self.press_key(66):  # KEYCODE_ENTER = 66
                            print("✅ Đã nhấn phím Enter sau khi nhập mật khẩu")
                        else:
                            print("❌ Không thể nhấn phím Enter")
                    else:
                        print("❌ Không thể nhập mật khẩu (cách chplay)")
                        return False

                    # Đợi lâu hơn sau khi nhập mật khẩu và trước khi nhấn Enter
                    self._random_sleep(5.0, 7.0, "Chờ sau khi nhập mật khẩu sau Close Payment và trước khi nhấn Enter")

                    # Đợi xác thực lâu hơn và đợi nút Continue Ver xuất hiện
                    self._random_sleep(10.0, 15.0, "Chờ xác thực mật khẩu sau Close Payment và đợi nút Continue Ver xuất hiện")

                    # Kiểm tra xem có xuất hiện authen_ads.png không (yêu cầu 2FA) - phần 2
                    print("🔍 Kiểm tra xem có yêu cầu 2FA không (sau Close Payment)...")

                    if os.path.exists(self.authen_ads_template_path):
                        authen_ads_found = self.find_image_on_screen(self.authen_ads_template_path, threshold=0.7)

                        if authen_ads_found:
                            print("✅ Đã tìm thấy authen_ads.png - Yêu cầu nhập code 2FA (sau Close Payment)")

                            # Lấy mã xác thực từ giao diện (ưu tiên) hoặc từ authentication_secret (giống chplay)
                            auth_secret_to_use = None

                            # Ưu tiên 1: Từ giao diện
                            if self.auth_secret:
                                auth_secret_to_use = self.auth_secret
                                print(f"✅ Sử dụng mã xác thực từ giao diện (sau Close Payment): {self.auth_secret[:4]}{'*' * (len(self.auth_secret) - 4) if len(self.auth_secret) > 4 else '*' * len(self.auth_secret)}")
                            # Ưu tiên 2: Từ authentication_secret đã có sẵn
                            elif hasattr(self, 'authentication_secret') and self.authentication_secret:
                                auth_secret_to_use = self.authentication_secret
                                print(f"✅ Sử dụng authentication_secret đã có (sau Close Payment): {self.authentication_secret[:4]}{'*' * (len(self.authentication_secret) - 4) if len(self.authentication_secret) > 4 else '*' * len(self.authentication_secret)}")

                            if auth_secret_to_use:
                                # Tạo mã OTP từ secret (giống chplay)
                                twofa_code = self.generate_otp_from_secret(auth_secret_to_use)

                                if twofa_code:
                                    print(f"🔐 Nhập code 2FA (sau Close Payment): {twofa_code}")
                                else:
                                    print("❌ Không thể tạo mã OTP từ secret, bỏ qua 2FA (sau Close Payment)")
                            else:
                                print("❌ Không có authentication secret, bỏ qua 2FA (sau Close Payment)")
                                twofa_code = None

                            # Chỉ nhập 2FA nếu có code hợp lệ
                            if twofa_code:
                                # Xóa trường nhập liệu trước khi nhập code 2FA
                                print("🔄 Xóa trường nhập liệu trước khi nhập code 2FA (sau Close Payment)")
                                for i in range(10):  # Xóa 10 ký tự cho code 2FA
                                    subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "keyevent", "67"], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                                    time.sleep(0.05)
                                print(f"✅ Đã xóa 10 ký tự từ trường nhập liệu (2FA sau Close Payment)")

                                # Nhập code 2FA bằng cách giống chplay
                                if self.input_text_chplay_style(twofa_code):
                                    print("✅ Đã nhập code 2FA thành công (cách chplay sau Close Payment)")

                                    # Nhấn phím Enter để xác nhận 2FA
                                    time.sleep(1)
                                    if self.press_key(66):  # KEYCODE_ENTER = 66
                                        print("✅ Đã nhấn phím Enter sau khi nhập code 2FA (sau Close Payment)")
                                        self._random_sleep(5.0, 7.0, "Chờ xác thực code 2FA (sau Close Payment)")
                                    else:
                                        print("❌ Lỗi khi nhấn phím Enter sau khi nhập code 2FA (sau Close Payment)")
                                else:
                                    print("❌ Lỗi khi nhập code 2FA (cách chplay sau Close Payment)")
                            else:
                                print("⚠️ Bỏ qua nhập 2FA vì không có code hợp lệ (sau Close Payment)")
                        else:
                            print("ℹ️ Không tìm thấy authen_ads.png - Không cần 2FA (sau Close Payment)")
                    else:
                        print("⚠️ File authen_ads.png không tồn tại - Bỏ qua kiểm tra 2FA (sau Close Payment)")

                    # Bước mới: Vuốt lên 1 lần để tìm nút why_are_you.png
                    print("Bước mới: Vuốt lên 1 lần để tìm nút why_are_you.png")
                    # Vuốt từ dưới lên trên với thời gian vuốt ngắn hơn để vuốt nhanh hơn
                    self.swipe(screen_width // 2, screen_height * 4 // 5, screen_width // 2, screen_height // 5, 100)
                    print("✅ Đã vuốt lên 1 lần để tìm nút why_are_you.png")

                    # Đợi một chút sau khi vuốt để màn hình kịp phản hồi
                    self._random_sleep(1.0, 2.0, "Chờ sau khi vuốt lên để tìm nút why_are_you.png")

                    # Tìm và tap vào nút why_are_you.png
                    print("Tìm và tap vào nút why_are_you.png")
                    why_are_you_found = False

                    if os.path.exists(self.why_are_you_template_path):
                        why_are_you_found = self.find_image_and_tap(self.why_are_you_template_path, threshold=0.7, max_retries=5)

                    if why_are_you_found:
                        print("✅ Đã tìm thấy và tap vào nút why_are_you.png")
                        # Đợi một chút sau khi tap vào why_are_you.png
                        self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào why_are_you.png")

                        # Tìm và tap vào nút i_dont_want.png hoặc i_have_had.png
                        print("Tìm và tap vào nút i_dont_want.png hoặc i_have_had.png")
                        i_dont_want_found = False
                        i_have_had_found = False

                        # Thử tìm và tap vào i_dont_want.png trước
                        if os.path.exists(self.i_dont_want_template_path):
                            i_dont_want_found = self.find_image_and_tap(self.i_dont_want_template_path, threshold=0.7, max_retries=3)

                        # Nếu không tìm thấy i_dont_want.png, thử tìm và tap vào i_have_had.png
                        if not i_dont_want_found and os.path.exists(self.i_have_had_template_path):
                            i_have_had_found = self.find_image_and_tap(self.i_have_had_template_path, threshold=0.7, max_retries=3)

                        if i_dont_want_found:
                            print("✅ Đã tìm thấy và tap vào nút i_dont_want.png")
                            # Đợi một chút sau khi tap vào i_dont_want.png
                            self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào i_dont_want.png")
                        elif i_have_had_found:
                            print("✅ Đã tìm thấy và tap vào nút i_have_had.png")
                            # Đợi một chút sau khi tap vào i_have_had.png
                            self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào i_have_had.png")
                        else:
                            print("⚠️ Không tìm thấy nút i_dont_want.png hoặc i_have_had.png, tiếp tục với bước tiếp theo")
                    else:
                        print("⚠️ Không tìm thấy nút why_are_you.png, tiếp tục với bước tiếp theo")
                else:
                    print("⚠️ Không có mật khẩu được cung cấp sau khi tap vào Close Payment")

            # Bước 16: Vuốt lên 1 lần để tìm nút Continue Close
            print("Bước 16: Vuốt lên 1 lần để tìm nút Continue Close")
            # Vuốt từ dưới lên trên với thời gian vuốt ngắn hơn để vuốt nhanh hơn
            # Vuốt từ gần cuối màn hình lên gần đầu màn hình để vuốt dài hơn
            self.swipe(screen_width // 2, screen_height * 4 // 5, screen_width // 2, screen_height // 5, 100)
            print("✅ Đã vuốt lên 1 lần để tìm nút Continue Close")

            # Đợi một chút sau khi vuốt để màn hình kịp phản hồi
            self._random_sleep(1.0, 2.0, "Chờ sau khi vuốt lên để tìm nút Continue Close")

            # Bước 17: Tìm và tap vào nút Continue Close
            print("Bước 17: Tìm và tap vào nút Continue Close")
            continue_close_found = False

            if os.path.exists(self.continue_close_template_path):
                continue_close_found = self.find_image_and_tap(self.continue_close_template_path)

            if not continue_close_found:
                print("⚠️ Không tìm thấy nút Continue Close, tiếp tục với bước tiếp theo")
                # Không return False nữa, tiếp tục thực hiện các bước tiếp theo
            else:
                # Đợi một chút sau khi tap vào Continue Close
                self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào Continue Close")

            # Bước 18: Tìm và tap vào nút Close Payment Profile
            print("Bước 18: Tìm và tap vào nút Close Payment Profile")
            close_payment_profile_found = False

            if os.path.exists(self.close_payment_profile_template_path):
                close_payment_profile_found = self.find_image_and_tap(self.close_payment_profile_template_path)

            if not close_payment_profile_found:
                print("⚠️ Không tìm thấy nút Close Payment Profile, tiếp tục với bước tiếp theo")
                # Không return False nữa, tiếp tục thực hiện các bước tiếp theo
            else:
                # Đợi một chút sau khi tap vào Close Payment Profile
                self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào Close Payment Profile")

            # Đợi trang web tải
            self._random_sleep(8.0, 10.0, "Chờ trang web tải")

            # Bước 19: Về màn hình chính để hoàn thành
            print("Bước 19: Về màn hình chính để hoàn thành")
            self.press_key(3)  # KEYCODE_HOME = 3
            print("✅ Đã về màn hình chính")

            print(f"\n=== Đã hoàn thành chuỗi hành động đóng hồ sơ trên thiết bị {self.serial} ===\n")
            return True

        except Exception as e:
            print(f"❌ Lỗi khi thực hiện chuỗi hành động: {str(e)}")
            return False

    def generate_otp_from_secret(self, secret):
        """
        Tạo mã OTP từ secret key (giống chplay).
        :param secret: Secret key để tạo OTP
        :return: Mã OTP 6 chữ số nếu thành công, None nếu thất bại
        """
        try:
            import pyotp

            # Loại bỏ khoảng trắng và chuyển thành chữ hoa
            clean_secret = secret.replace(" ", "").upper()

            # Tạo đối tượng TOTP
            totp = pyotp.TOTP(clean_secret)

            # Tạo mã OTP
            otp_code = totp.now()

            print(f"✅ Đã tạo mã OTP: {otp_code}")
            return otp_code

        except ImportError:
            print("❌ Không thể import thư viện pyotp. Vui lòng cài đặt: pip install pyotp")
            return None
        except Exception as e:
            print(f"❌ Lỗi khi tạo mã OTP từ secret: {str(e)}")
            return None

    def input_text_chplay_style(self, text):
        """
        Nhập văn bản vào thiết bị với xử lý khoảng trắng và ký tự đặc biệt (giống chplay).
        :param text: Văn bản cần nhập
        :return: True nếu thành công, False nếu thất bại
        """
        try:
            print(f"🔤 Đang nhập văn bản (độ dài: {len(text)} ký tự)")

            # Xử lý ký tự đặc biệt và khoảng trắng giống chplay
            safe_text = text

            # Thay thế xuống dòng bằng khoảng trắng
            safe_text = safe_text.replace('\n', ' ').replace('\r', ' ')

            # Thay thế các ký tự đặc biệt có thể gây lỗi shell (KHÔNG xóa |)
            safe_text = safe_text.replace('"', ' ').replace("'", ' ').replace('\\', ' ')
            safe_text = safe_text.replace('`', ' ').replace('$', ' ').replace('&', ' ')
            safe_text = safe_text.replace('|', ' ').replace(';', ' ').replace('(', ' ')  # Thay | bằng khoảng trắng
            safe_text = safe_text.replace(')', ' ').replace('<', ' ').replace('>', ' ')

            # Thay thế khoảng trắng bằng %s để tránh lỗi shell
            safe_text = safe_text.replace(' ', '%s')

            print(f"🔤 Văn bản sau khi xử lý: {safe_text[:10]}{'...' if len(safe_text) > 10 else ''}")

            # Gửi văn bản qua ADB
            subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "text", safe_text],
                          check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

            print(f"✅ Đã nhập văn bản thành công")
            return True

        except Exception as e:
            print(f"❌ Lỗi khi nhập văn bản: {str(e)}")
            return False

    def press_key(self, keycode):
        """
        Gửi phím keyevent đến thiết bị (giống chplay).
        :param keycode: Mã phím (keycode) cần gửi
        :return: True nếu thành công, False nếu thất bại
        """
        try:
            subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "keyevent", str(keycode)],
                          check=True,
                          creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
            return True
        except Exception as e:
            print(f"❌ Lỗi khi nhấn phím {keycode}: {str(e)}")
            return False

    def tap_at_position(self, x, y):
        """
        Tap tại vị trí cụ thể trên màn hình.
        :param x: Tọa độ x
        :param y: Tọa độ y
        :return: True nếu thành công, False nếu thất bại
        """
        try:
            subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "tap", str(x), str(y)],
                          check=True,
                          creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
            print(f"✅ Đã tap tại vị trí ({x}, {y})")
            return True
        except Exception as e:
            print(f"❌ Lỗi khi tap tại vị trí ({x}, {y}): {str(e)}")
            return False

    def execute_with_loop(self, chrome_template_path=None, threshold=0.8, continue_template_path=None,
                         yes_im_in_template_path=None, got_it_template_path=None, search_template_path=None,
                         ver_that_template_path=None, ok_template_path=None, try_another_way_template_path=None,
                         continue2_template_path=None, menu_template_path=None, setting_template_path=None,
                         close_payment_template_path=None, continue_ver_template_path=None,
                         why_are_you_template_path=None, i_dont_want_template_path=None, i_have_had_template_path=None,
                         check_stop_flag=None, tunit_on_template_path=None, git_it_trang_template_path=None):
        """
        Thực hiện chuỗi hành động đóng hồ sơ 1 lần duy nhất (đã bỏ retry toàn cục).
        :param chrome_template_path: Đường dẫn đến file template chrome.png.
        :param threshold: Ngưỡng độ tương đồng (0-1).
        :param continue_template_path: Đường dẫn đến file template continue.png.
        :param yes_im_in_template_path: Đường dẫn đến file template yes_im_in.png.
        :param got_it_template_path: Đường dẫn đến file template got_it.png.
        :param search_template_path: Đường dẫn đến file template search.png.
        :param ver_that_template_path: Đường dẫn đến file template ver_that.png.
        :param ok_template_path: Đường dẫn đến file template ok.png.
        :param try_another_way_template_path: Đường dẫn đến file template try_another_way.png.
        :param continue2_template_path: Đường dẫn đến file template continue2.png.
        :param menu_template_path: Đường dẫn đến file template menu.png.
        :param setting_template_path: Đường dẫn đến file template setting.png.
        :param close_payment_template_path: Đường dẫn đến file template close_payment.png.
        :param check_stop_flag: Hàm callback để kiểm tra cờ dừng từ AutoK.py.
        :return: True nếu thành công, False nếu thất bại.
        """
        # Cập nhật các đường dẫn template nếu được cung cấp
        if chrome_template_path:
            self.chrome_template_path = chrome_template_path
        if continue_template_path:
            self.continue_template_path = continue_template_path
        if yes_im_in_template_path:
            self.yes_im_in_template_path = yes_im_in_template_path
        if got_it_template_path:
            self.got_it_template_path = got_it_template_path
        if search_template_path:
            self.search_template_path = search_template_path
        if ver_that_template_path:
            self.ver_that_template_path = ver_that_template_path
        if menu_template_path:
            self.menu_template_path = menu_template_path
        if setting_template_path:
            self.setting_template_path = setting_template_path
        if close_payment_template_path:
            self.close_payment_template_path = close_payment_template_path
        if continue_ver_template_path:
            self.continue_ver_template_path = continue_ver_template_path
        # Cập nhật các đường dẫn template mới
        if why_are_you_template_path:
            self.why_are_you_template_path = why_are_you_template_path
        if i_dont_want_template_path:
            self.i_dont_want_template_path = i_dont_want_template_path
        if i_have_had_template_path:
            self.i_have_had_template_path = i_have_had_template_path
        # Cập nhật các đường dẫn template tùy chọn tunit_on, git_it_trang
        if tunit_on_template_path:
            self.tunit_on_template_path = tunit_on_template_path
        if git_it_trang_template_path:
            self.git_it_trang_template_path = git_it_trang_template_path


        # Thực hiện chuỗi hành động 1 lần duy nhất (bỏ retry toàn cục)
        print("\n=== Bắt đầu đóng hồ sơ ===\n")

        # Kiểm tra cờ dừng trước khi bắt đầu
        if check_stop_flag and callable(check_stop_flag) and check_stop_flag():
            print("⚠️ Đã nhận tín hiệu dừng từ người dùng")
            return False

        try:
            result = self.execute_actions(check_stop_flag)
            if result:
                print("✅ Đóng hồ sơ thành công")
                return True
            else:
                print("❌ Đóng hồ sơ thất bại")
                return False
        except Exception as e:
            print(f"❌ Lỗi khi đóng hồ sơ: {str(e)}")
            return False


def DeviceActions_with_password(serial, password, device_index=0, total_devices=1, adb_path=None):
    """
    Hàm tiện ích để tạo đối tượng DeviceActions với mật khẩu.
    :param serial: Số serial của thiết bị.
    :param password: Mật khẩu để nhập khi cần.
    :param device_index: Chỉ số thiết bị trong danh sách thiết bị (để phân phối email).
    :param total_devices: Tổng số thiết bị (để phân phối email).
    :param adb_path: Đường dẫn đến file adb.exe
    :return: Đối tượng DeviceActions đã được khởi tạo với mật khẩu.
    """
    device = DeviceActions(serial, device_index, total_devices, adb_path)
    device.password = password
    return device