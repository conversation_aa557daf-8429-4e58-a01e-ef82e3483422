================================================================================
                        HƯỚNG DẪN SỬ DỤNG AUTOK
================================================================================




Đánh thức màn hình click chuột phải

================================================================================
4. CÁC TÍNH NĂNG CHÍNH
================================================================================

🎯 A. ADDWALLET - THÊM THẺ TỰ ĐỘNG:
┌─────────────────────────────────────────────────────────────────────────────┐
│ Mô tả: Tự động thêm thẻ tín dụng vào Google Wallet                         │
│                                                                             │
│ Cách sử dụng:                                                               │
│ 1. Chuẩn bị file data/card.txt với format:                                 │
│    số_thẻ|tháng|năm|cvv                                                     │
│    Ví dụ: ****************|12|25|123                                       │
│                                                                             │
│ 2. Cấu hình:                                                                │
│    - spinBox_so_lan_add_card: Số lần thêm thẻ (1-100)                      │
│    - spinBox_error: Giới hạn lỗi liên tiếp (1-50)                          │
│                                                                             │
│ 3. Chọn thiết bị và nhấn "AddWallet"                                        │
│                                                                             │
│ Tính năng đặc biệt:                                                         │
│ - Tên random từ data/name.txt                                               │
│ - Số địa chỉ random từ data/address.txt                                     │
│ - Tự động phân phối thẻ cho nhiều thiết bị                                  │
│ - Dừng tự động khi đạt giới hạn lỗi                                         │
└─────────────────────────────────────────────────────────────────────────────┘

🎮 B. MUA - MUA GÓI GAME TỰ ĐỘNG:
┌─────────────────────────────────────────────────────────────────────────────┐
│ Mô tả: Tự động mua gói trong game                                           │
│                                                                             │
│ Cấu hình:                                                                   │
│ - goi1: Số lần mua gói 1 (0-100)                                           │
│ - goi2: Số lần mua gói 2 (0-100)                                           │
│ - goi3: Số lần mua gói 3 (0-100)                                           │
│                                                                             │
│ Tính năng:                                                                  │
│ - Hỗ trợ chế độ swipe cho slide_swipe.png                                  │
│ - Tự động nhập mật khẩu                                                     │
│ - Xử lý nhiều loại gói khác nhau   
   
   Đổi game mua:
   Sử dụng screenshot DOMA
   Cần 4 file chính logo game: đặt tên game1.png
                               các gói 1,2,3 đặt tên goi1,goi2,goi3.png
                                   │
│                                              │
└─────────────────────────────────────────────────────────────────────────────┘

📧 C. RESTORE EMAIL - KHÔI PHỤC EMAIL:
┌─────────────────────────────────────────────────────────────────────────────┐
│ Mô tả: Tự động khôi phục email và quản lý tài khoản                         │
│                                                                             │
│ File data/mail.txt format:                                                  │
│ email|password|recovery_email|proxy_socks5                                  │
│                                                                             │
│ Ví dụ:                                                                      │
│ <EMAIL>|pass123|<EMAIL>|socks5://user:pass:ip:port       │
│                                                                             │
│ Tính năng:                                                                  │
│ - Tự động gán proxy cố định theo email                                      │
│ - Xóa email đã sử dụng (không auto-reset)                                  │
│ - Hỗ trợ OTP từ tableWidget column 3                                        │
│ - Backup tự động qua API                                                    │
│ - Cập nhật trạng thái real-time                                             │
└─────────────────────────────────────────────────────────────────────────────┘

🛒 D. CH PLAY - ĐĂNG NHẬP CH PLAY:
┌─────────────────────────────────────────────────────────────────────────────┐
│ Mô tả: Tự động đăng nhập vào CH Play                                        │
│                                                                             │
│ Tính năng:                                                                  │
│ - Tự động bỏ qua captcha trong auto mode                                    │
│ - Xử lý chplay_ok để nhảy trực tiếp bước 5.5                               │
│ - Xử lý network_chplay.png trong 4s đầu                                     │
│ - Threading đơn giản, hiệu quả                                              │
└─────────────────────────────────────────────────────────────────────────────┘

💳 E. CLOSE PAYMENT - ĐÓNG THANH TOÁN:
┌─────────────────────────────────────────────────────────────────────────────┐
│ Mô tả: Tự động đóng phương thức thanh toán                                  │
│                                                                             │
│ Tính năng:                                                                  │


🗑️ F. DELETE CARD - XÓA THẺ:
┌─────────────────────────────────────────────────────────────────────────────┐
│ Mô tả: Tự động xóa thẻ close, thẻ ver                                           │


================================================================================
5. CẤU HÌNH PROXY
================================================================================

🌐 SMART PROXY ASSIGNMENT:
┌─────────────────────────────────────────────────────────────────────────────┐
│                              │
│                                                                             │
│ 1. PROXY CỐ ĐỊNH (từ mail.txt):                                            │
│    - Email có proxy trong trường 4 → Gán proxy cố định                     │
│    - Format hỗ trợ:                                                         │
│      • user:pass:ip:port                                                    │
│      • socks5://user:pass:ip:port                                           │
│      • socks5://user:pass@ip:port                                           │
│                                                                             │
│ 2. PROXY RANDOM (từ proxy.txt):                                            │
│    - Email không có proxy → Gán random từ proxy.txt                        │
│    - Tránh trùng lặp với proxy cố định                                      │
│                                                                             │
│ 3. HIỂN THỊ:                                                                │
│    🎯 Device 1: **************:56856 (Auth: user) → :2001 (Cố định)       │
│    🎲 Device 2: ***********:8080 (Auth: proxy1) → :2002 (Random)          │
│                                                                             │
│ 4. CẤU HÌNH:                                                                │
│    - Auto-Assign: Bật/tắt tự động gán proxy                                │
│    - Tự động chạy sau 20s khi restore email                                │
│                                │
└─────────────────────────────────────────────────────────────────────────────┘

📁 FILE PROXY.TXT:
Format: ip:port:username:password
Ví dụ:
***********:8080:user1:pass1
***********:8080:user2:pass2

🔧 CẤU HÌNH PROXY:
- Start Port: 2001 (cổng bắt đầu)
- Proxy Type: SOCKS5
- Tự động khởi động proxy server
- Hiển thị trạng thái kết nối

================================================================================
6. QUẢN LÝ DỮ LIỆU
================================================================================

📄 CÁC FILE DỮ LIỆU QUAN TRỌNG:

A. data/config.txt - Cấu hình chung:
goi1=5                    # Số lần mua gói 1
goi2=5                    # Số lần mua gói 2  
goi3=30                   # Số lần mua gói 3
add_card_count=1          # Số lần thêm thẻ
view_per_row=5            # Số thiết bị trên 1 hàng
max_failures=10           # Giới hạn lỗi liên tiếp

B. data/mail.txt - Danh sách email:
email|password|recovery|proxy
<EMAIL>|pass123|<EMAIL>|socks5://user:pass:ip:port

C. data/card.txt - Danh sách thẻ:
****************|12|25|123
****************|01|26|456

D. data/name.txt - Tên random (1000 tên):
Ray
Alfredo
Miller
...

E. data/address.txt - Số địa chỉ random (1-98):
1
2
3
...

================================================================================
7. TÍNH NĂNG NÂNG CAO
================================================================================

🔄 AUTO-ASSIGN PROXY:
- Checkbox "Auto-Assign": Bật tự động gán proxy
- Tự động chạy sau 20s khi restore email
- Gán proxy thông minh theo email

📊 STATUS UPDATE:
- API getstatus: http://127.0.0.1:60708/?getstatus=SERIAL
- Backup API: http://127.0.0.1:60708/?backup=SERIAL
- Cập nhật trạng thái real-time

🎛️ CONTROLS:
- spinBox_view: Số thiết bị/hàng (1-10)
- spinBox_error: Giới hạn lỗi (1-50)
- spinBox_so_lan_add_card: Số lần add thẻ (1-100)


================================================================================
8. XỬ LÝ LỖI VÀ KHẮC PHỤC
================================================================================

❌ CÁC LỖI THƯỜNG GẶP:

1. "Không tìm thấy thiết bị":
   ✅ Kiểm tra USB Debugging đã bật
   ✅ Cài đặt ADB driver
   ✅ Thử cáp USB khác

2. "Template không tìm thấy":
   ✅ Kiểm tra thư mục template/
   ✅ Đảm bảo file .png tồn tại
   ✅ Kiểm tra độ phân giải màn hình

3. "Proxy không kết nối được":
   ✅ Kiểm tra proxy.txt format
   ✅ Test proxy bằng tool khác
   ✅ Kiểm tra firewall
   Tắt tường lửa

4. "Email đã được sử dụng":
   ✅ Xóa file trong mail_used/
   ✅ Hoặc thêm email mới vào mail.txt

5. "Thẻ không hợp lệ":
   ✅ Kiểm tra format card.txt
   ✅ Đảm bảo thẻ chưa hết hạn
   ✅ Kiểm tra CVV đúng

🔧 KHẮC PHỤC:
- Restart AutoK nếu gặp lỗi
- Kiểm tra log để debug
- Backup dữ liệu trước khi chạy
- Test với 1 thiết bị trước

================================================================================
9. TIPS VÀ TRICKS
================================================================================

💡 HIỆU SUẤT TỐI ƯU:



🚀 WORKFLOW KHUYẾN NGHỊ:

1. Chuẩn bị:
   ✅ Kiểm tra tất cả file data/
   ✅ Test kết nối thiết bị
   ✅ Backup cấu hình

2. Chạy thử:
   ✅ Test với 1 thiết bị
   ✅ Kiểm tra từng tính năng
   ✅ Xác nhận kết quả

3. Chạy production:
   ✅ Chọn nhiều thiết bị
   ✅ Monitor liên tục
   ✅ Sẵn sàng can thiệp

4. Sau khi chạy:
   ✅ Backup kết quả
   ✅ Phân tích log
   ✅ Cập nhật cấu hình

================================================================================
📞 HỖ TRỢ VÀ LIÊN HỆ
================================================================================


📝 Ghi chú phiên bản:
- Phiên bản hiện tại hỗ trợ đầy đủ tính năng
- Cập nhật thường xuyên để có tính năng mới
- Backup dữ liệu trước khi cập nhật

================================================================================
                                 HẾT
================================================================================
