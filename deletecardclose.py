import os
import sys
import subprocess
import time
import random
import cv2

# Import path_manager đ<PERSON> quản lý đường dẫn
import path_manager

# Biến toàn cục để lưu đường dẫn ADB
ADB_PATH = "adb"  # Giá trị mặc định

class DeleteCardClose:
    def __init__(self, serial, device_index=0, update_status_callback=None, adb_path=None, password=None, auth_secret=None):
        """
        Khởi tạo đối tượng DeviceActions.
        :param serial: Số serial của thiết bị.
        :param device_index: Chỉ số thiết bị trong danh sách thiết bị được chọn.
        :param update_status_callback: Hàm callback để cập nhật trạng thái trong giao diện người dùng.
        :param adb_path: Đường dẫn đến file adb.exe
        :param password: Mật khẩu từ giao diện (ưu tiên cao nhất)
        :param auth_secret: <PERSON><PERSON> x<PERSON><PERSON> thực từ giao diện (<PERSON><PERSON> tiên cao nhất)
        """
        self.serial = serial
        self.device_index = device_index
        self.password = password or ""  # Mật khẩu để nhập khi cần (ưu tiên từ giao diện)
        self.auth_secret = auth_secret or ""  # Mã xác thực từ giao diện (ưu tiên cao nhất)
        self.update_status_callback = update_status_callback  # Hàm callback để cập nhật trạng thái

        # In thông tin debug
        print(f"🔍 deletecardclose.__init__: serial = {serial}")
        print(f"🔍 deletecardclose.__init__: device_index = {device_index}")
        print(f"🔍 deletecardclose.__init__: adb_path = {adb_path}")
        print(f"🔍 deletecardclose.__init__: sys.executable = {sys.executable}")
        print(f"🔍 deletecardclose.__init__: os.getcwd() = {os.getcwd()}")
        print(f"🔍 deletecardclose.__init__: __file__ = {__file__}")

        # Kiểm tra mật khẩu từ giao diện
        if self.password:
            masked_password = self.password[:2] + '*' * (len(self.password) - 2) if len(self.password) > 2 else '*' * len(self.password)
            print(f"✅ Đã nhận mật khẩu từ giao diện: {masked_password}")
        else:
            print("ℹ️ Không có mật khẩu từ giao diện, sẽ tải từ file mail.txt")

        # Kiểm tra auth_secret từ giao diện
        if self.auth_secret:
            masked_auth_secret = self.auth_secret[:4] + '*' * (len(self.auth_secret) - 4) if len(self.auth_secret) > 4 else '*' * len(self.auth_secret)
            print(f"✅ Đã nhận auth_secret từ giao diện: {masked_auth_secret}")
        else:
            print("ℹ️ Không có auth_secret từ giao diện, sẽ tải từ file mail.txt")

        # Kiểm tra xem có đang chạy từ file exe không
        is_frozen = getattr(sys, 'frozen', False)
        print(f"🔍 deletecardclose.__init__: is_frozen = {is_frozen}")

        # Cập nhật biến toàn cục ADB_PATH nếu được cung cấp
        global ADB_PATH
        if adb_path:
            ADB_PATH = adb_path
            print(f"✅ Đã cập nhật đường dẫn ADB: {ADB_PATH}")

        # Lấy đường dẫn đến thư mục template
        template_dir = path_manager.get_template_path()

        # Tải mật khẩu từ file mail.txt nếu không có mật khẩu từ giao diện
        if not self.password:
            self.load_password_from_mail()

        # Đường dẫn đến các file template
        self.template_dir = template_dir
        self.chrome_template_path = os.path.join(template_dir, "chrome.png")
        self.continue_template_path = os.path.join(template_dir, "continue.png")
        self.yes_im_in_template_path = os.path.join(template_dir, "yes_im_in.png")
        self.yes_im_in_trang_template_path = os.path.join(template_dir, "yes_im_in_trang.png")
        self.got_it_template_path = os.path.join(template_dir, "got_it.png")
        self.search_template_path = os.path.join(template_dir, "search.png")
        self.search2_template_path = os.path.join(template_dir, "search2.png")
        self.ver_that_template_path = os.path.join(template_dir, "ver_that.png")
        self.menu_template_path = os.path.join(template_dir, "menu.png")
        self.setting_template_path = os.path.join(template_dir, "setting.png")
        self.close_payment_template_path = os.path.join(template_dir, "close_payment.png")
        self.continue_close_template_path = os.path.join(template_dir, "continue_close.png")
        self.close_payment_profile_template_path = os.path.join(template_dir, "close_payment_profile.png")

        # Các file template tùy chọn
        self.verify_it_you_template_path = os.path.join(template_dir, "verify_it_you.png")
        self.use_a_difer_template_path = os.path.join(template_dir, "use_a_difer.png")
        self.cancel_template_path = os.path.join(template_dir, "cancel.png")
        self.try_another_way_template_path = os.path.join(template_dir, "try_another_way.png")
        self.enter_your_pass_template_path = os.path.join(template_dir, "enter_your_pass.png")
        self.continue_ver_template_path = os.path.join(template_dir, "continue_ver.png")

        # Thêm các file template mới cho bước sau khi nhập mật khẩu
        self.why_are_you_template_path = os.path.join(template_dir, "why_are_you.png")
        self.i_dont_want_template_path = os.path.join(template_dir, "i_dont_want.png")
        self.i_have_had_template_path = os.path.join(template_dir, "i_have_had.png")

        # Thêm file template cho bước tìm payment_method
        self.payment_method_template_path = os.path.join(template_dir, "payment_method.png")

        # Thêm các file template cho bước tìm thẻ và xóa
        self.card_ver_template_path = os.path.join(template_dir, "card_ver.png")
        self.card_ver2_template_path = os.path.join(template_dir, "card_ver2.png")
        self.card_close_template_path = os.path.join(template_dir, "card_close.png")
        self.card_fix_template_path = os.path.join(template_dir, "card_fix.png")
        self.remove_template_path = os.path.join(template_dir, "remove.png")
        self.remove2_template_path = os.path.join(template_dir, "remove2.png")
        self.ver_it_template_path = os.path.join(template_dir, "ver_it.png")
        self.continue_del_template_path = os.path.join(template_dir, "continue_del.png")
        self.payment_center_template_path = os.path.join(template_dir, "payment_center.png")

        # Thêm các file template mới
        self.tunit_on_template_path = os.path.join(template_dir, "tunit_on.png")
        self.git_it_trang_template_path = os.path.join(template_dir, "git_it_trang.png")
        self.more_template_path = os.path.join(template_dir, "more.png")
        self.signed_in_template_path = os.path.join(template_dir, "signed_in.png")
        self.authen_ads_template_path = os.path.join(template_dir, "authen_ads.png")

    def update_status(self, status):
        """
        Cập nhật trạng thái trong giao diện người dùng.
        :param status: Trạng thái cần cập nhật.
        """
        # Cập nhật trạng thái trong giao diện người dùng
        if self.update_status_callback and callable(self.update_status_callback):
            self.update_status_callback(status)

        # In ra console để dễ theo dõi (với thông tin thiết bị chỉ hiển thị trong console)
        print(f"📱 [{self.serial}] {status}")

    def load_password_from_mail(self):
        """
        Đọc mật khẩu từ file mail.txt dựa trên chỉ số thiết bị
        Format của file: email|password|authentication
        """
        try:
            # Danh sách các vị trí có thể chứa file mail.txt
            possible_paths = []

            # Kiểm tra xem có đang chạy từ file exe không
            is_frozen = getattr(sys, 'frozen', False)
            print(f"🔍 load_password_from_mail: is_frozen = {is_frozen}")

            # Thêm đường dẫn đặc biệt cho file exe (ưu tiên cao nhất)
            if is_frozen:
                # Thêm đường dẫn tương đối với thư mục chứa file exe
                exe_dir = os.path.dirname(sys.executable)
                possible_paths.append(os.path.join(exe_dir, "data", "mail.txt"))
                possible_paths.append(os.path.join(exe_dir, "mail.txt"))

            # Thêm các đường dẫn thông thường
            base_path = os.path.abspath(".")
            possible_paths.extend([
                # Trong thư mục hiện tại
                os.path.join(base_path, "mail.txt"),
                # Trong thư mục data
                os.path.join(base_path, "data", "mail.txt"),
                # Trong thư mục cha
                os.path.join(os.path.dirname(base_path), "mail.txt"),
                # Trong thư mục data của thư mục cha
                os.path.join(os.path.dirname(base_path), "data", "mail.txt"),
            ])

            # Thử sử dụng path_manager
            try:
                # Thử sử dụng get_txt_path (phương pháp đơn giản nhất)
                mail_file_path = path_manager.get_txt_path("mail.txt")
                possible_paths.append(mail_file_path)
                print(f"🔍 Đường dẫn file mail.txt từ get_txt_path: {mail_file_path}")
            except Exception as e:
                print(f"⚠️ Lỗi khi sử dụng get_txt_path: {str(e)}")

            try:
                # Sử dụng get_data_path như phương án dự phòng
                mail_file_path = path_manager.get_data_path("mail.txt")
                possible_paths.append(mail_file_path)
                print(f"🔍 Đường dẫn file mail.txt từ get_data_path: {mail_file_path}")
            except Exception as e:
                print(f"⚠️ Lỗi khi sử dụng get_data_path: {str(e)}")

            # In thông tin debug
            print(f"🔍 Danh sách các đường dẫn có thể chứa mail.txt:")
            for idx, path in enumerate(possible_paths):
                print(f"  {idx+1}. {path}")

            # Tìm file mail.txt trong danh sách đường dẫn
            mail_file_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    mail_file_path = path
                    print(f"✅ Đã tìm thấy file mail.txt tại: {mail_file_path}")
                    break

            # Nếu không tìm thấy, tạo file mail.txt mẫu
            if mail_file_path is None:
                print(f"⚠️ Không tìm thấy file mail.txt trong tất cả các đường dẫn")
                self.update_status(f"⚠️ Không tìm thấy file mail.txt")

                # Chọn đường dẫn để tạo file mail.txt mẫu
                if is_frozen:
                    # Nếu đang chạy từ file exe, tạo file trong thư mục data bên cạnh file exe
                    exe_dir = os.path.dirname(sys.executable)
                    data_dir = os.path.join(exe_dir, "data")
                    if not os.path.exists(data_dir):
                        os.makedirs(data_dir, exist_ok=True)
                    mail_file_path = os.path.join(data_dir, "mail.txt")
                else:
                    # Nếu đang chạy từ script, tạo file trong thư mục data
                    data_dir = os.path.join(base_path, "data")
                    if not os.path.exists(data_dir):
                        os.makedirs(data_dir, exist_ok=True)
                    mail_file_path = os.path.join(data_dir, "mail.txt")

                # Tạo file mail.txt mẫu
                try:
                    with open(mail_file_path, "w") as f:
                        f.write("# Danh sách email (format: email|password|authentication)\n")
                        f.write("# Ví dụ: <EMAIL>|password123|ABCDEFGHIJKLMNOP\n")
                    print(f"✅ Đã tạo file mail.txt mẫu tại: {mail_file_path}")
                    self.update_status(f"✅ Đã tạo file mail.txt mẫu")
                except Exception as e:
                    print(f"❌ Lỗi khi tạo file mail.txt mẫu: {str(e)}")
                return

            # Kiểm tra thư mục cha
            parent_dir = os.path.dirname(mail_file_path)
            print(f"🔍 Thư mục cha: {parent_dir}")
            if os.path.exists(parent_dir):
                print(f"✅ Thư mục cha tồn tại: {parent_dir}")
                print(f"🔍 Nội dung thư mục cha:")
                try:
                    for item in os.listdir(parent_dir):
                        print(f"  - {item}")
                except Exception as e:
                    print(f"❌ Lỗi khi liệt kê nội dung thư mục cha: {str(e)}")
            else:
                print(f"❌ Thư mục cha không tồn tại: {parent_dir}")
                try:
                    os.makedirs(parent_dir, exist_ok=True)
                    print(f"✅ Đã tạo thư mục cha: {parent_dir}")
                except Exception as e:
                    print(f"❌ Lỗi khi tạo thư mục cha: {str(e)}")

            print(f"✅ File mail.txt tồn tại: {mail_file_path}")

            # Đọc danh sách email từ file
            try:
                with open(mail_file_path, "r", encoding="utf-8") as file:
                    lines = file.readlines()
                print(f"✅ Đã đọc {len(lines)} dòng từ file {mail_file_path}")
            except Exception as e:
                print(f"❌ Lỗi khi đọc file {mail_file_path}: {str(e)}")
                # Thử đọc với encoding khác
                try:
                    with open(mail_file_path, "r") as file:
                        lines = file.readlines()
                    print(f"✅ Đã đọc {len(lines)} dòng từ file {mail_file_path} (không có encoding)")
                except Exception as e2:
                    print(f"❌ Lỗi khi đọc file {mail_file_path} (không có encoding): {str(e2)}")
                    self.update_status(f"❌ Không thể đọc file mail.txt")
                    return

            # In nội dung file để debug (ẩn mật khẩu)
            print(f"🔍 Nội dung file mail.txt:")
            for i, line in enumerate(lines):
                # Ẩn mật khẩu trong dòng
                if "|" in line:
                    parts = line.split("|")
                    if len(parts) >= 2:
                        masked_line = parts[0] + "|" + "*" * len(parts[1])
                        if len(parts) > 2:
                            masked_line += "|" + "|".join(parts[2:])
                        print(f"  {i+1}. {masked_line}")
                    else:
                        print(f"  {i+1}. {line.strip()}")
                else:
                    print(f"  {i+1}. {line.strip()}")

            # Lọc các dòng trống và dòng comment
            valid_lines = []
            for line in lines:
                line = line.strip()
                if not line or line.startswith("#"):
                    continue
                valid_lines.append(line)

            print(f"✅ Đã lọc được {len(valid_lines)} dòng hợp lệ từ {len(lines)} dòng")

            # Kiểm tra xem có email nào không
            if not valid_lines:
                print(f"⚠️ Không có email nào trong file {mail_file_path}")
                print(f"⚠️ Vui lòng thêm email vào file với định dạng: email|password|authentication")
                self.update_status(f"⚠️ Không có email nào trong file mail.txt")
                return

            # Kiểm tra xem có đủ email cho thiết bị không
            if self.device_index >= len(valid_lines):
                print(f"⚠️ Không đủ email trong file {mail_file_path} cho thiết bị {self.serial} (index {self.device_index})")
                print(f"⚠️ Số email hiện có: {len(valid_lines)}, cần ít nhất {self.device_index + 1} email")
                self.update_status(f"⚠️ Hết mail - Không đủ email trong file mail.txt")

                # Nếu không đủ email, sử dụng email theo chỉ số thiết bị mod số lượng email
                if len(valid_lines) > 0:
                    self.device_index = self.device_index % len(valid_lines)
                    print(f"ℹ️ Sử dụng email ở vị trí {self.device_index + 1} cho thiết bị {self.serial}")
                else:
                    return

            # Lấy thông tin email tương ứng với chỉ số thiết bị
            email_line = valid_lines[self.device_index]
            print(f"🔍 Dòng email được chọn: {email_line.split('|')[0]}|***")

            # Phân tích dòng email
            email_info = email_line.split('|')
            print(f"🔍 Số phần tử sau khi phân tích: {len(email_info)}")

            # Kiểm tra định dạng email
            if len(email_info) >= 2:
                # Lấy mật khẩu từ cột thứ 2
                self.password = email_info[1].strip()
                # Hiển thị 2 ký tự đầu của mật khẩu và che phần còn lại
                masked_password = self.password[:2] + '*' * (len(self.password) - 2) if len(self.password) > 2 else '*' * len(self.password)
                print(f"✅ Đã tải mật khẩu cho thiết bị {self.serial} (index {self.device_index}): {masked_password}")
                self.update_status(f"✅ Đã tải mật khẩu từ mail.txt")
            else:
                print(f"⚠️ Định dạng email không hợp lệ: {email_line.split('|')[0]}|***")
                print(f"⚠️ Định dạng cần thiết: email|password|authentication")
                self.update_status(f"⚠️ Định dạng email không hợp lệ")

        except Exception as e:
            print(f"❌ Lỗi khi tải mật khẩu từ file mail.txt: {str(e)}")
            self.update_status(f"❌ Lỗi khi tải mật khẩu: {str(e)}")

    def _random_sleep(self, min_seconds, max_seconds, message=None, check_stop_flag=None):
        """
        Tạm dừng thực thi trong một khoảng thời gian ngẫu nhiên.
        :param min_seconds: Thời gian tối thiểu (giây).
        :param max_seconds: Thời gian tối đa (giây).
        :param message: Thông báo tùy chọn để hiển thị.
        :param check_stop_flag: Hàm callback để kiểm tra cờ dừng.
        :return: True nếu đã dừng do cờ dừng, False nếu đã ngủ hết thời gian.
        """
        seconds = random.uniform(min_seconds, max_seconds)
        if message:
            print(f"{message} ({seconds:.2f}s)")

        # Nếu có hàm kiểm tra cờ dừng, kiểm tra mỗi 0.05 giây
        if check_stop_flag and callable(check_stop_flag):
            start_time = time.time()
            end_time = start_time + seconds

            while time.time() < end_time:
                # Kiểm tra cờ dừng
                if check_stop_flag():
                    print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi đang chờ")
                    # Thông báo thêm để dễ theo dõi
                    print(f"⚠️ Dừng quá trình ngủ tại {message if message else 'không có thông báo'}")
                    return True  # Đã dừng do cờ dừng

                # Ngủ một khoảng thời gian ngắn hơn để phản ứng nhanh hơn với cờ dừng
                time.sleep(0.05)

            return False  # Đã ngủ hết thời gian
        else:
            # Nếu không có hàm kiểm tra cờ dừng, ngủ bình thường
            time.sleep(seconds)
            return False

    def input_text_chplay_style(self, text):
        """
        Nhập văn bản vào thiết bị với xử lý khoảng trắng và ký tự đặc biệt (giống chplay).
        :param text: Văn bản cần nhập
        :return: True nếu thành công, False nếu thất bại
        """
        try:
            print(f"🔤 Đang nhập văn bản (độ dài: {len(text)} ký tự)")

            # Xử lý ký tự đặc biệt và khoảng trắng giống chplay
            safe_text = text

            # Thay thế xuống dòng bằng khoảng trắng
            safe_text = safe_text.replace('\n', ' ').replace('\r', ' ')

            # Thay thế các ký tự đặc biệt có thể gây lỗi shell (KHÔNG xóa |)
            safe_text = safe_text.replace('"', ' ').replace("'", ' ').replace('\\', ' ')
            safe_text = safe_text.replace('`', ' ').replace('$', ' ').replace('&', ' ')
            safe_text = safe_text.replace('|', ' ').replace(';', ' ').replace('(', ' ')  # Thay | bằng khoảng trắng
            safe_text = safe_text.replace(')', ' ').replace('<', ' ').replace('>', ' ')

            # Thay thế khoảng trắng bằng %s để tránh lỗi shell
            safe_text = safe_text.replace(' ', '%s')

            print(f"🔤 Văn bản sau khi xử lý: {safe_text[:10]}{'...' if len(safe_text) > 10 else ''}")

            # Gửi văn bản qua ADB
            subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "text", safe_text],
                          check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

            print(f"✅ Đã nhập văn bản thành công")
            return True

        except Exception as e:
            print(f"❌ Lỗi khi nhập văn bản: {str(e)}")
            return False

    def press_key(self, keycode):
        """
        Gửi phím keyevent đến thiết bị (giống chplay).
        :param keycode: Mã phím (keycode) cần gửi
        :return: True nếu thành công, False nếu thất bại
        """
        try:
            subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "keyevent", str(keycode)],
                          check=True,
                          creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
            return True
        except Exception as e:
            print(f"❌ Lỗi khi nhấn phím {keycode}: {str(e)}")
            return False

    def tap_at_position(self, x, y):
        """
        Tap tại vị trí cụ thể trên màn hình.
        :param x: Tọa độ x
        :param y: Tọa độ y
        :return: True nếu thành công, False nếu thất bại
        """
        try:
            subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "tap", str(x), str(y)],
                          check=True,
                          creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
            print(f"✅ Đã tap tại vị trí ({x}, {y})")
            return True
        except Exception as e:
            print(f"❌ Lỗi khi tap tại vị trí ({x}, {y}): {str(e)}")
            return False

    def swipe(self, start_x, start_y, end_x, end_y, duration=200):
        """
        Thực hiện thao tác vuốt trên màn hình.
        :param start_x: Tọa độ X bắt đầu.
        :param start_y: Tọa độ Y bắt đầu.
        :param end_x: Tọa độ X kết thúc.
        :param end_y: Tọa độ Y kết thúc.
        :param duration: Thời gian vuốt (ms). Mặc định là 100ms để vuốt nhanh hơn.
        """
        try:
            # Tính toán khoảng cách vuốt
            distance = ((end_x - start_x) ** 2 + (end_y - start_y) ** 2) ** 0.5
            print(f"🔄 Thực hiện vuốt nhanh và dài: khoảng cách {distance:.0f} pixels, thời gian {duration}ms")

            # Thực hiện vuốt nhanh và dài hơn
            result = subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "swipe",
                            str(start_x), str(start_y), str(end_x), str(end_y), str(duration)],
                            check=True, capture_output=True, text=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

            if result.stdout:
                print(f"✅ Kết quả stdout: {result.stdout}")
            if result.stderr:
                print(f"⚠️ Kết quả stderr: {result.stderr}")

            print(f"✅ Đã vuốt từ ({start_x}, {start_y}) đến ({end_x}, {end_y}) trong {duration}ms")
        except subprocess.CalledProcessError as e:
            print(f"❌ Lỗi khi vuốt: {str(e)}")
            if e.stdout:
                print(f"⚠️ Kết quả stdout: {e.stdout}")
            if e.stderr:
                print(f"⚠️ Kết quả stderr: {e.stderr}")

            # Thử lại với thời gian vuốt ngắn hơn
            try:
                print(f"🔄 Thử lại với thời gian vuốt ngắn hơn: 50ms")
                subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "swipe",
                                str(start_x), str(start_y), str(end_x), str(end_y), "50"], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                print(f"✅ Đã vuốt từ ({start_x}, {start_y}) đến ({end_x}, {end_y}) trong 50ms")
            except Exception as e2:
                print(f"❌ Lỗi khi thử lại vuốt: {str(e2)}")

    def send_text(self, text, delay_range=(0.01, 0.08)):
        """
        Gửi văn bản đến thiết bị một cách chậm rãi từng ký tự.
        :param text: Văn bản cần gửi.
        :param delay_range: Tuple (min_delay, max_delay) độ trễ giữa các ký tự.
        """
        if not text:
            print("⚠️ Văn bản trống, không gửi gì cả")
            return

        print(f"🔄 Gửi chậm văn bản: {text[:2]}{'*' * (len(text) - 2)}")

        # Loại bỏ ký tự không hợp lệ với ADB input
        text = text.replace('|', '')

        try:
            for idx, char in enumerate(text):
                subprocess.run(
                    [ADB_PATH, "-s", self.serial, "shell", "input", "text", char],
                    check=True,
                    creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
                )
                print(f"   ⌨️ Đã gửi ký tự {idx + 1}/{len(text)}: '{char}'")
                self._random_sleep(*delay_range, "Đợi giữa các ký tự")
            print(f"✅ Đã gửi văn bản thành công: {text[:2]}{'*' * (len(text) - 2)}")
        except Exception as e:
            print(f"❌ Lỗi khi gửi văn bản từng ký tự: {e}")
            raise

    def input_text(self, text):
        """
        Nhập văn bản vào thiết bị với xử lý khoảng trắng và ký tự đặc biệt (giống chplay.py).
        :param text: Văn bản cần nhập
        :return: True nếu thành công, False nếu thất bại
        """
        try:
            print(f"🔤 Đang nhập văn bản (độ dài: {len(text)} ký tự)")

            # Xử lý ký tự đặc biệt và khoảng trắng
            safe_text = text

            # Thay thế xuống dòng bằng khoảng trắng
            safe_text = safe_text.replace('\n', ' ').replace('\r', ' ')

            # Thay thế các ký tự đặc biệt có thể gây lỗi shell
            safe_text = safe_text.replace('"', ' ').replace("'", ' ').replace('\\', ' ')
            safe_text = safe_text.replace('`', ' ').replace('$', ' ').replace('&', ' ')
            safe_text = safe_text.replace('|', ' ').replace(';', ' ').replace('(', ' ')
            safe_text = safe_text.replace(')', ' ').replace('<', ' ').replace('>', ' ')

            # Loại bỏ khoảng trắng thừa
            safe_text = ' '.join(safe_text.split())

            print(f"📝 Văn bản sau khi xử lý (độ dài: {len(safe_text)} ký tự): {safe_text[:100]}{'...' if len(safe_text) > 100 else ''}")

            # Sử dụng quotes để bao bọc text
            quoted_text = f'"{safe_text}"'

            subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "text", quoted_text],
                          check=True,
                          creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

            print(f"✅ Đã nhập văn bản thành công")
            return True

        except Exception as e:
            print(f"❌ Lỗi khi nhập văn bản: {str(e)}")

            # Thử phương pháp fallback - nhập từng từ
            try:
                print(f"🔄 Thử phương pháp fallback - nhập từng phần...")

                # Chia text thành tất cả các từ (không giới hạn)
                words = safe_text.split()  # Lấy tất cả từ

                for i, word in enumerate(words):
                    # Hiển thị progress mỗi 10 từ để không spam log
                    if i % 10 == 0 or i == len(words) - 1:
                        print(f"📝 Đang nhập từ {i+1}/{len(words)}: {word}")

                    subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "text", word],
                                  check=True,
                                  creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

                    # Thêm khoảng trắng sau mỗi từ (trừ từ cuối)
                    if i < len(words) - 1:
                        subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "text", " "],
                                      check=True,
                                      creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

                    # Đợi một chút giữa các từ (giảm thời gian để nhanh hơn)
                    time.sleep(0.1)

                print(f"✅ Đã nhập văn bản thành công (phương pháp fallback)")
                return True

            except Exception as e2:
                print(f"❌ Phương pháp fallback cũng thất bại: {str(e2)}")
                return False


    def find_image_on_screen(self, template_path, threshold=0.8, roi=None, check_stop_flag=None):
        """
        Tìm hình ảnh trên màn hình nhưng không tap vào.
        :param template_path: Đường dẫn đến file template.
        :param threshold: Ngưỡng độ tương đồng (0-1).
        :param roi: Region of Interest - Vùng quan tâm [x, y, width, height]. Nếu None, tìm trên toàn bộ màn hình.
        :param check_stop_flag: Hàm callback để kiểm tra cờ dừng.
        :return: (True, location) nếu tìm thấy, (False, None) nếu không.
        """
        try:
            # Kiểm tra cờ dừng nếu có
            if check_stop_flag and callable(check_stop_flag):
                if check_stop_flag():
                    print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi tìm {os.path.basename(template_path)}")
                    return False, None

            # Bắt đầu đo thời gian
            start_time = time.time()

            # Chụp ảnh màn hình
            print(f"📸 Đang chụp ảnh màn hình để tìm {os.path.basename(template_path)} (không tap)...")
            screenshot_path = path_manager.get_screen_path(f"screen_{self.serial}.png")

            # Sử dụng exec-out để tăng tốc độ chụp ảnh
            with open(screenshot_path, "wb") as f:
                subprocess.run([ADB_PATH, "-s", self.serial, "exec-out", "screencap", "-p"],
                              stdout=f, check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

            capture_time = time.time() - start_time
            print(f"✅ Đã chụp ảnh màn hình thành công: {screenshot_path} (thời gian: {capture_time:.2f}s)")

            # Kiểm tra cờ dừng sau khi chụp ảnh
            if check_stop_flag and callable(check_stop_flag):
                if check_stop_flag():
                    print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng sau khi chụp ảnh màn hình")
                    return False, None

            # Đợi một chút sau khi chụp ảnh để đảm bảo file đã được lưu hoàn toàn
            time.sleep(0.5)

            # Đọc ảnh template và ảnh màn hình
            template = cv2.imread(template_path)
            screenshot = cv2.imread(screenshot_path)

            if template is None:
                print(f"❌ Không thể đọc ảnh template: {template_path}")
                # Kiểm tra kích thước file template
                try:
                    template_size = os.path.getsize(template_path)
                    print(f"ℹ️ Kích thước file template: {template_size} bytes")
                    if template_size == 0:
                        print(f"❌ File template có kích thước 0 bytes")
                except Exception as e:
                    print(f"❌ Lỗi khi kiểm tra kích thước file template: {str(e)}")
                return False, None

            if screenshot is None:
                print(f"❌ Không thể đọc ảnh màn hình: {screenshot_path}")
                # Thử đợi thêm một chút và đọc lại ảnh màn hình
                time.sleep(1.0)
                screenshot = cv2.imread(screenshot_path)
                if screenshot is None:
                    print(f"❌ Vẫn không thể đọc ảnh màn hình sau khi đợi thêm")
                    return False, None

            # In thông tin về kích thước ảnh để debug
            print(f"ℹ️ Kích thước template: {template.shape}")
            print(f"ℹ️ Kích thước screenshot: {screenshot.shape}")

            # Chuyển đổi ảnh sang thang độ xám
            template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
            screenshot_gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)

            # Lấy kích thước template
            template_h, template_w = template_gray.shape

            # Nếu có ROI, cắt ảnh màn hình theo ROI
            if roi:
                x, y, w, h = roi
                screenshot_gray = screenshot_gray[y:y+h, x:x+w]
                roi_offset_x, roi_offset_y = x, y
            else:
                roi_offset_x, roi_offset_y = 0, 0

            # Tìm kiếm template trong ảnh màn hình
            result = cv2.matchTemplate(screenshot_gray, template_gray, cv2.TM_CCOEFF_NORMED)
            _, max_val, _, max_loc = cv2.minMaxLoc(result)

            # Tính thời gian tổng cộng
            total_time = time.time() - start_time

            # Nếu độ khớp lớn hơn ngưỡng, trả về True và vị trí
            if max_val >= threshold:
                # Tính toán tọa độ trung tâm của template trên màn hình
                center_x = roi_offset_x + max_loc[0] + template_w // 2
                center_y = roi_offset_y + max_loc[1] + template_h // 2

                print(f"✅ Đã tìm thấy {os.path.basename(template_path)} tại ({center_x}, {center_y}) (độ khớp: {max_val:.2f}, thời gian: {total_time:.2f}s)")
                return True, (center_x, center_y)
            else:
                print(f"❌ Không tìm thấy {os.path.basename(template_path)} (max_val = {max_val:.2f}, thời gian: {total_time:.2f}s)")
                return False, None

        except Exception as e:
            print(f"❌ Lỗi khi tìm {os.path.basename(template_path)}: {str(e)}")
            return False, None

    def find_image_and_tap(self, template_path, threshold=0.8, max_retries=1, roi=None, check_stop_flag=None):
        """
        Tìm hình ảnh trên màn hình và tap vào vị trí tìm thấy.
        :param template_path: Đường dẫn đến file template.
        :param threshold: Ngưỡng độ tương đồng (0-1).
        :param max_retries: Số lần thử lại tối đa.
        :param roi: Region of Interest - Vùng quan tâm [x, y, width, height]. Nếu None, tìm trên toàn bộ màn hình.
        :param check_stop_flag: Hàm callback để kiểm tra cờ dừng.
        :return: True nếu tìm thấy và tap thành công, False nếu không.
        """
        for attempt in range(max_retries):
            # Kiểm tra cờ dừng nếu có
            if check_stop_flag and callable(check_stop_flag):
                if check_stop_flag():
                    print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong lần thử {attempt + 1}/{max_retries}")
                    return False

            print(f"🔍 Tìm và tap {os.path.basename(template_path)} (lần thử {attempt + 1}/{max_retries})")

            # Tìm hình ảnh trên màn hình
            found, location = self.find_image_on_screen(template_path, threshold, roi, check_stop_flag)

            if found and location:
                center_x, center_y = location

                # Tap vào vị trí tìm thấy
                try:
                    print(f"👆 Tap vào vị trí ({center_x}, {center_y})")
                    subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "tap", str(center_x), str(center_y)],
                                  check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                    print(f"✅ Đã tap thành công vào {os.path.basename(template_path)}")
                    return True
                except Exception as e:
                    print(f"❌ Lỗi khi tap vào {os.path.basename(template_path)}: {str(e)}")
                    # Tiếp tục thử lại nếu còn lần thử
                    continue

            # Nếu không tìm thấy và còn lần thử, đợi một chút rồi thử lại
            if attempt < max_retries - 1:
                print(f"⌛ Không tìm thấy, thử lại sau 1 giây...")

                # Sử dụng _random_sleep để có thể kiểm tra cờ dừng trong khi chờ
                if check_stop_flag and callable(check_stop_flag):
                    if self._random_sleep(1.0, 1.0, f"Chờ trước khi thử lại tìm {os.path.basename(template_path)}", check_stop_flag):
                        print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ thử lại")
                        return False
                else:
                    time.sleep(1.0)

        print(f"❌ Đã thử {max_retries} lần nhưng không tìm thấy hoặc không tap được vào {os.path.basename(template_path)}")
        return False

    def wait_for_image_and_tap(self, template_path, threshold=0.8, timeout=15.0, check_interval=2.0, roi=None, max_retries=1, check_stop_flag=None):
        """
        Chờ cho đến khi tìm thấy hình ảnh và tap vào đó.
        :param template_path: Đường dẫn template hình ảnh.
        :param threshold: Ngưỡng độ khớp.
        :param timeout: Tổng thời gian chờ tối đa (giây).
        :param check_interval: Khoảng thời gian giữa mỗi lần kiểm tra (giây).
        :param roi: ROI nếu cần.
        :param max_retries: Số lần thử lại tối đa cho mỗi lần tìm.
        :param check_stop_flag: Hàm callback để kiểm tra cờ dừng.
        :return: True nếu tìm thấy và tap được, False nếu hết thời gian hoặc nhận tín hiệu dừng.
        """
        start_time = time.time()
        while time.time() - start_time < timeout:
            # Kiểm tra cờ dừng nếu có
            if check_stop_flag and callable(check_stop_flag):
                if check_stop_flag():
                    print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi đang tìm {os.path.basename(template_path)}")
                    return False

            found = self.find_image_and_tap(template_path, threshold=threshold, max_retries=max_retries, roi=roi)
            if found:
                return True

            time_left = timeout - (time.time() - start_time)
            print(f"⌛ Chưa thấy, chờ thêm {check_interval:.1f}s (còn {time_left:.1f}s)...")

            # Sử dụng _random_sleep để có thể kiểm tra cờ dừng trong khi chờ
            if check_stop_flag and callable(check_stop_flag):
                if self._random_sleep(check_interval, check_interval, f"Chờ trước khi tìm lại {os.path.basename(template_path)}", check_stop_flag):
                    print(f"⚠️ Đã nhận tín hiệu dừng từ người dùng trong khi chờ tìm lại {os.path.basename(template_path)}")
                    return False
            else:
                time.sleep(check_interval)

        print(f"❌ Hết thời gian chờ nhưng không tìm thấy {os.path.basename(template_path)}")
        return False

    def execute_steps_4_to_8(self, check_stop_flag=None):
        """
        Thực hiện bước 4-8: Continue, Yes I'm in, Got it và các bước xác thực.
        :param check_stop_flag: Hàm callback để kiểm tra cờ dừng
        :return: True nếu thành công, False nếu thất bại
        """
        try:
            def should_stop():
                return check_stop_flag and callable(check_stop_flag) and check_stop_flag()

            print("🔄 Bắt đầu thực hiện bước 4-8")

            # Bước 4: Tìm và tap vào nút Continue
            print("Bước 4: Tìm và tap vào nút Continue")
            continue_found = False

            if should_stop():
                return False

            if os.path.exists(self.continue_template_path):
                continue_found = self.find_image_and_tap(self.continue_template_path, check_stop_flag=check_stop_flag)

            if not continue_found:
                print("⚠️ Không tìm thấy nút Continue, tiếp tục với bước tiếp theo")
            else:
                if self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào Continue", check_stop_flag):
                    return False

            # Bước 5: Tìm và tap vào nút Yes I'm in
            print("Bước 5: Tìm và tap vào nút Yes I'm in")
            yes_im_in_found = False

            if should_stop():
                return False

            # Thử tìm yes_im_in_trang.png trước
            if os.path.exists(self.yes_im_in_trang_template_path):
                yes_im_in_found = self.find_image_and_tap(self.yes_im_in_trang_template_path, max_retries=2, check_stop_flag=check_stop_flag)
                if yes_im_in_found:
                    print("✅ Đã tìm thấy và tap vào yes_im_in_trang.png")

            # Nếu không tìm thấy yes_im_in_trang.png, thử yes_im_in.png
            if not yes_im_in_found and os.path.exists(self.yes_im_in_template_path):
                yes_im_in_found = self.find_image_and_tap(self.yes_im_in_template_path, max_retries=2, check_stop_flag=check_stop_flag)
                if yes_im_in_found:
                    print("✅ Đã tìm thấy và tap vào yes_im_in.png")

            if not yes_im_in_found:
                print("⚠️ Không tìm thấy cả yes_im_in_trang.png và yes_im_in.png, tiếp tục với bước tiếp theo")
            else:
                if self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào Yes I'm in", check_stop_flag):
                    return False

            # Bước 6: Kiểm tra more và vuốt nếu cần
            print("Bước 6: Kiểm tra more và vuốt nếu cần")
            if should_stop():
                return False

            more_found = False
            if os.path.exists(self.more_template_path):
                found, _ = self.find_image_on_screen(self.more_template_path, check_stop_flag=check_stop_flag)
                more_found = found

                if more_found:
                    screen_width = 1080
                    screen_height = 1920
                    self.swipe(screen_width // 2, screen_height * 4 // 5,
                              screen_width // 2, screen_height // 5, 200)
                    if self._random_sleep(2.0, 3.0, "Chờ sau khi vuốt", check_stop_flag):
                        return False

            # Bước 7: Tìm và tap vào nút Got it
            print("Bước 7: Tìm và tap vào nút Got it")
            if should_stop():
                return False

            got_it_found = False
            if os.path.exists(self.got_it_template_path):
                got_it_found = self.wait_for_image_and_tap(self.got_it_template_path, check_stop_flag=check_stop_flag)

            if not got_it_found:
                print("⚠️ Không tìm thấy nút Got it, thử tìm tunit_on và git_it_trang")

                # Bước 7.1: Tìm và tap vào nút tunit_on
                tunit_on_found = False
                if os.path.exists(self.tunit_on_template_path):
                    tunit_on_found = self.find_image_and_tap(self.tunit_on_template_path, check_stop_flag=check_stop_flag)

                    if tunit_on_found:
                        print("✅ Đã tìm thấy và tap vào nút tunit_on")
                        screen_width = 1080
                        screen_height = 1920
                        self.swipe(screen_width // 2, screen_height * 4 // 5, screen_width // 2, screen_height // 5, 300)
                        if self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào tunit_on", check_stop_flag):
                            return False

                        # Bước 7.2: Tìm và tap vào nút git_it_trang
                        git_it_trang_found = False
                        if os.path.exists(self.git_it_trang_template_path):
                            git_it_trang_found = self.find_image_and_tap(self.git_it_trang_template_path, check_stop_flag=check_stop_flag)

                        if git_it_trang_found:
                            print("✅ Đã tìm thấy và tap vào nút git_it_trang")
                            if self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào git_it_trang", check_stop_flag):
                                return False
            else:
                if self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào Got it", check_stop_flag):
                    return False

            # Bước 8: Tìm và tap vào ô tìm kiếm
            print("Bước 8: Tìm và tap vào ô tìm kiếm")
            if should_stop():
                return False

            search_found = False
            if os.path.exists(self.search_template_path):
                search_found = self.wait_for_image_and_tap(self.search_template_path, max_retries=2, check_stop_flag=check_stop_flag)

            if not search_found and os.path.exists(self.search2_template_path):
                search_found = self.find_image_and_tap(self.search2_template_path, check_stop_flag=check_stop_flag)

            if not search_found:
                print("❌ Không tìm thấy ô tìm kiếm - Bước 4-8 thất bại")
                return False

            if self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào ô tìm kiếm", check_stop_flag):
                return False

            print("✅ Đã hoàn thành bước 4-8 thành công")
            return True

        except Exception as e:
            print(f"❌ Lỗi khi thực hiện bước 4-8: {str(e)}")
            return False

    def load_auth_secret_from_mail_file(self):
        """
        Tải mã xác thực (authentication secret) từ giao diện (ưu tiên) hoặc từ file mail.txt dựa trên chỉ số thiết bị (giống chplay).
        :return: Mã xác thực nếu tìm thấy, None nếu không tìm thấy
        """
        try:
            # Nếu đã có mã xác thực từ giao diện, sử dụng nó (ưu tiên cao nhất)
            if self.auth_secret:
                print(f"✅ Sử dụng mã xác thực từ giao diện: {self.auth_secret[:4]}{'*' * (len(self.auth_secret) - 4) if len(self.auth_secret) > 4 else '*' * len(self.auth_secret)}")
                return self.auth_secret

            # Lấy đường dẫn đến file mail.txt
            mail_file_path = path_manager.get_txt_path("mail.txt")

            if not os.path.exists(mail_file_path):
                print(f"❌ Không tìm thấy file mail.txt tại {mail_file_path}")
                return None

            # Đọc file mail.txt
            with open(mail_file_path, "r", encoding="utf-8") as file:
                lines = file.readlines()

            # Lọc các dòng hợp lệ (bỏ qua dòng trống và comment)
            valid_lines = []
            for line in lines:
                line = line.strip()
                if line and not line.startswith("#"):
                    valid_lines.append(line)

            if not valid_lines:
                print(f"❌ Không có dòng hợp lệ nào trong file mail.txt")
                return None

            # Tính toán chỉ số dòng dựa trên chỉ số thiết bị
            line_index = self.device_index % len(valid_lines)
            selected_line = valid_lines[line_index]

            # Phân tích dòng để lấy mã xác thực (định dạng: email|password|authentication)
            parts = selected_line.split("|")
            if len(parts) >= 3:
                auth_secret = parts[2].strip()
                print(f"✅ Đã tải mã xác thực từ file mail.txt (dòng {line_index + 1}): {auth_secret[:4]}{'*' * (len(auth_secret) - 4) if len(auth_secret) > 4 else '*' * len(auth_secret)}")
                return auth_secret
            else:
                print(f"❌ Không có mã xác thực trong file mail.txt (dòng {line_index + 1}): {selected_line}")
                return None

        except Exception as e:
            print(f"❌ Lỗi khi tải mã xác thực từ file mail.txt: {str(e)}")
            return None

    def generate_otp_from_secret(self, secret):
        """
        Tạo mã OTP từ secret key (giống chplay).
        :param secret: Secret key để tạo OTP
        :return: Mã OTP 6 chữ số nếu thành công, None nếu thất bại
        """
        try:
            import pyotp

            # Loại bỏ khoảng trắng và chuyển thành chữ hoa
            clean_secret = secret.replace(" ", "").upper()

            # Tạo đối tượng TOTP
            totp = pyotp.TOTP(clean_secret)

            # Tạo mã OTP
            otp_code = totp.now()

            print(f"✅ Đã tạo mã OTP: {otp_code}")
            return otp_code

        except ImportError:
            print("❌ Không thể import thư viện pyotp. Vui lòng cài đặt: pip install pyotp")
            return None
        except Exception as e:
            print(f"❌ Lỗi khi tạo mã OTP từ secret: {str(e)}")
            return None

    def handle_ver_that_step(self, check_stop_flag=None):
        """
        Xử lý bước Ver That: tìm nút Ver That, nhập mật khẩu và xử lý 2FA.
        :param check_stop_flag: Hàm callback để kiểm tra cờ dừng
        :return: True nếu thành công, False nếu thất bại
        """
        try:
            # Hàm kiểm tra cờ dừng
            def should_stop():
                if check_stop_flag and callable(check_stop_flag):
                    stop_flag = check_stop_flag()
                    if stop_flag:
                        print("⚠️ Đã nhận tín hiệu dừng từ người dùng trong handle_ver_that_step")
                    return stop_flag
                return False

            print("🔍 Bắt đầu xử lý bước Ver That")

            # Kiểm tra cờ dừng trước khi bắt đầu
            if should_stop():
                return False

            # Tìm và tap vào nút Ver That
            print("Tìm và tap vào nút Ver That")
            ver_that_found = False

            if os.path.exists(self.ver_that_template_path):
                ver_that_found = self.wait_for_image_and_tap(self.ver_that_template_path, threshold=0.7, max_retries=3, check_stop_flag=check_stop_flag)

            if ver_that_found:
                print("✅ Đã tìm thấy và tap vào nút Ver That")
                self._random_sleep(8.0, 10.0, "Chờ sau khi tap vào Ver That trước khi nhập mật khẩu")

                # Nhập mật khẩu ngay sau khi tìm thấy Ver That
                print(f"🔍 Kiểm tra mật khẩu: {self.password if self.password else 'Không có'}")
                if self.password:
                    print(f"🔑 Nhập mật khẩu sau khi tap vào Ver That: {self.password[:2]}{'*' * (len(self.password) - 2)}")

                    # Xóa trường nhập liệu trước khi nhập mật khẩu mới
                    print("🔄 Xóa trường nhập liệu trước khi nhập mật khẩu")
                    for i in range(30):
                        subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "keyevent", "67"], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                        time.sleep(0.05)
                    print(f"✅ Đã xóa 30 ký tự từ trường nhập liệu")

                    # Nhập mật khẩu bằng cách giống chplay
                    if self.input_text_chplay_style(self.password):
                        print("✅ Đã nhập mật khẩu thành công (cách chplay)")

                        # Nhấn phím Enter
                        time.sleep(1)
                        if self.press_key(66):
                            print("✅ Đã nhấn phím Enter sau khi nhập mật khẩu")
                        else:
                            print("❌ Lỗi khi nhấn phím Enter sau khi nhập mật khẩu")
                    else:
                        print("❌ Lỗi khi nhập mật khẩu (cách chplay)")

                    # Đợi xác thực và kiểm tra 2FA
                    self._random_sleep(10.0, 15.0, "Chờ xác thực mật khẩu")

                    # Kiểm tra xem có xuất hiện authen_ads.png không (yêu cầu 2FA)
                    print("🔍 Kiểm tra xem có yêu cầu 2FA không...")

                    if os.path.exists(self.authen_ads_template_path):
                        authen_ads_found = self.find_image_on_screen(self.authen_ads_template_path, threshold=0.7, check_stop_flag=check_stop_flag)

                        if authen_ads_found[0]:  # find_image_on_screen trả về tuple (found, location)
                            print("✅ Đã tìm thấy authen_ads.png - Yêu cầu nhập code 2FA")

                            # Lấy mã xác thực từ file mail.txt (giống chplay)
                            auth_secret = self.load_auth_secret_from_mail_file()

                            if auth_secret:
                                # Tạo mã OTP từ secret (giống chplay)
                                twofa_code = self.generate_otp_from_secret(auth_secret)

                                if twofa_code:
                                    print(f"🔐 Nhập code 2FA: {twofa_code}")
                                else:
                                    print("❌ Không thể tạo mã OTP từ secret, bỏ qua 2FA")
                            else:
                                print("❌ Không thể lấy mã xác thực từ file mail.txt, bỏ qua 2FA")
                                twofa_code = None

                            # Chỉ nhập 2FA nếu có code hợp lệ
                            if twofa_code:
                                # Xóa trường nhập liệu trước khi nhập code 2FA
                                print("🔄 Xóa trường nhập liệu trước khi nhập code 2FA")
                                for i in range(10):  # Xóa 10 ký tự cho code 2FA
                                    subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "keyevent", "67"], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                                    time.sleep(0.05)
                                print(f"✅ Đã xóa 10 ký tự từ trường nhập liệu (2FA)")

                                # Nhập code 2FA bằng cách giống chplay
                                if self.input_text_chplay_style(twofa_code):
                                    print("✅ Đã nhập code 2FA thành công (cách chplay)")

                                    # Nhấn phím Enter để xác nhận 2FA
                                    time.sleep(1)
                                    if self.press_key(66):  # KEYCODE_ENTER = 66
                                        print("✅ Đã nhấn phím Enter sau khi nhập code 2FA")
                                        self._random_sleep(5.0, 7.0, "Chờ xác thực code 2FA")
                                    else:
                                        print("❌ Lỗi khi nhấn phím Enter sau khi nhập code 2FA")
                                else:
                                    print("❌ Lỗi khi nhập code 2FA (cách chplay)")
                            else:
                                print("⚠️ Bỏ qua nhập 2FA vì không có code hợp lệ")
                        else:
                            print("ℹ️ Không tìm thấy authen_ads.png - Không cần 2FA")
                    else:
                        print("⚠️ File authen_ads.png không tồn tại - Bỏ qua kiểm tra 2FA")

                    # Tìm và tap vào nút Continue Ver (nếu xuất hiện)
                    if os.path.exists(self.continue_ver_template_path):
                        continue_ver_found = self.wait_for_image_and_tap(self.continue_ver_template_path, threshold=0.7, max_retries=2, check_stop_flag=check_stop_flag)
                        if continue_ver_found:
                            print("✅ Đã tìm thấy và tap vào nút Continue Ver")
                            self._random_sleep(8.0, 12.0, "Chờ sau khi tap vào Continue Ver")
                else:
                    print("⚠️ Không có mật khẩu được cung cấp")

                print("✅ Hoàn thành xử lý bước Ver That")
                return True
            else:
                print("⚠️ Không tìm thấy nút Ver That sau thời gian chờ")
                return False

        except Exception as e:
            print(f"❌ Lỗi khi xử lý bước Ver That: {e}")
            return False

    def execute_actions(self, check_stop_flag=None):
        """
        Thực hiện chuỗi hành động xóa thẻ với retry logic cho bước 4-8.
        :param check_stop_flag: Hàm callback để kiểm tra cờ dừng từ MainForm.py
        :return: True nếu thành công, False nếu thất bại.
        """
        try:
            print(f"\n=== Bắt đầu thực hiện chuỗi hành động xóa thẻ trên thiết bị {self.serial} ===\n")
            self.update_status(f"🚀 Bắt đầu thực hiện xóa thẻ")

            # Hàm kiểm tra cờ dừng
            def should_stop():
                if check_stop_flag and callable(check_stop_flag):
                    stop_flag = check_stop_flag()
                    if stop_flag:
                        print("⚠️ Đã nhận tín hiệu dừng từ người dùng")
                    return stop_flag
                return False

            # Kiểm tra các file template cần thiết
            self.update_status("🔍 Đang kiểm tra các file template cần thiết...")
            missing_templates = []
            for template_name in ["chrome.png", "continue.png", "got_it.png",
                                 "ver_that.png", "menu.png"]:
                template_path = os.path.join(self.template_dir, template_name)
                if not os.path.exists(template_path):
                    missing_templates.append(template_name)

            # Kiểm tra xem có ít nhất một trong hai file yes_im_in.png hoặc yes_im_in_trang.png
            yes_im_in_path = os.path.join(self.template_dir, "yes_im_in.png")
            yes_im_in_trang_path = os.path.join(self.template_dir, "yes_im_in_trang.png")
            if not os.path.exists(yes_im_in_path) and not os.path.exists(yes_im_in_trang_path):
                missing_templates.append("yes_im_in.png hoặc yes_im_in_trang.png")

            # Kiểm tra xem có ít nhất một trong hai file search.png hoặc search2.png
            search_path = os.path.join(self.template_dir, "search.png")
            search2_path = os.path.join(self.template_dir, "search2.png")
            if not os.path.exists(search_path) and not os.path.exists(search2_path):
                missing_templates.append("search.png hoặc search2.png")

            if missing_templates:
                error_msg = f"⚠️ Không tìm thấy các file template sau: {', '.join(missing_templates)}"
                print(error_msg)
                print(f"⚠️ Vui lòng đặt các file template vào thư mục {self.template_dir}")
                self.update_status(error_msg)
                return False
            else:
                self.update_status("✅ Đang thực hiện xóa thẻ close, vui lòng đợi...")

            # Kiểm tra cờ dừng trước khi bắt đầu
            if should_stop():
                print("⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi bắt đầu thực hiện các bước")
                return False

            # Bước 1-3: Khởi tạo và mở Chrome
            print("🔄 Thực hiện bước 1-3: Khởi tạo và mở Chrome")

            # Bước 1: Về màn hình chính
            print("Bước 1: Về màn hình chính bằng cách nhấn nút Home")
            subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "keyevent", "3"], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
            print("✅ Đã về màn hình chính")

            if self._random_sleep(2.0, 3.0, "Chờ sau khi về màn hình chính", check_stop_flag):
                return False

            # Bước 2: Vuốt lên để hiển thị menu ứng dụng
            print("Bước 2: Vuốt lên 1 lần để hiển thị menu ứng dụng")
            screen_height = 1920
            screen_width = 1080
            self.swipe(screen_width // 2, screen_height * 4 // 5, screen_width // 2, screen_height // 5, 250)

            if self._random_sleep(1.0, 2.0, "Chờ sau khi vuốt lên 1 lần", check_stop_flag):
                return False

            # Bước 3: Tìm và tap vào Chrome
            print("Bước 3: Tìm và tap vào biểu tượng Chrome")
            chrome_found = False
            if os.path.exists(self.chrome_template_path):
                chrome_found = self.find_image_and_tap(self.chrome_template_path, max_retries=2, check_stop_flag=check_stop_flag)

            if not chrome_found:
                print("❌ Không thể mở Chrome")
                return False

            if self._random_sleep(5.0, 7.0, "Chờ Chrome khởi động hoàn toàn", check_stop_flag):
                return False

            # Bước 4-8: Thực hiện với retry logic
            print("🔄 Thực hiện bước 4-8 với retry logic")
            max_retries = 3  # Số lần thử lại tối đa

            for attempt in range(max_retries):
                if should_stop():
                    return False

                print(f"🔄 Lần thử {attempt + 1}/{max_retries} cho bước 4-8")

                # Thực hiện bước 4-8
                if self.execute_steps_4_to_8(check_stop_flag):
                    print("✅ Bước 4-8 thành công, tiếp tục với các bước tiếp theo")
                    break
                else:
                    print(f"❌ Bước 4-8 thất bại (lần thử {attempt + 1}/{max_retries})")

                    if attempt < max_retries - 1:  # Không phải lần thử cuối
                        print("🔄 Thực hiện retry: Mở lại Chrome và thử lại")

                        # Về màn hình chính
                        subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "keyevent", "3"], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                        time.sleep(2)

                        # Vuốt lên
                        self.swipe(screen_width // 2, screen_height * 4 // 5, screen_width // 2, screen_height // 5, 250)
                        time.sleep(2)

                        # Mở Chrome lại
                        if os.path.exists(self.chrome_template_path):
                            chrome_found = self.find_image_and_tap(self.chrome_template_path, max_retries=2, check_stop_flag=check_stop_flag)
                            if chrome_found:
                                print("✅ Đã mở lại Chrome thành công")
                                if self._random_sleep(5.0, 7.0, "Chờ Chrome khởi động lại", check_stop_flag):
                                    return False
                            else:
                                print("❌ Không thể mở lại Chrome")
                                return False
                        else:
                            print("❌ File chrome.png không tồn tại")
                            return False
                    else:
                        print("❌ Đã thử tối đa số lần cho bước 4-8, dừng thực hiện")
                        return False

            # Bước 9-10: Nhập URL
            print("🔄 Thực hiện bước 9-10: Nhập URL")

            # Nhập URL payments.google.com
            print("Bước 9-10: Nhập URL payments.google.com")
            try:
                self.send_text("payments.google.com")
                print("✅ Đã nhập URL payments.google.com")

                if self._random_sleep(2.0, 3.0, "Chờ sau khi nhập URL", check_stop_flag):
                    return False

                # Nhấn Enter để tìm kiếm
                self.press_key(66)  # KEYCODE_ENTER = 66
                print("✅ Đã nhấn Enter để tìm kiếm")
            except Exception as e:
                print(f"❌ Lỗi khi nhập URL: {str(e)}")
                return False

            # Đợi trang web tải
            if self._random_sleep(14.0, 15.0, "Chờ trang web tải", check_stop_flag):
                return False

            # Bước 11: Tìm và tap vào nút Ver That với nhập mật khẩu
            print("🔄 Thực hiện bước 11: Ver That và nhập mật khẩu")

            ver_that_found = False
            if os.path.exists(self.ver_that_template_path):
                print(f"✅ File ver_that.png tồn tại: {self.ver_that_template_path}")
                ver_that_found = self.wait_for_image_and_tap(
                    self.ver_that_template_path,
                    threshold=0.7,
                    timeout=15.0,
                    check_interval=1.0,
                    max_retries=3,
                    check_stop_flag=check_stop_flag
                )
            else:
                print(f"❌ File ver_that.png không tồn tại: {self.ver_that_template_path}")

            if ver_that_found:
                print("✅ Đã tìm thấy và tap vào nút Ver That")
                self._random_sleep(8.0, 10.0, "Chờ sau khi tap vào Ver That trước khi nhập mật khẩu")

                # Nhập mật khẩu ngay sau khi tìm thấy Ver That
                print(f"🔍 Kiểm tra mật khẩu: {self.password if self.password else 'Không có'}")
                if self.password:
                    print(f"🔑 Nhập mật khẩu sau khi tap vào Ver That: {self.password[:2]}{'*' * (len(self.password) - 2)}")

                    # Xóa trường nhập liệu trước khi nhập mật khẩu mới
                    print("🔄 Xóa trường nhập liệu trước khi nhập mật khẩu")
                    for i in range(30):
                        subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "keyevent", "67"], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                        time.sleep(0.05)
                    print(f"✅ Đã xóa 30 ký tự từ trường nhập liệu")

                    # Nhập mật khẩu bằng cách giống chplay
                    if self.input_text_chplay_style(self.password):
                        print("✅ Đã nhập mật khẩu thành công (cách chplay)")

                        # Nhấn phím Enter
                        time.sleep(1)
                        if self.press_key(66):
                            print("✅ Đã nhấn phím Enter sau khi nhập mật khẩu")
                        else:
                            print("❌ Lỗi khi nhấn phím Enter sau khi nhập mật khẩu")
                    else:
                        print("❌ Lỗi khi nhập mật khẩu (cách chplay)")

                    # Đợi xác thực và kiểm tra 2FA
                    self._random_sleep(10.0, 15.0, "Chờ xác thực mật khẩu")

                    # Kiểm tra xem có xuất hiện authen_ads.png không (yêu cầu 2FA)
                    print("🔍 Kiểm tra xem có yêu cầu 2FA không...")

                    if os.path.exists(self.authen_ads_template_path):
                        authen_ads_found = self.find_image_on_screen(self.authen_ads_template_path, threshold=0.7, check_stop_flag=check_stop_flag)

                        if authen_ads_found[0]:  # find_image_on_screen trả về tuple (found, location)
                            print("✅ Đã tìm thấy authen_ads.png - Yêu cầu nhập code 2FA")

                            # Lấy mã xác thực từ file mail.txt (giống chplay)
                            auth_secret = self.load_auth_secret_from_mail_file()

                            if auth_secret:
                                # Tạo mã OTP từ secret (giống chplay)
                                twofa_code = self.generate_otp_from_secret(auth_secret)

                                if twofa_code:
                                    print(f"🔐 Nhập code 2FA: {twofa_code}")
                                else:
                                    print("❌ Không thể tạo mã OTP từ secret, bỏ qua 2FA")
                            else:
                                print("❌ Không thể lấy mã xác thực từ file mail.txt, bỏ qua 2FA")
                                twofa_code = None

                            # Chỉ nhập 2FA nếu có code hợp lệ
                            if twofa_code:
                                # Xóa trường nhập liệu trước khi nhập code 2FA
                                print("🔄 Xóa trường nhập liệu trước khi nhập code 2FA")
                                for i in range(10):  # Xóa 10 ký tự cho code 2FA
                                    subprocess.run([ADB_PATH, "-s", self.serial, "shell", "input", "keyevent", "67"], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                                    time.sleep(0.05)
                                print(f"✅ Đã xóa 10 ký tự từ trường nhập liệu (2FA)")

                                # Nhập code 2FA bằng cách giống chplay
                                if self.input_text_chplay_style(twofa_code):
                                    print("✅ Đã nhập code 2FA thành công (cách chplay)")

                                    # Nhấn phím Enter để xác nhận 2FA
                                    time.sleep(1)
                                    if self.press_key(66):  # KEYCODE_ENTER = 66
                                        print("✅ Đã nhấn phím Enter sau khi nhập code 2FA")
                                        self._random_sleep(5.0, 7.0, "Chờ xác thực code 2FA")
                                    else:
                                        print("❌ Lỗi khi nhấn phím Enter sau khi nhập code 2FA")
                                else:
                                    print("❌ Lỗi khi nhập code 2FA (cách chplay)")
                            else:
                                print("⚠️ Bỏ qua nhập 2FA vì không có code hợp lệ")
                        else:
                            print("ℹ️ Không tìm thấy authen_ads.png - Không cần 2FA")
                    else:
                        print("⚠️ File authen_ads.png không tồn tại - Bỏ qua kiểm tra 2FA")

                    # Tìm và tap vào nút Continue Ver (nếu xuất hiện)
                    if os.path.exists(self.continue_ver_template_path):
                        continue_ver_found = self.wait_for_image_and_tap(self.continue_ver_template_path, threshold=0.7, max_retries=2, check_stop_flag=check_stop_flag)
                        if continue_ver_found:
                            print("✅ Đã tìm thấy và tap vào nút Continue Ver")
                            self._random_sleep(8.0, 12.0, "Chờ sau khi tap vào Continue Ver")
                else:
                    print("⚠️ Không có mật khẩu được cung cấp")
            else:
                print("⚠️ Không tìm thấy nút Ver That sau thời gian chờ")

            # Bước 11.5: Kiểm tra signed_in.png có xuất hiện không
            print("Bước 11.5: Kiểm tra signed_in.png có xuất hiện không")
            if os.path.exists(self.signed_in_template_path):
                signed_in_found = self.find_image_on_screen(self.signed_in_template_path, threshold=0.7, check_stop_flag=check_stop_flag)
                if signed_in_found[0]:  # find_image_on_screen trả về tuple (found, location)
                    print("✅ Đã tìm thấy signed_in.png - Tap vào vị trí 444, 440")
                    if self.tap_at_position(444, 440):
                        print("✅ Đã tap vào vị trí 444, 440 thành công")
                        self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào signed_in", check_stop_flag)
                    else:
                        print("❌ Không thể tap vào vị trí 444, 440")
                else:
                    print("ℹ️ Không tìm thấy signed_in.png - Tiếp tục bình thường")
            else:
                print("⚠️ File signed_in.png không tồn tại - Bỏ qua kiểm tra")

            # Bước 12: Tìm menu.png với retry logic (3 lần thử quay lại bước 9)
            print("Bước 12: Tìm menu.png với retry logic")
            menu_found = False
            menu_retry_count = 0
            max_menu_retries = 3  # 3 lần thử quay lại bước 9

            while not menu_found and menu_retry_count < max_menu_retries:
                if should_stop():
                    return False

                print(f"🔍 Tìm menu.png (lần thử {menu_retry_count + 1}/{max_menu_retries})")

                if os.path.exists(self.menu_template_path):
                    menu_found = self.wait_for_image_and_tap(
                        self.menu_template_path,
                        threshold=0.7,
                        timeout=7.0,
                        check_interval=1.0,
                        max_retries=3,
                        check_stop_flag=check_stop_flag
                    )
                else:
                    print(f"❌ File menu.png không tồn tại: {self.menu_template_path}")
                    break

                if menu_found:
                    print("✅ Đã tìm thấy và tap vào menu.png")
                    self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào Menu")
                    break
                else:
                    menu_retry_count += 1
                    print(f"❌ Không tìm thấy menu.png (lần thử {menu_retry_count}/{max_menu_retries})")

                    # Retry logic mới: thực hiện bước 11.5 -> back -> thực hiện lại từ bước 9
                    if menu_retry_count < max_menu_retries:
                        print("🔄 Thực hiện quy trình khôi phục: bước 11.5 -> back -> thực hiện lại từ bước 9")

                        # Thực hiện bước 11.5: Kiểm tra signed_in.png có xuất hiện không
                        print("Bước 11.5 (retry): Kiểm tra signed_in.png có xuất hiện không")
                        if os.path.exists(self.signed_in_template_path):
                            signed_in_found = self.find_image_on_screen(self.signed_in_template_path, threshold=0.7, check_stop_flag=check_stop_flag)
                            if signed_in_found[0]:  # find_image_on_screen trả về tuple (found, location)
                                print("✅ Đã tìm thấy signed_in.png - Tap vào vị trí 444, 440 (retry)")
                                if self.tap_at_position(444, 440):
                                    print("✅ Đã tap vào vị trí 444, 440 thành công (retry)")
                                    self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào signed_in (retry)", check_stop_flag)
                                else:
                                    print("❌ Không thể tap vào vị trí 444, 440 (retry)")
                            else:
                                print("ℹ️ Không tìm thấy signed_in.png - Tiếp tục bình thường (retry)")
                        else:
                            print("⚠️ File signed_in.png không tồn tại - Bỏ qua kiểm tra (retry)")

                        # Back lại 1 lần
                        print("🔄 Back lại 1 lần")
                        self.press_key(4)  # KEYCODE_BACK = 4
                        time.sleep(2)

                        # Thực hiện lại từ bước 9: Tìm và tap vào ô tìm kiếm
                        print("🔄 Thực hiện lại từ bước 9: Tìm và tap vào ô tìm kiếm")
                        search_found = False

                        if os.path.exists(self.search_template_path):
                            search_found = self.wait_for_image_and_tap(self.search_template_path, threshold=0.7, max_retries=2, check_stop_flag=check_stop_flag)

                        if not search_found:
                            print("⚠️ Không tìm thấy ô tìm kiếm, thử tìm ô tìm kiếm phụ (search2)")
                            if os.path.exists(self.search2_template_path):
                                search_found = self.find_image_and_tap(self.search2_template_path, check_stop_flag=check_stop_flag)

                        if search_found:
                            print("✅ Đã tìm thấy ô tìm kiếm (retry)")
                            self._random_sleep(2.0, 3.0, "Chờ sau khi tap vào ô tìm kiếm (retry)", check_stop_flag)

                            # Bước 10: Nhập URL payments.google.com
                            print("🔄 Bước 10 (retry): Nhập URL payments.google.com")
                            try:
                                self.send_text("payments.google.com")
                                print("✅ Đã nhập URL payments.google.com (retry)")
                                self._random_sleep(2.0, 3.0, "Chờ sau khi nhập URL (retry)", check_stop_flag)

                                # Nhấn Enter để tìm kiếm
                                self.press_key(66)  # KEYCODE_ENTER = 66
                                print("✅ Đã nhấn Enter để tìm kiếm (retry)")
                            except Exception as e:
                                print(f"❌ Lỗi khi nhập URL (retry): {str(e)}")

                            # Đợi trang web tải
                            self._random_sleep(10.0, 14.0, "Chờ trang web tải (retry)", check_stop_flag)

                            # Bước 11: Xử lý Ver That (retry)
                            print("🔄 Bước 11 (retry): Gọi hàm xử lý Ver That")
                            ver_that_success = self.handle_ver_that_step(check_stop_flag)
                            if not ver_that_success:
                                print("⚠️ Xử lý Ver That không thành công (retry)")
                        else:
                            print("❌ Không tìm thấy ô tìm kiếm trong retry, bỏ qua retry này")

            if not menu_found:
                print("❌ Không thể tìm thấy menu.png sau 3 lần thử quay lại bước 9")
                self.update_status("❌ Xóa thẻ thất bại - Không tìm thấy menu")
                return False

            # Bước 13: Tiếp tục với các bước xóa thẻ
            print("🔄 Thực hiện các bước xóa thẻ")

            # Tìm và tap vào payment_method.png
            if os.path.exists(self.payment_method_template_path):
                payment_method_found = self.wait_for_image_and_tap(self.payment_method_template_path, max_retries=3, check_stop_flag=check_stop_flag)
                if payment_method_found:
                    print("✅ Đã tìm thấy payment_method.png")
                    self._random_sleep(4.0, 6.0, "Chờ sau khi tap vào payment_method.png")                    
                else:
                    print("❌ Không tìm thấy payment_method.png")
                    return False

            # Giới hạn số lần lặp lại việc tìm và xóa thẻ
            payment_center_found = False
            if os.path.exists(self.payment_center_template_path):
                found, _ = self.find_image_on_screen(self.payment_center_template_path, threshold=0.7, check_stop_flag=should_stop)
                payment_center_found = found

            if payment_center_found:
                print("✅ Tìm thấy trang Payment Center thực hiện vòng lặp xóa thẻ")
                self.update_status("Đã tìm thấy trang Payment Center")
             
            # Giới hạn số lần lặp lại việc tìm và xóa thẻ
            max_iterations = 10
            iteration = 0
            found_any_card = True  # Để kích hoạt vòng lặp lần đầu

            while found_any_card and iteration < max_iterations:
                iteration += 1
                print(f"\nLặp lại việc tìm và xóa thẻ (lần {iteration}/{max_iterations})")

                # Vuốt lên 3 lần để cuộn nội dung
                print("Bước 1: Vuốt lên 3 lần")
                for i in range(3):
                    self.swipe(screen_width // 2, screen_height * 4 // 5, screen_width // 2, screen_height * 1 // 5, 80)
                    print(f"✅ Đã vuốt lên lần thứ {i + 1}")
                    # Tăng thời gian chờ sau khi vuốt lên
                    self._random_sleep(0.5, 1.5, f"Chờ sau khi vuốt lên lần thứ {i + 1}")

                # Vuốt xuống 1 lần nhẹ nhàng
                print("Bước 2: Vuốt xuống 1 lần nhẹ nhàng để điều chỉnh vị trí")
                for i in range(1):
                    self.swipe(screen_width // 2, screen_height * 3 // 10, screen_width // 2, screen_height * 7 // 10, 900)
                    print(f"✅ Đã vuốt xuống nhẹ nhàng lần thứ {i + 1}")
                    self.update_status("Đang tìm xoá thẻ...")
                    # Tăng thời gian chờ sau khi vuốt xuống
                    self._random_sleep(3.0, 4.0, f"Chờ sau khi vuốt xuống nhẹ nhàng lần thứ {i + 1}")

                # Danh sách các template cần tìm
                card_templates = [
                    (self.card_close_template_path, "card_close.png"),
                    (self.card_ver_template_path, "card_ver.png"),
                    (self.card_ver2_template_path, "card_ver2.png"),
                    (self.card_fix_template_path, "card_fix.png"),
                ]

                found_any_card = False  # Đặt lại trạng thái

                print("Bắt đầu tìm và tap các thẻ")
                for template_path, template_name in card_templates:
                    if os.path.exists(template_path):
                        print(f"Tìm {template_name}")
                        # Tăng số lần thử lại lên 3 và giảm ngưỡng xuống 0.75 để dễ tìm thấy hơn
                        card_found = self.find_image_and_tap(template_path, threshold=0.75, max_retries=3, check_stop_flag=should_stop)

                        if card_found:
                            print(f"✅ Đã tìm thấy và tap vào {template_name}")
                            found_any_card = True
                            # Tăng thời gian chờ sau khi tap vào thẻ
                            self._random_sleep(1.0, 2.0, f"Chờ sau khi tap vào {template_name}")

                            # Tìm và tap nút Remove
                            if os.path.exists(self.remove_template_path):
                                print("Tìm và tap vào nút Remove")
                                self.update_status("Đang xoá.....")
                                # Tăng số lần thử lại lên 3 và giảm ngưỡng xuống 0.75
                                remove_found = self.find_image_and_tap(self.remove_template_path, threshold=0.75, max_retries=3, check_stop_flag=should_stop)

                                if remove_found:
                                    print("✅ Đã tìm thấy và tap vào nút Remove")
                                    self.update_status(f"✅ Đang xóa thẻ thứ {iteration}...")
                                    # Tăng thời gian chờ sau khi tap vào Remove
                                    self._random_sleep(1.0, 3.0, "Chờ sau khi tap vào Remove")

                                    # Tìm và tap nút Remove2 (nếu có)
                                    if os.path.exists(self.remove2_template_path):
                                        print("Tìm và tap vào nút Remove2")
                                        # Tăng số lần thử lại lên 3 và giảm ngưỡng xuống 0.75
                                        remove2_found = self.find_image_and_tap(self.remove2_template_path, threshold=0.75, max_retries=3, check_stop_flag=should_stop)

                                        if remove2_found:
                                            print("✅ Đã tìm thấy và tap vào nút Remove2")
                                            self.update_status(f"✅ Đã xác nhận xóa thẻ thứ {iteration}...")
                                            # Tăng thời gian chờ sau khi tap vào Remove2
                                            self._random_sleep(1.0, 3.0, "Chờ sau khi tap vào Remove2")
                                        else:
                                            print("⚠️ Không tìm thấy nút Remove2, tiếp tục")
                                else:
                                    print("⚠️ Không tìm thấy nút Remove, tiếp tục")
                            break  # Sau khi xử lý 1 thẻ, thoát vòng card_templates
                        else:
                            print(f"⚠️ Không tìm thấy {template_name}")

                # Kiểm tra cờ dừng lần nữa
                if should_stop():
                    print("⚠️ Đã nhận tín hiệu dừng từ người dùng")
                    return False

                if not found_any_card:
                    print("✅ Không tìm thấy thẻ nào để xóa - Kết thúc vòng lặp")
                    self.update_status("✅ Đã xóa hết các thẻ hoặc không còn thẻ nào để xử lý")
                    break

            if iteration >= max_iterations:
                print(f"⚠️ Đã đạt đến giới hạn số lần lặp lại ({max_iterations}), dừng lại")
                self.update_status(f"⚠️ Đã đạt đến giới hạn số lần lặp lại ({max_iterations}), dừng lại")

            # Về Home sau khi xoá thẻ
            print("Bước 19: Về màn hình chính để hoàn thành")
            self.press_key(3)  # KEYCODE_HOME = 3
            print("✅ Đã về màn hình chính")

            self.update_status(f"✅ Đã hoàn thành xoá thẻ trên thiết bị {self.serial}")
            return True


        except Exception as e:
            print(f"❌ Lỗi khi thực hiện chuỗi hành động: {str(e)}")
            self.update_status(f"❌ Lỗi: {str(e)}")
            return False

    def execute_with_loop(self, chrome_template_path=None, continue_template_path=None,
                         yes_im_in_template_path=None, got_it_template_path=None,
                         search_template_path=None, search2_template_path=None, ver_that_template_path=None,
                         menu_template_path=None, setting_template_path=None, close_payment_template_path=None,
                         continue_ver_template_path=None, why_are_you_template_path=None,
                         i_dont_want_template_path=None, i_have_had_template_path=None,
                         payment_method_template_path=None, card_ver_template_path=None, card_ver2_template_path=None,
                         card_close_template_path=None, card_fix_template_path=None, remove_template_path=None,
                         remove2_template_path=None, ver_it_template_path=None, continue_del_template_path=None,
                         tunit_on_template_path=None, git_it_trang_template_path=None,
                         payment_center_template_path=None,
                         check_stop_flag=None):
        """
        Thực hiện chuỗi hành động xóa thẻ 1 lần duy nhất (đã bỏ retry toàn cục).
        :param check_stop_flag: Hàm callback để kiểm tra cờ dừng từ AutoK.py.
        :return: True nếu thành công, False nếu thất bại.
        """
        # Cập nhật các đường dẫn template nếu được cung cấp
        if chrome_template_path:
            self.chrome_template_path = chrome_template_path
        if continue_template_path:
            self.continue_template_path = continue_template_path
        if yes_im_in_template_path:
            self.yes_im_in_template_path = yes_im_in_template_path
        if got_it_template_path:
            self.got_it_template_path = got_it_template_path
        if search_template_path:
            self.search_template_path = search_template_path
        if search2_template_path:
            self.search2_template_path = search2_template_path
        if ver_that_template_path:
            self.ver_that_template_path = ver_that_template_path
        if menu_template_path:
            self.menu_template_path = menu_template_path
        if setting_template_path:
            self.setting_template_path = setting_template_path
        if close_payment_template_path:
            self.close_payment_template_path = close_payment_template_path
        if continue_ver_template_path:
            self.continue_ver_template_path = continue_ver_template_path
        if why_are_you_template_path:
            self.why_are_you_template_path = why_are_you_template_path
        if i_dont_want_template_path:
            self.i_dont_want_template_path = i_dont_want_template_path
        if i_have_had_template_path:
            self.i_have_had_template_path = i_have_had_template_path
        if payment_method_template_path:
            self.payment_method_template_path = payment_method_template_path
        if card_ver_template_path:
            self.card_ver_template_path = card_ver_template_path
        if card_ver2_template_path:
            self.card_ver2_template_path = card_ver2_template_path
        if card_close_template_path:
            self.card_close_template_path = card_close_template_path
        if card_fix_template_path:
            self.card_fix_template_path = card_fix_template_path
        if remove_template_path:
            self.remove_template_path = remove_template_path
        if remove2_template_path:
            self.remove2_template_path = remove2_template_path
        if ver_it_template_path:
            self.ver_it_template_path = ver_it_template_path
        if continue_del_template_path:
            self.continue_del_template_path = continue_del_template_path
        if tunit_on_template_path:
            self.tunit_on_template_path = tunit_on_template_path
        if git_it_trang_template_path:
            self.git_it_trang_template_path = git_it_trang_template_path
        if payment_center_template_path:
            self.payment_center_template_path = payment_center_template_path

        # Thực hiện chuỗi hành động 1 lần duy nhất (bỏ retry toàn cục)
        self.update_status("=== Bắt đầu xóa thẻ ===")

        # Kiểm tra cờ dừng trước khi bắt đầu
        if check_stop_flag and callable(check_stop_flag) and check_stop_flag():
            self.update_status("⚠️ Đã nhận tín hiệu dừng từ người dùng")
            return False

        try:
            result = self.execute_actions(check_stop_flag=check_stop_flag)
            if result:
                self.update_status("✅ Xóa thẻ thành công")
                return True
            else:
                self.update_status("❌ Xóa thẻ thất bại")
                return False
        except Exception as e:
            error_message = f"❌ Lỗi khi xóa thẻ: {str(e)}"
            self.update_status(error_message)
            return False


def delete_card_close(serial, device_index=0, update_status_callback=None, adb_path=None, password=None, auth_secret=None):
    """
    Hàm tiện ích để tạo đối tượng DeleteCardClose và thực hiện chuỗi hành động xóa thẻ.
    :param serial: Số serial của thiết bị.
    :param device_index: Chỉ số thiết bị trong danh sách thiết bị được chọn.
    :param update_status_callback: Hàm callback để cập nhật trạng thái trong giao diện người dùng.
    :param adb_path: Đường dẫn đến file adb.exe
    :param password: Mật khẩu từ giao diện (ưu tiên cao nhất)
    :param auth_secret: Mã xác thực từ giao diện (ưu tiên cao nhất)
    :return: Đối tượng DeleteCardClose đã được khởi tạo.
    """
    return DeleteCardClose(serial, device_index, update_status_callback, adb_path, password, auth_secret)