#!/usr/bin/env python3
"""
Script build bảo mật cho AutoK
Sử dụng PyArmor + <PERSON>uitka để tạo executable bảo mật cao
"""

import os
import sys
import shutil
import subprocess
import tempfile
from pathlib import Path

class SecureBuild:
    def __init__(self):
        self.project_dir = Path.cwd()
        self.temp_dir = None
        self.output_dir = self.project_dir / "dist_secure"
        
    def setup_temp_directory(self):
        """Tạo thư mục tạm để build"""
        self.temp_dir = Path(tempfile.mkdtemp(prefix="autok_build_"))
        print(f"📁 Tạo thư mục tạm: {self.temp_dir}")
        
    def copy_source_files(self):
        """Copy source files cần thiết"""
        files_to_copy = [
            "AutoK.py",
            "security_core.py", 
            "Form.py",
            "addads.py",
            "addwallet.py",
            "chplay.py",
            "closepayment.py",
            "deletecardclose.py",
            "mua.py",
            "path_manager.py",
            "password_manager.py"
        ]
        
        dirs_to_copy = [
            "template",
            "data", 
            "icon",
            "screen"
        ]
        
        # Copy files
        for file_name in files_to_copy:
            src = self.project_dir / file_name
            if src.exists():
                dst = self.temp_dir / file_name
                shutil.copy2(src, dst)
                print(f"✅ Copied: {file_name}")
        
        # Copy directories
        for dir_name in dirs_to_copy:
            src = self.project_dir / dir_name
            if src.exists():
                dst = self.temp_dir / dir_name
                shutil.copytree(src, dst, dirs_exist_ok=True)
                print(f"✅ Copied directory: {dir_name}")
    
    def obfuscate_with_pyarmor(self):
        """Obfuscate code với PyArmor"""
        print("🔒 Bắt đầu obfuscation với PyArmor...")
        
        os.chdir(self.temp_dir)
        
        # Tạo license với thời hạn
        license_cmd = [
            "pyarmor", "licenses", 
            "--expired", "2025-12-31",
            "--bind-disk",
            "autok_license"
        ]
        
        try:
            subprocess.run(license_cmd, check=True, capture_output=True)
            print("✅ Tạo license thành công")
        except subprocess.CalledProcessError as e:
            print(f"⚠️ Không thể tạo license: {e}")
        
        # Obfuscate với cấu hình bảo mật cao
        obfuscate_cmd = [
            "pyarmor", "obfuscate",
            "--advanced", "2",
            "--restrict", "4", 
            "--enable-jit",
            "--mix-str",
            "--recursive"
        ]
        
        # Thêm license nếu có
        license_file = self.temp_dir / "licenses" / "autok_license" / "license.lic"
        if license_file.exists():
            obfuscate_cmd.extend(["--with-license", str(license_file)])
        
        obfuscate_cmd.append("AutoK.py")
        
        try:
            result = subprocess.run(obfuscate_cmd, check=True, capture_output=True, text=True)
            print("✅ Obfuscation thành công")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Obfuscation thất bại: {e}")
            print(f"Output: {e.stdout}")
            print(f"Error: {e.stderr}")
            return False
    
    def compile_with_nuitka(self):
        """Compile với Nuitka"""
        print("⚙️ Bắt đầu compile với Nuitka...")
        
        # Chuyển đến thư mục dist (nơi có file đã obfuscate)
        dist_dir = self.temp_dir / "dist"
        if not dist_dir.exists():
            print("❌ Không tìm thấy thư mục dist từ PyArmor")
            return False
            
        os.chdir(dist_dir)
        
        nuitka_cmd = [
            "nuitka",
            "--standalone",
            "--onefile", 
            "--remove-output",
            "--optimize-for-size",
            "--no-pyi-file",
            "--disable-console",
            "--windows-disable-console",
            "--enable-plugin=pyqt5",
            "--include-data-dir=template=template",
            "--include-data-dir=data=data", 
            "--include-data-dir=icon=icon",
            "--include-data-dir=screen=screen",
            "--output-filename=AutoK_Secure.exe",
            "AutoK.py"
        ]
        
        try:
            result = subprocess.run(nuitka_cmd, check=True, capture_output=True, text=True)
            print("✅ Nuitka compile thành công")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Nuitka compile thất bại: {e}")
            print(f"Output: {e.stdout}")
            print(f"Error: {e.stderr}")
            return False
    
    def compress_with_upx(self):
        """Nén với UPX để tăng bảo mật"""
        print("📦 Nén với UPX...")
        
        exe_file = self.temp_dir / "dist" / "AutoK_Secure.exe"
        if not exe_file.exists():
            print("❌ Không tìm thấy file exe để nén")
            return False
        
        upx_cmd = ["upx", "--best", "--ultra-brute", str(exe_file)]
        
        try:
            subprocess.run(upx_cmd, check=True, capture_output=True)
            print("✅ UPX nén thành công")
            return True
        except subprocess.CalledProcessError as e:
            print(f"⚠️ UPX nén thất bại (không bắt buộc): {e}")
            return False
        except FileNotFoundError:
            print("⚠️ UPX không được cài đặt (không bắt buộc)")
            return False
    
    def copy_final_output(self):
        """Copy file cuối cùng ra thư mục output"""
        print("📋 Copy file cuối cùng...")
        
        # Tạo thư mục output
        self.output_dir.mkdir(exist_ok=True)
        
        # Tìm file exe
        exe_file = self.temp_dir / "dist" / "AutoK_Secure.exe"
        if not exe_file.exists():
            # Thử tìm file khác
            dist_dir = self.temp_dir / "dist"
            exe_files = list(dist_dir.glob("*.exe"))
            if exe_files:
                exe_file = exe_files[0]
            else:
                print("❌ Không tìm thấy file exe")
                return False
        
        # Copy file
        final_exe = self.output_dir / "AutoK_Secure.exe"
        shutil.copy2(exe_file, final_exe)
        
        print(f"✅ File cuối cùng: {final_exe}")
        print(f"📊 Kích thước: {final_exe.stat().st_size / 1024 / 1024:.2f} MB")
        
        return True
    
    def cleanup(self):
        """Dọn dẹp thư mục tạm"""
        if self.temp_dir and self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
            print(f"🗑️ Đã xóa thư mục tạm: {self.temp_dir}")
    
    def build(self):
        """Thực hiện toàn bộ quá trình build"""
        try:
            print("🚀 Bắt đầu build bảo mật AutoK...")
            
            # Kiểm tra dependencies
            self.check_dependencies()
            
            # Setup
            self.setup_temp_directory()
            self.copy_source_files()
            
            # Obfuscate
            if not self.obfuscate_with_pyarmor():
                print("❌ Build thất bại tại bước obfuscation")
                return False
            
            # Compile
            if not self.compile_with_nuitka():
                print("❌ Build thất bại tại bước compile")
                return False
            
            # Optional: Compress
            self.compress_with_upx()
            
            # Copy output
            if not self.copy_final_output():
                print("❌ Build thất bại tại bước copy output")
                return False
            
            print("🎉 Build bảo mật hoàn thành!")
            return True
            
        except Exception as e:
            print(f"❌ Lỗi trong quá trình build: {e}")
            return False
        finally:
            self.cleanup()
    
    def check_dependencies(self):
        """Kiểm tra các dependency cần thiết"""
        required_tools = ["pyarmor", "nuitka"]
        
        for tool in required_tools:
            try:
                subprocess.run([tool, "--version"], 
                             check=True, capture_output=True)
                print(f"✅ {tool} đã cài đặt")
            except (subprocess.CalledProcessError, FileNotFoundError):
                print(f"❌ {tool} chưa được cài đặt")
                print(f"   Cài đặt: pip install {tool}")
                sys.exit(1)

if __name__ == "__main__":
    builder = SecureBuild()
    success = builder.build()
    sys.exit(0 if success else 1)
