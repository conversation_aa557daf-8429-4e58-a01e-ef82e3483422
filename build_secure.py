#!/usr/bin/env python3
"""
Script build bảo mật cho AutoK
Sử dụng PyArmor + <PERSON>uitka để tạo executable bảo mật cao
"""

import os
import sys
import shutil
import subprocess
import tempfile
from pathlib import Path

class SecureBuild:
    def __init__(self):
        self.project_dir = Path.cwd()
        self.temp_dir = None
        self.output_dir = self.project_dir / "dist_secure"
        
    def setup_temp_directory(self):
        """Tạo thư mục tạm để build"""
        self.temp_dir = Path(tempfile.mkdtemp(prefix="autok_build_"))
        print(f"📁 Tạo thư mục tạm: {self.temp_dir}")
        
    def copy_source_files(self):
        """Copy source files cần thiết"""
        files_to_copy = [
            "AutoK.py",
            "security_core.py", 
            "Form.py",
            "addads.py",
            "addwallet.py",
            "chplay.py",
            "closepayment.py",
            "deletecardclose.py",
            "mua.py",
            "path_manager.py",
            "password_manager.py"
        ]
        
        dirs_to_copy = [
            "template",
            "data", 
            "icon",
            "screen"
        ]
        
        # Copy files
        for file_name in files_to_copy:
            src = self.project_dir / file_name
            if src.exists():
                dst = self.temp_dir / file_name
                shutil.copy2(src, dst)
                print(f"✅ Copied: {file_name}")
        
        # Copy directories
        for dir_name in dirs_to_copy:
            src = self.project_dir / dir_name
            if src.exists():
                dst = self.temp_dir / dir_name
                shutil.copytree(src, dst, dirs_exist_ok=True)
                print(f"✅ Copied directory: {dir_name}")
    
    def find_pyarmor_executable(self):
        """Tìm PyArmor executable"""
        # Thử các đường dẫn có thể có
        python_dir = Path(sys.executable).parent
        possible_paths = [
            python_dir / "Scripts" / "pyarmor.exe",
            python_dir / "Scripts" / "pyarmor",
            "pyarmor"  # Fallback to PATH
        ]

        for path in possible_paths:
            try:
                if str(path) == "pyarmor":
                    # Test if pyarmor is in PATH
                    subprocess.run([str(path), "--version"],
                                 check=True, capture_output=True)
                    return str(path)
                elif path.exists():
                    return str(path)
            except (subprocess.CalledProcessError, FileNotFoundError):
                continue

        return None

    def obfuscate_with_pyarmor(self):
        """Obfuscate code với PyArmor"""
        print("Bat dau obfuscation voi PyArmor...")

        # Tìm PyArmor executable
        pyarmor_exe = self.find_pyarmor_executable()
        if not pyarmor_exe:
            print("Khong tim thay PyArmor executable")
            return False

        print(f"Su dung PyArmor: {pyarmor_exe}")
        os.chdir(self.temp_dir)

        # Tạo license với thời hạn
        license_cmd = [
            pyarmor_exe, "licenses",
            "--expired", "2025-12-31",
            "--bind-disk",
            "autok_license"
        ]
        
        try:
            subprocess.run(license_cmd, check=True, capture_output=True)
            print("Tao license thanh cong")
        except subprocess.CalledProcessError as e:
            print(f"Khong the tao license: {e}")

        # Obfuscate với cấu hình bảo mật cao
        obfuscate_cmd = [
            pyarmor_exe, "obfuscate",
            "--advanced", "2",
            "--restrict", "4",
            "--enable-jit",
            "--mix-str",
            "--recursive"
        ]
        
        # Thêm license nếu có
        license_file = self.temp_dir / "licenses" / "autok_license" / "license.lic"
        if license_file.exists():
            obfuscate_cmd.extend(["--with-license", str(license_file)])
        
        obfuscate_cmd.append("AutoK.py")
        
        try:
            result = subprocess.run(obfuscate_cmd, check=True, capture_output=True, text=True)
            print("Obfuscation thanh cong")
            return True
        except subprocess.CalledProcessError as e:
            print(f"Obfuscation that bai: {e}")
            print(f"Output: {e.stdout}")
            print(f"Error: {e.stderr}")
            return False
    
    def compile_with_nuitka(self):
        """Compile với Nuitka"""
        print("Bat dau compile voi Nuitka...")

        # Kiểm tra các thư mục có thể có sau PyArmor
        possible_dirs = [
            self.temp_dir / "dist",
            self.temp_dir / "obf",
            self.temp_dir
        ]

        obf_dir = None
        for dir_path in possible_dirs:
            if dir_path.exists() and (dir_path / "AutoK.py").exists():
                obf_dir = dir_path
                break

        if not obf_dir:
            print("Khong tim thay file AutoK.py da obfuscate")
            print("Cac thu muc co san:")
            for item in self.temp_dir.iterdir():
                print(f"  - {item.name}")
            return False

        print(f"Su dung thu muc: {obf_dir}")
        os.chdir(obf_dir)
        
        nuitka_cmd = [
            sys.executable, "-m", "nuitka",
            "--standalone",
            "--onefile",
            "--remove-output",
            "--no-pyi-file",
            "--disable-console",
            "--windows-disable-console",
            "--enable-plugin=pyqt5",
            "--output-filename=AutoK_Secure.exe",
            "AutoK.py"
        ]

        # Thêm include data directories nếu tồn tại
        data_dirs = ["template", "data", "icon", "screen"]
        for data_dir in data_dirs:
            if (obf_dir / data_dir).exists():
                nuitka_cmd.append(f"--include-data-dir={data_dir}={data_dir}")

        try:
            print(f"Chay lenh: {' '.join(nuitka_cmd)}")
            result = subprocess.run(nuitka_cmd, check=True, capture_output=True, text=True)
            print("Nuitka compile thanh cong")
            return True
        except subprocess.CalledProcessError as e:
            print(f"Nuitka compile that bai: {e}")
            print(f"Output: {e.stdout}")
            print(f"Error: {e.stderr}")
            return False
    
    def compress_with_upx(self):
        """Nén với UPX để tăng bảo mật"""
        print("📦 Nén với UPX...")
        
        exe_file = self.temp_dir / "dist" / "AutoK_Secure.exe"
        if not exe_file.exists():
            print("❌ Không tìm thấy file exe để nén")
            return False
        
        upx_cmd = ["upx", "--best", "--ultra-brute", str(exe_file)]
        
        try:
            subprocess.run(upx_cmd, check=True, capture_output=True)
            print("✅ UPX nén thành công")
            return True
        except subprocess.CalledProcessError as e:
            print(f"⚠️ UPX nén thất bại (không bắt buộc): {e}")
            return False
        except FileNotFoundError:
            print("⚠️ UPX không được cài đặt (không bắt buộc)")
            return False
    
    def copy_final_output(self):
        """Copy file cuoi cung ra thu muc output"""
        print("Copy file cuoi cung...")

        # Tạo thư mục output
        self.output_dir.mkdir(exist_ok=True)

        # Tìm file exe trong nhiều vị trí có thể có
        possible_locations = [
            self.temp_dir / "AutoK_Secure.exe",  # Nuitka output trực tiếp
            self.temp_dir / "dist" / "AutoK_Secure.exe",
            self.temp_dir / "AutoK.exe",
        ]

        exe_file = None
        for location in possible_locations:
            if location.exists():
                exe_file = location
                break

        if not exe_file:
            # Tìm tất cả file exe trong temp_dir
            exe_files = list(self.temp_dir.glob("*.exe"))
            if exe_files:
                exe_file = exe_files[0]
            else:
                print("Khong tim thay file exe")
                print("Cac file co san:")
                for item in self.temp_dir.iterdir():
                    print(f"  - {item.name}")
                return False
        
        # Copy file
        final_exe = self.output_dir / "AutoK_Secure.exe"
        shutil.copy2(exe_file, final_exe)

        print(f"File cuoi cung: {final_exe}")
        print(f"Kich thuoc: {final_exe.stat().st_size / 1024 / 1024:.2f} MB")

        return True
    
    def cleanup(self):
        """Don dep thu muc tam"""
        if self.temp_dir and self.temp_dir.exists():
            try:
                # Thử xóa nhiều lần với delay
                import time
                for attempt in range(3):
                    try:
                        shutil.rmtree(self.temp_dir)
                        print(f"Da xoa thu muc tam: {self.temp_dir}")
                        break
                    except PermissionError as e:
                        if attempt < 2:
                            print(f"Thu lai xoa thu muc tam (lan {attempt + 1}/3)...")
                            time.sleep(2)
                        else:
                            print(f"Khong the xoa thu muc tam: {self.temp_dir}")
                            print("Ban co the xoa thu cong sau khi build xong")
            except Exception as e:
                print(f"Loi khi xoa thu muc tam: {e}")
                print(f"Thu muc tam: {self.temp_dir}")
    
    def build(self):
        """Thuc hien toan bo qua trinh build"""
        try:
            print("Bat dau build bao mat AutoK...")

            # Kiểm tra dependencies
            self.check_dependencies()

            # Setup
            self.setup_temp_directory()
            self.copy_source_files()

            # Obfuscate
            if not self.obfuscate_with_pyarmor():
                print("Build that bai tai buoc obfuscation")
                return False

            # Compile
            if not self.compile_with_nuitka():
                print("Build that bai tai buoc compile")
                return False

            # Optional: Compress
            self.compress_with_upx()

            # Copy output
            if not self.copy_final_output():
                print("Build that bai tai buoc copy output")
                return False

            print("Build bao mat hoan thanh!")
            return True

        except Exception as e:
            print(f"Loi trong qua trinh build: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            self.cleanup()
    
    def check_dependencies(self):
        """Kiểm tra các dependency cần thiết"""
        print("Kiem tra cac dependency...")

        # Kiểm tra PyArmor
        try:
            result = subprocess.run([sys.executable, "-m", "pyarmor", "--version"],
                                 check=True, capture_output=True, text=True)
            print(f"PyArmor da cai dat: {result.stdout.strip()}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            try:
                # Thử cách khác
                result = subprocess.run(["pyarmor", "--version"],
                                     check=True, capture_output=True, text=True)
                print(f"PyArmor da cai dat: {result.stdout.strip()}")
            except (subprocess.CalledProcessError, FileNotFoundError):
                print("PyArmor chua duoc cai dat")
                print("Cai dat: pip install pyarmor")
                sys.exit(1)

        # Kiểm tra Nuitka
        try:
            result = subprocess.run([sys.executable, "-m", "nuitka", "--version"],
                                 check=True, capture_output=True, text=True)
            print(f"Nuitka da cai dat: {result.stdout.strip()}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            try:
                # Thử cách khác
                result = subprocess.run(["nuitka", "--version"],
                                     check=True, capture_output=True, text=True)
                print(f"Nuitka da cai dat: {result.stdout.strip()}")
            except (subprocess.CalledProcessError, FileNotFoundError):
                print("Nuitka chua duoc cai dat")
                print("Cai dat: pip install nuitka")
                sys.exit(1)

if __name__ == "__main__":
    builder = SecureBuild()
    success = builder.build()
    sys.exit(0 if success else 1)
