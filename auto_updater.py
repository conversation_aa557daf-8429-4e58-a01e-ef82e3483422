#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Auto Updater cho AutoK Tool
Kiểm tra và tải xuống phiên bản mới từ server
"""

import os
import sys
import json
import requests
import zipfile
import shutil
import subprocess
import tempfile
from pathlib import Path
from PyQt5.QtWidgets import QMessageBox, QProgressDialog, QApplication
from PyQt5.QtCore import QThread, pyqtSignal, Qt

# Cấu hình update server
UPDATE_CONFIG = {
    'version_check_url': 'https://your-server.com/api/autok/version',
    'download_url': 'https://your-server.com/api/autok/download',
    'current_version_file': 'version.txt',
    'backup_folder': 'backup_update',
    'temp_folder': 'temp_update'
}

class UpdateChecker:
    """Class kiểm tra phiên bản mới"""
    
    def __init__(self):
        self.current_version = self.get_current_version()
        
    def get_current_version(self):
        """L<PERSON>y phiên bản hiện tại"""
        try:
            version_file = UPDATE_CONFIG['current_version_file']
            if os.path.exists(version_file):
                with open(version_file, 'r') as f:
                    return f.read().strip()
            return "1.0.0"  # Version mặc định
        except Exception:
            return "1.0.0"
    
    def check_for_updates(self):
        """Kiểm tra phiên bản mới trên server"""
        try:
            response = requests.get(
                UPDATE_CONFIG['version_check_url'],
                timeout=10,
                headers={'User-Agent': 'AutoK-Updater/1.0'}
            )
            
            if response.status_code == 200:
                data = response.json()
                latest_version = data.get('version', '1.0.0')
                download_url = data.get('download_url', '')
                changelog = data.get('changelog', '')
                
                if self.is_newer_version(latest_version, self.current_version):
                    return {
                        'has_update': True,
                        'latest_version': latest_version,
                        'current_version': self.current_version,
                        'download_url': download_url,
                        'changelog': changelog
                    }
                else:
                    return {
                        'has_update': False,
                        'latest_version': latest_version,
                        'current_version': self.current_version
                    }
            else:
                return {'error': f'Server error: {response.status_code}'}
                
        except requests.RequestException as e:
            return {'error': f'Network error: {str(e)}'}
        except Exception as e:
            return {'error': f'Unknown error: {str(e)}'}
    
    def is_newer_version(self, latest, current):
        """So sánh phiên bản"""
        try:
            latest_parts = [int(x) for x in latest.split('.')]
            current_parts = [int(x) for x in current.split('.')]
            
            # Đảm bảo cả hai có cùng số phần tử
            max_len = max(len(latest_parts), len(current_parts))
            latest_parts.extend([0] * (max_len - len(latest_parts)))
            current_parts.extend([0] * (max_len - len(current_parts)))
            
            return latest_parts > current_parts
        except Exception:
            return False

class UpdateDownloader(QThread):
    """Thread tải xuống update"""
    
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    download_completed = pyqtSignal(str)
    download_failed = pyqtSignal(str)
    
    def __init__(self, download_url, save_path):
        super().__init__()
        self.download_url = download_url
        self.save_path = save_path
        
    def run(self):
        """Tải xuống file update"""
        try:
            self.status_updated.emit("Đang kết nối đến server...")
            
            response = requests.get(
                self.download_url,
                stream=True,
                timeout=30,
                headers={'User-Agent': 'AutoK-Updater/1.0'}
            )
            
            if response.status_code != 200:
                self.download_failed.emit(f"Server error: {response.status_code}")
                return
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0
            
            self.status_updated.emit("Đang tải xuống...")
            
            with open(self.save_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        if total_size > 0:
                            progress = int((downloaded_size / total_size) * 100)
                            self.progress_updated.emit(progress)
            
            self.status_updated.emit("Tải xuống hoàn thành!")
            self.download_completed.emit(self.save_path)
            
        except Exception as e:
            self.download_failed.emit(f"Download error: {str(e)}")

class AutoUpdater:
    """Class chính quản lý cập nhật"""
    
    def __init__(self, parent=None):
        self.parent = parent
        self.checker = UpdateChecker()
        
    def check_and_prompt_update(self, silent=False):
        """Kiểm tra và hỏi người dùng có muốn cập nhật không"""
        try:
            if not silent:
                # Hiển thị dialog checking
                checking_msg = QMessageBox(self.parent)
                checking_msg.setWindowTitle("Kiểm tra cập nhật")
                checking_msg.setText("Đang kiểm tra phiên bản mới...")
                checking_msg.setStandardButtons(QMessageBox.NoButton)
                checking_msg.show()
                QApplication.processEvents()
            
            result = self.checker.check_for_updates()
            
            if not silent:
                checking_msg.close()
            
            if 'error' in result:
                if not silent:
                    QMessageBox.warning(
                        self.parent,
                        "Lỗi kiểm tra cập nhật",
                        f"Không thể kiểm tra cập nhật:\n{result['error']}"
                    )
                return False
            
            if result['has_update']:
                return self.prompt_update(result)
            else:
                if not silent:
                    QMessageBox.information(
                        self.parent,
                        "Không có cập nhật",
                        f"Bạn đang sử dụng phiên bản mới nhất: {result['current_version']}"
                    )
                return False
                
        except Exception as e:
            if not silent:
                QMessageBox.critical(
                    self.parent,
                    "Lỗi",
                    f"Lỗi kiểm tra cập nhật: {str(e)}"
                )
            return False
    
    def prompt_update(self, update_info):
        """Hỏi người dùng có muốn cập nhật không"""
        changelog = update_info.get('changelog', 'Không có thông tin thay đổi.')
        
        msg = QMessageBox(self.parent)
        msg.setWindowTitle("Cập nhật có sẵn")
        msg.setIcon(QMessageBox.Information)
        msg.setText(
            f"Phiên bản mới có sẵn!\n\n"
            f"Phiên bản hiện tại: {update_info['current_version']}\n"
            f"Phiên bản mới: {update_info['latest_version']}\n\n"
            f"Thay đổi:\n{changelog}\n\n"
            f"Bạn có muốn cập nhật ngay bây giờ không?"
        )
        msg.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
        msg.setDefaultButton(QMessageBox.Yes)
        
        if msg.exec_() == QMessageBox.Yes:
            return self.start_update(update_info)
        
        return False
    
    def start_update(self, update_info):
        """Bắt đầu quá trình cập nhật"""
        try:
            # Tạo thư mục tạm
            temp_dir = UPDATE_CONFIG['temp_folder']
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
            os.makedirs(temp_dir)
            
            # Đường dẫn file tải xuống
            download_path = os.path.join(temp_dir, 'autok_update.zip')
            
            # Tạo progress dialog
            progress = QProgressDialog(
                "Đang tải xuống cập nhật...",
                "Hủy",
                0, 100,
                self.parent
            )
            progress.setWindowTitle("Cập nhật AutoK")
            progress.setWindowModality(Qt.WindowModal)
            progress.show()
            
            # Tạo downloader thread
            self.downloader = UpdateDownloader(
                update_info['download_url'],
                download_path
            )
            
            # Kết nối signals
            self.downloader.progress_updated.connect(progress.setValue)
            self.downloader.status_updated.connect(progress.setLabelText)
            self.downloader.download_completed.connect(
                lambda path: self.install_update(path, update_info['latest_version'], progress)
            )
            self.downloader.download_failed.connect(
                lambda error: self.handle_download_error(error, progress)
            )
            
            # Bắt đầu tải xuống
            self.downloader.start()
            
            return True
            
        except Exception as e:
            QMessageBox.critical(
                self.parent,
                "Lỗi cập nhật",
                f"Không thể bắt đầu cập nhật: {str(e)}"
            )
            return False
    
    def install_update(self, zip_path, new_version, progress_dialog):
        """Cài đặt cập nhật"""
        try:
            progress_dialog.setLabelText("Đang cài đặt cập nhật...")
            progress_dialog.setValue(50)
            
            # Tạo backup
            self.create_backup()
            
            progress_dialog.setValue(70)
            
            # Giải nén update
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(UPDATE_CONFIG['temp_folder'])
            
            progress_dialog.setValue(90)
            
            # Copy files mới
            self.copy_update_files()
            
            # Cập nhật version
            self.update_version_file(new_version)
            
            progress_dialog.setValue(100)
            progress_dialog.close()
            
            # Thông báo thành công
            msg = QMessageBox.information(
                self.parent,
                "Cập nhật thành công",
                f"AutoK đã được cập nhật lên phiên bản {new_version}!\n\n"
                f"Ứng dụng sẽ khởi động lại để áp dụng thay đổi."
            )
            
            # Khởi động lại ứng dụng
            self.restart_application()
            
        except Exception as e:
            progress_dialog.close()
            QMessageBox.critical(
                self.parent,
                "Lỗi cài đặt",
                f"Không thể cài đặt cập nhật: {str(e)}\n\n"
                f"Ứng dụng sẽ được khôi phục từ backup."
            )
            self.restore_backup()
    
    def create_backup(self):
        """Tạo backup trước khi cập nhật"""
        backup_dir = UPDATE_CONFIG['backup_folder']
        if os.path.exists(backup_dir):
            shutil.rmtree(backup_dir)
        os.makedirs(backup_dir)
        
        # Backup các file quan trọng
        important_files = [
            'AutoK.py', 'Form.py', 'addads.py', 'addwallet.py',
            'chplay.py', 'closepayment.py', 'deletecardclose.py',
            'mua.py', 'password_manager.py', 'path_manager.py',
            'license_manager.py', 'version.txt'
        ]
        
        for file in important_files:
            if os.path.exists(file):
                shutil.copy2(file, backup_dir)
    
    def copy_update_files(self):
        """Copy files từ update"""
        update_dir = os.path.join(UPDATE_CONFIG['temp_folder'], 'AutoK')
        
        if os.path.exists(update_dir):
            for item in os.listdir(update_dir):
                src = os.path.join(update_dir, item)
                dst = item
                
                if os.path.isfile(src):
                    shutil.copy2(src, dst)
                elif os.path.isdir(src):
                    if os.path.exists(dst):
                        shutil.rmtree(dst)
                    shutil.copytree(src, dst)
    
    def update_version_file(self, new_version):
        """Cập nhật file version"""
        with open(UPDATE_CONFIG['current_version_file'], 'w') as f:
            f.write(new_version)
    
    def restore_backup(self):
        """Khôi phục từ backup"""
        backup_dir = UPDATE_CONFIG['backup_folder']
        if os.path.exists(backup_dir):
            for item in os.listdir(backup_dir):
                src = os.path.join(backup_dir, item)
                dst = item
                shutil.copy2(src, dst)
    
    def restart_application(self):
        """Khởi động lại ứng dụng"""
        try:
            # Lấy đường dẫn executable hiện tại
            if getattr(sys, 'frozen', False):
                # Nếu đang chạy từ exe
                executable = sys.executable
            else:
                # Nếu đang chạy từ Python script
                executable = [sys.executable, 'AutoK.py']
            
            # Khởi động lại
            subprocess.Popen(executable)
            
            # Thoát ứng dụng hiện tại
            QApplication.quit()
            
        except Exception as e:
            QMessageBox.critical(
                self.parent,
                "Lỗi khởi động lại",
                f"Không thể khởi động lại ứng dụng: {str(e)}\n\n"
                f"Vui lòng khởi động lại thủ công."
            )
    
    def handle_download_error(self, error, progress_dialog):
        """Xử lý lỗi tải xuống"""
        progress_dialog.close()
        QMessageBox.critical(
            self.parent,
            "Lỗi tải xuống",
            f"Không thể tải xuống cập nhật: {error}"
        )
    
    def cleanup_temp_files(self):
        """Dọn dẹp files tạm"""
        try:
            temp_dir = UPDATE_CONFIG['temp_folder']
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
        except Exception:
            pass

# Hàm tiện ích
def check_for_updates_on_startup(parent=None):
    """Kiểm tra cập nhật khi khởi động (silent)"""
    updater = AutoUpdater(parent)
    return updater.check_and_prompt_update(silent=True)

def manual_check_for_updates(parent=None):
    """Kiểm tra cập nhật thủ công"""
    updater = AutoUpdater(parent)
    return updater.check_and_prompt_update(silent=False)
