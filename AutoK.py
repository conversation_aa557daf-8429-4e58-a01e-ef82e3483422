
import sys
import subprocess
import pyotp  # pip install pyotp
import concurrent.futures
import os
import webbrowser
import time
import requests # type: ignore
import urllib.parse
import random
import threading
import socket
import json
import tempfile
import logging

# Import cho SOCKS proxy
try:
    import socks # type: ignore
except ImportError:
    print("⚠️ Cần cài đặt PySocks: pip install PySocks")
    socks = None

# Import path_manager để quản lý đường dẫn
import path_manager
from LicenseManagerDialog import LicenseManagerDialog
import license_manager
from PyQt5.QtCore import QSettings # type: ignore
# Import password_manager để quản lý mật khẩu tập trung
from password_manager import password_manager


# Đường dẫn cơ sở của ứng dụng
BASE_PATH = path_manager.get_base_path()

# Kiểm tra license khi khởi động
def check_license():
    """
    Kiểm tra license key khi khởi động ứng dụng

    Returns:
        tuple: (is_valid, features)
            - is_valid (bool): True nếu license hợp lệ
            - features (str): <PERSON><PERSON><PERSON> t<PERSON><PERSON> năng được phép sử dụng
    """
    try:
        settings = QSettings("AutoK", "License")
        saved_key = settings.value("license_key", "")

        if not saved_key:
            print("⚠️ Không tìm thấy license key đã lưu")
            return False, "ALL_BLOCKED"

        # Lấy machine UUID
        machine_uuid = license_manager.get_machine_uuid()
        result = license_manager.check_license(saved_key, machine_uuid)

        # Xử lý kết quả trả về (có thể là 3 hoặc 4 phần tử)
        if len(result) == 4:
            valid, message, features, user_info = result
        else:
            valid, message, features = result
            user_info = {}

        if valid:
            print(f"✅ License hợp lệ: {features}")
            return True, features
        else:
            print(f"❌ License không hợp lệ: {message}")
            return False, "ALL_BLOCKED"

    except Exception as e:
        print(f"❌ Lỗi kiểm tra license: {e}")
        return False, "ALL_BLOCKED"

# Hàm kiểm tra tính năng được phép sử dụng
def is_feature_allowed(feature_name, available_features):
    """
    Kiểm tra xem một tính năng có được phép sử dụng không

    Args:
        feature_name (str): Tên tính năng cần kiểm tra
        available_features (str): Chuỗi chứa các tính năng được phép, phân cách bởi dấu phẩy

    Returns:
        bool: True nếu tính năng được phép sử dụng
    """
    if not available_features:
        return False

    # Nếu available_features là "ALL", cho phép tất cả tính năng
    if available_features.upper() == "ALL":
        return True

    # Tách chuỗi tính năng thành danh sách
    feature_list = [f.strip().upper() for f in available_features.split(",")]

    # Kiểm tra tính năng
    return feature_name.upper() in feature_list



# Hàm để tìm đường dẫn đến ADB
def get_adb_path():
    """
    Tìm đường dẫn đến ADB, ưu tiên tìm trong thư mục của ứng dụng trước
    """
    # Danh sách các vị trí có thể chứa ADB
    possible_paths = [
        # Trong thư mục platform-tools của ứng dụng
        os.path.join(BASE_PATH, "platform-tools", "adb.exe"),
        os.path.join(BASE_PATH, "_internal", "platform-tools", "adb.exe"),
        # Trong thư mục scrcpy của ứng dụng
        os.path.join(BASE_PATH, "scrcpy", "adb.exe"),
        os.path.join(BASE_PATH, "_internal", "scrcpy", "adb.exe"),
        # Đường dẫn mặc định
        "adb"
    ]

    # Kiểm tra từng đường dẫn
    for path in possible_paths:
        if os.path.exists(path):
            print(f"✅ Đã tìm thấy ADB tại: {path}")
            return path

    # Nếu không tìm thấy, trả về "adb" để sử dụng ADB từ PATH
    print("⚠️ Không tìm thấy ADB trong thư mục ứng dụng, sẽ sử dụng ADB từ PATH")
    return "adb"

# Hàm để tìm đường dẫn đến scrcpy
def get_scrcpy_path():
    """
    Tìm đường dẫn đến scrcpy, ưu tiên tìm trong thư mục của ứng dụng trước
    """
    # Danh sách các vị trí có thể chứa scrcpy
    possible_paths = [
        # Trong thư mục scrcpy của ứng dụng (ưu tiên cao nhất)
        os.path.join(BASE_PATH, "scrcpy", "scrcpy.exe"),
        # Trong thư mục _internal khi đóng gói
        os.path.join(BASE_PATH, "_internal", "scrcpy", "scrcpy.exe"),
        # Trong thư mục dist khi đóng gói
        os.path.join(BASE_PATH, "dist", "AutoK", "scrcpy", "scrcpy.exe"),
        # Các vị trí cài đặt phổ biến
        r"C:\Program Files\scrcpy\scrcpy.exe",
        r"C:\scrcpy\scrcpy.exe",
        r"C:\tools\scrcpy\scrcpy.exe",
        # Đường dẫn mặc định (ưu tiên thấp nhất)
        "scrcpy"
    ]

    # Kiểm tra từng đường dẫn
    for path in possible_paths:
        if os.path.exists(path):
            print(f"✅ Đã tìm thấy scrcpy tại: {path}")
            return path

    # Nếu không tìm thấy, trả về None để hiển thị thông báo lỗi
    print("⚠️ Không tìm thấy scrcpy trong thư mục ứng dụng hoặc các vị trí cài đặt phổ biến")
    return None

# Đường dẫn đến ADB và scrcpy
ADB_PATH = get_adb_path()
SCRCPY_PATH = get_scrcpy_path()

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QTableWidgetItem, QCheckBox,
    QWidget, QHBoxLayout, QHeaderView, QAbstractItemView, QPushButton,
    QMessageBox, QLabel, QDialog, QVBoxLayout, QInputDialog, QPlainTextEdit,
    QGroupBox, QListWidget, QListWidgetItem, QDialogButtonBox, QLineEdit
)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal, QStringListModel # type: ignore
from PyQt5.QtGui import QGuiApplication, QKeySequence, QPixmap, QImage, QIcon
from Form import Ui_MainWindow

# Import các module tự tạo
from closepayment import DeviceActions
from addads import add_ads
from deletecardclose import delete_card_close
from addwallet import add_wallet
from chplay import ChPlayLogin

# Setup logging cho proxy (tắt debug để console sạch sẽ)
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.ERROR, format='%(asctime)s - %(levelname)s - %(message)s')
logger.setLevel(logging.ERROR)

# ===== PROXY CLASSES =====

class ProxyEditDialog(QDialog):
    """Dialog để chỉnh sửa file proxy"""
    def __init__(self, proxy_file_path, parent=None):
        super().__init__(parent)
        self.proxy_file_path = proxy_file_path
        self.setup_ui()
        self.load_proxy_file()

    def setup_ui(self):
        self.setWindowTitle("Chỉnh sửa Proxy")
        self.setModal(True)
        self.resize(600, 500)

        # Thêm icon nếu có
        icon_path = os.path.join("icon", "iconproxy.ico")
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))

        layout = QVBoxLayout()

        # Instructions
        instructions = QLabel("""
Hướng dẫn format proxy:
• socks5://username:password@ip:port
• ip:port:username:password
• ip:port (không có auth)
• Dòng bắt đầu bằng # sẽ bị bỏ qua
        """)
        instructions.setStyleSheet("color: #666; font-size: 10px;")
        layout.addWidget(instructions)

        # Text editor
        self.text_edit = QPlainTextEdit()
        self.text_edit.setPlainText("Đang tải...")
        layout.addWidget(self.text_edit)

        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.save_and_accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        self.setLayout(layout)

    def load_proxy_file(self):
        """Tải nội dung file proxy"""
        try:
            if os.path.exists(self.proxy_file_path):
                with open(self.proxy_file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    self.text_edit.setPlainText(content)
            else:
                self.text_edit.setPlainText("# File proxy chưa tồn tại\n# Thêm proxy theo format:\n# ip:port:username:password\n# hoặc ip:port")
        except Exception as e:
            QMessageBox.critical(self, "Lỗi", f"Không thể đọc file proxy:\n{e}")
            self.text_edit.setPlainText(f"# Lỗi đọc file: {e}")

    def save_and_accept(self):
        """Lưu file và đóng dialog"""
        try:
            content = self.text_edit.toPlainText()

            # Tạo thư mục nếu chưa có
            os.makedirs(os.path.dirname(self.proxy_file_path), exist_ok=True)

            with open(self.proxy_file_path, 'w', encoding='utf-8') as f:
                f.write(content)

            # Log thông báo lưu proxy (không hiển thị message box)
            # self.log_message("✅ Đã lưu file proxy thành công!")  # Không có self.log_message trong dialog
            self.accept()
        except Exception as e:
            QMessageBox.critical(self, "Lỗi", f"Không thể lưu file proxy:\n{e}")


class ProxyHealthChecker(QThread):
    """Thread để kiểm tra tình trạng proxy"""
    health_checked = pyqtSignal(str, bool)  # proxy_info, is_healthy

    def __init__(self, proxy_info, test_url="http://httpbin.org/ip", timeout=10):
        super().__init__()
        self.proxy_info = proxy_info
        self.test_url = test_url
        self.timeout = timeout

    def run(self):
        try:
            ip, port, user, pwd = self.proxy_info
            proxy_url = f"socks5://{user}:{pwd}@{ip}:{port}" if user and pwd else f"socks5://{ip}:{port}"

            proxies = {
                'http': proxy_url,
                'https': proxy_url
            }

            response = requests.get(self.test_url, proxies=proxies, timeout=self.timeout)
            is_healthy = response.status_code == 200
            self.health_checked.emit(f"{ip}:{port}", is_healthy)
        except Exception as e:
            logger.error(f"Health check failed for {self.proxy_info[0]}:{self.proxy_info[1]} - {e}")
            self.health_checked.emit(f"{self.proxy_info[0]}:{self.proxy_info[1]}", False)


class ChromeProxyDialog(QDialog):
    """Dialog để chọn proxy và mở Chrome"""
    def __init__(self, proxy_list, config, parent=None):
        super().__init__(parent)
        self.proxy_list = proxy_list
        self.config = config
        self.setup_ui()

    def setup_ui(self):
        self.setWindowTitle("Mở Chrome Proxy")
        self.setModal(True)
        self.resize(500, 400)

        # Thêm icon
        icon_path = os.path.join("icon", "Logo.ico")
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))

        layout = QVBoxLayout()

        # Website input
        website_group = QGroupBox("🌍 Website")
        website_layout = QVBoxLayout()

        self.website_input = QLineEdit()
        self.website_input.setPlaceholderText("Nhập URL (ví dụ: https://google.com)")
        self.website_input.setText("https://www.google.com")
        website_layout.addWidget(self.website_input)

        # Quick website buttons
        quick_layout = QHBoxLayout()
        quick_sites = [
            ("🔍 IP Check", "https://ip8.com"),
            ("🌐 Google", "https://www.google.com"),
            ("📺 YouTube", "https://www.youtube.com"),
            ("🚀 Captcha Solver", "https://chromewebstore.google.com/detail/buster-captcha-solver-for/mpbjkejclgfgadiemmefgebjfooflfhl?pli=1")
        ]

        for name, url in quick_sites:
            btn = QPushButton(name)
            btn.clicked.connect(lambda checked, u=url: self.website_input.setText(u))
            quick_layout.addWidget(btn)

        website_layout.addLayout(quick_layout)
        website_group.setLayout(website_layout)
        layout.addWidget(website_group)

        # Proxy selection
        proxy_group = QGroupBox("🔗 Chọn Proxy")
        proxy_layout = QVBoxLayout()

        self.proxy_list_widget = QListWidget()
        for i, proxy_info in enumerate(self.proxy_list):
            if proxy_info:
                ip, port, user, pwd = proxy_info
                display_text = f"Proxy {i+1}: {ip}:{port}"
                if user:
                    display_text += f" (User: {user})"
                item = QListWidgetItem(display_text)
                item.setData(Qt.UserRole, (i, proxy_info))
                self.proxy_list_widget.addItem(item)

        # Chọn proxy đầu tiên mặc định
        if self.proxy_list_widget.count() > 0:
            self.proxy_list_widget.setCurrentRow(0)

        proxy_layout.addWidget(self.proxy_list_widget)
        proxy_group.setLayout(proxy_layout)
        layout.addWidget(proxy_group)

        # Info
        info_label = QLabel(
            "💡 Chrome sẽ mở với profile riêng để tránh xung đột với Chrome hiện tại.\n"
            "🔒 Tất cả traffic sẽ đi qua proxy đã chọn."
        )
        info_label.setStyleSheet("color: #666; font-style: italic;")
        layout.addWidget(info_label)

        # Buttons
        button_layout = QHBoxLayout()

        self.ok_btn = QPushButton("🚀 Mở Chrome")
        self.ok_btn.clicked.connect(self.accept)
        self.ok_btn.setDefault(True)

        self.cancel_btn = QPushButton("❌ Hủy")
        self.cancel_btn.clicked.connect(self.reject)

        button_layout.addWidget(self.ok_btn)
        button_layout.addWidget(self.cancel_btn)

        layout.addLayout(button_layout)
        self.setLayout(layout)

    def get_selected_proxy(self):
        """Lấy proxy đã chọn"""
        current_item = self.proxy_list_widget.currentItem()
        if current_item:
            return current_item.data(Qt.UserRole)
        return None

    def get_website(self):
        """Lấy website đã nhập"""
        return self.website_input.text().strip()


class MainApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.ui = Ui_MainWindow()
        self.ui.setupUi(self)
        self.setWindowTitle("AutoK Tool")

        # Biến để theo dõi trạng thái đang chạy của các chức năng
        self.is_deleting_card = False  # Trạng thái đang xóa thẻ
        self.stop_deleting_card = False  # Cờ để dừng quá trình xóa thẻ
        self.is_adding_card = False  # Trạng thái đang thêm thẻ
        self.stop_adding_card = False  # Cờ để dừng quá trình thêm thẻ
        self.is_mua_game_running = False  # Trạng thái đang chạy chức năng mua game
        self.stop_mua_game = False  # Cờ để dừng quá trình mua game
        self.is_chplay_running = False  # Trạng thái đang chạy chức năng đăng nhập bằng Google Play
        self.stop_chplay = False  # Cờ để dừng quá trình đăng nhập bằng Google Play

        # Biến để theo dõi trạng thái start_actions
        self.is_start_actions_running = False  # Trạng thái đang chạy start_actions
        self.stop_start_actions = False  # Cờ để dừng quá trình start_actions

        # ===== PROXY VARIABLES =====
        # Khởi tạo các biến proxy
        self.proxy_list = []  # Danh sách proxy từ file
        self.current_proxies = []  # Proxy hiện tại đang sử dụng
        self.device_proxy_mapping = {}  # Mapping device_id -> proxy_string
        self.auto_assign_enabled = False  # Trạng thái auto-assign (mặc định OFF)
        self.proxy_servers = []  # Danh sách proxy servers đang chạy
        self.local_ports = []  # Danh sách local ports
        self.last_mobile_count = 0  # Số lượng thiết bị mobile lần cuối
        self.device_ids_cache = set()  # Cache device IDs

        # ===== SMART PROXY VARIABLES =====
        self.device_email_mapping = {}  # Mapping device_id -> email_address
        self.email_proxy_mapping = {}  # Mapping email -> proxy_info từ mail.txt
        self.lan_ip = None  # Cache IP LAN để tránh gọi lại nhiều lần

        # Health check variables
        self.health_check_running = False  # Trạng thái đang chạy health check
        self.health_checkers = []  # Danh sách health checker threads
        self.showing_health_results = False  # Trạng thái hiển thị kết quả health check
        self.health_check_results = {}  # Kết quả health check

        # Cấu hình proxy
        self.proxy_config = {
            'proxy_file': os.path.join('data', 'proxy.txt'),  # File proxy trong thư mục data
            'start_port': 2001,  # Port bắt đầu (đổi từ 1081 thành 2001)
            'num_devices': 5,  # Số thiết bị mặc định
            'connection_timeout': 10,  # Timeout kết nối
            'health_check_url': 'http://httpbin.org/ip',  # URL kiểm tra health
            'local_bind': '0.0.0.0',  # Bind address cho proxy server
            'max_devices': 50  # Số thiết bị tối đa
        }

        # Proxy server management
        self.active_servers = []  # Danh sách proxy servers đang chạy
        self.proxy_threads = []  # Danh sách proxy threads

        # Tạo QTimer để cập nhật UI định kỳ
        self.ui_update_timer = QTimer(self)
        self.ui_update_timer.timeout.connect(self.force_update_ui)
        self.ui_update_timer.start(500)  # Cập nhật UI mỗi 500ms

        # Initialize UI update queue để tránh UI freeze
        self.ui_update_queue = []
        self.ui_update_timer_fast = QTimer(self)
        self.ui_update_timer_fast.timeout.connect(self.process_ui_updates)
        self.ui_update_timer_fast.start(50)  # Process updates every 50ms

        # Điều chỉnh giao diện dựa trên DPI của hệ thống
        self.adjust_ui_for_dpi()

        # Đọc dữ liệu từ file card.txt và cập nhật các ô số
        self.update_card_stats()

        # Cấu hình bảng
        self.ui.tableWidget.setSelectionBehavior(QAbstractItemView.SelectItems)
        self.ui.tableWidget.setEditTriggers(QAbstractItemView.DoubleClicked | QAbstractItemView.SelectedClicked)
        self.ui.tableWidget.setColumnWidth(0, 27)   # Cột 0 (ẩn tên) hoặc dùng cho icon?
        self.ui.tableWidget.setColumnWidth(1, 90)  # Devices
        self.ui.tableWidget.setColumnWidth(2, 90)   # Pass
        self.ui.tableWidget.setColumnWidth(3, 90)   # Authen
        self.ui.tableWidget.setColumnWidth(4, 120)  # Data
        self.ui.tableWidget.setColumnWidth(5, 200)  # Log


        # Tạo vách ngăn giữa tiêu đề cột và nội dung
        self.ui.tableWidget.setStyleSheet("""
            QHeaderView::section {
                background-color: #bdbfbf;
                padding: 4px;
                border: 1px solid #d0d0d0;
                border-bottom: 2px solid #a0a0a0;
                font-weight: bold;
                color: darkblue;
            }
            QTableWidget {
                gridline-color: #d0d0d0;
                selection-background-color: #7cb9d6;
            }
        """)

        # Kết nối các nút với chức năng
        self.ui.paste_pass_Button.clicked.connect(lambda: self.send_column_to_device(2))
        self.ui.pase_authen_Button.clicked.connect(lambda: self.send_column_to_device(3, is_totp=True))
        self.ui.pase_data_Button.clicked.connect(lambda: self.send_column_to_device(4))
        self.ui.home_Button.clicked.connect(self.go_to_home)
        self.ui.add_card_Button.clicked.connect(self.addcardads)  # Kết nối với addcardads để thực hiện chuỗi hành động trong app Google Ads
        self.ui.close_payment_Button.clicked.connect(self.close_payment)
        self.ui.xoa_the_close_Button.clicked.connect(self.delete_card)  # Kết nối với delete_card để thực hiện chuỗi hành động mở Chrome và trang payments.google.com
        self.ui.view_Button.clicked.connect(self.view_device_screen)  # Kết nối nút View với chức năng xem màn hình
        self.ui.pushButton_Mua.clicked.connect(self.mua_game)  # Kết nối nút Mua với chức năng mua game
        self.ui.button_wifi.clicked.connect(self.toggle_wifi)  # Kết nối nút wifi với chức năng bật/tắt wifi
        self.ui.button_wifi.setChecked(True)  # Thiết lập trạng thái mặc định là ON (checked)
        self.ui.chplay.clicked.connect(self.chplay_login)  # Kết nối nút chplay với chức năng đăng nhập bằng Google Play
        self.ui.toolButton_wallet.clicked.connect(self.add_wallet)  # Kết nối nút wallet với chức năng thêm ví

        # Kết nối các nút changer, restore và backup
        if hasattr(self.ui, 'changer'):
            self.ui.changer.clicked.connect(self.changer_button_clicked)  # Kết nối nút changer với chức năng changer
        if hasattr(self.ui, 'restore'):
            self.ui.restore.clicked.connect(self.restore_button_clicked)  # Kết nối nút restore với chức năng restore
        if hasattr(self.ui, 'backup'):
            self.ui.backup.clicked.connect(self.backup_button_clicked)  # Kết nối nút backup với chức năng backup

        # Thiết lập tooltip cho các button
        self.setup_button_tooltips()

        # Kết nối các nút trong tab Setting
        if hasattr(self.ui, 'open_mail_Button'):
            self.ui.open_mail_Button.clicked.connect(self.open_mail_file)
        if hasattr(self.ui, 'open_card_Button'):
            self.ui.open_card_Button.clicked.connect(self.open_card_file)
        if hasattr(self.ui, 'pushButton'):
            self.ui.pushButton.clicked.connect(self.open_data_folder)
        if hasattr(self.ui, 'licenses'):
            self.ui.licenses.clicked.connect(self.show_license_manager)

        # ===== KẾT NỐI CÁC NÚT PROXY =====
        # Kết nối các nút proxy với chức năng tương ứng
        if hasattr(self.ui, 'pushButton_2'):  # Đổi Proxy
            self.ui.pushButton_2.clicked.connect(self.rotate_proxies)
        if hasattr(self.ui, 'pushButton_4'):  # Check Proxy
            self.ui.pushButton_4.clicked.connect(self.check_proxy_health)
        if hasattr(self.ui, 'pushButton_5'):  # Dừng tất cả
            self.ui.pushButton_5.clicked.connect(self.stop_all_proxies)
        if hasattr(self.ui, 'pushButton_6'):  # Config Proxy
            self.ui.pushButton_6.clicked.connect(self.config_proxy)
        if hasattr(self.ui, 'pushButton_7'):  # Auto-Assign
            self.ui.pushButton_7.clicked.connect(self.toggle_auto_assign)
        if hasattr(self.ui, 'pushButton_8'):  # Open Chrome
            self.ui.pushButton_8.clicked.connect(self.open_chrome_with_proxy)

        # Kết nối checkbox chọn tất cả với phương thức toggle_all_checkboxes
        if hasattr(self.ui, 'checkBoxALL'):
            self.ui.checkBoxALL.setChecked(False)  # Thiết lập trạng thái mặc định là unchecked
            self.update_checkbox_all_icon()  # Cập nhật icon theo trạng thái
            self.ui.checkBoxALL.stateChanged.connect(self.toggle_all_checkboxes)
            self.ui.checkBoxALL.stateChanged.connect(self.update_checkbox_all_icon)  # Cập nhật icon khi trạng thái thay đổi
        elif hasattr(self.ui, 'checkBox'):
            self.ui.checkBox.setChecked(False)  # Thiết lập trạng thái mặc định là unchecked
            self.ui.checkBox.stateChanged.connect(self.toggle_all_checkboxes)

        # Cấu hình thanh trượt kích thước màn hình
        if hasattr(self.ui, 'horizontalSlider'):
            # Đặt giá trị mặc định tương ứng với kích thước 270x480
            # Phạm vi chiều rộng từ 200 đến 518 pixel
            min_width = 200
            max_width = 518
            default_width = 250  # Kích thước mặc định mới
            width_range = max_width - min_width

            # Kiểm tra xem có giá trị đã lưu không
            saved_width = self.load_slider_value()
            if saved_width:
                default_width = saved_width
                self.log_message(f"Đã tải giá trị kích thước đã lưu: {default_width}")

        # Tải giá trị spinbox goi1, goi2, goi3 đã lưu
        if hasattr(self.ui, 'goi1') and hasattr(self.ui, 'goi2') and hasattr(self.ui, 'goi3'):
            saved_values = self.load_spinbox_values()
            if saved_values:
                goi1_value, goi2_value, goi3_value = saved_values
                self.ui.goi1.setValue(goi1_value)
                self.ui.goi2.setValue(goi2_value)
                self.ui.goi3.setValue(goi3_value)
                self.log_message(f"Đã tải giá trị gói đã lưu: Gói 1={goi1_value}, Gói 2={goi2_value}, Gói 3={goi3_value}")

            # Kết nối các spinbox với phương thức save_spinbox_values để lưu giá trị khi thay đổi
            self.ui.goi1.valueChanged.connect(self.save_spinbox_values)
            self.ui.goi2.valueChanged.connect(self.save_spinbox_values)
            self.ui.goi3.valueChanged.connect(self.save_spinbox_values)

            # Tính toán giá trị thanh trượt tương ứng với kích thước mặc định
            # Công thức: (default_width - min_width) / width_range * 100 = slider_value
            slider_value = int((default_width - min_width) / width_range * 100)
            # Đảm bảo giá trị nằm trong khoảng 0-100
            slider_value = max(0, min(100, slider_value))

            self.ui.horizontalSlider.setMinimum(0)
            self.ui.horizontalSlider.setMaximum(100)

            # Đặt flag để tránh lưu tự động khi khởi tạo
            self._initializing_slider = True
            self.ui.horizontalSlider.setValue(slider_value)

            # Hiển thị giá trị khi thanh trượt thay đổi
            self.ui.horizontalSlider.valueChanged.connect(self.update_slider_value)

            # Cập nhật giá trị ban đầu mà không lưu
            self.update_slider_value_without_save(slider_value)

            # Bỏ flag sau khi khởi tạo xong
            self._initializing_slider = False

            # Lưu kích thước mặc định để sử dụng sau này
            self.default_scrcpy_width = default_width
            aspect_ratio = 16.0 / 9.0
            self.default_scrcpy_height = int(default_width * aspect_ratio)

        self.ui.comboBox_wallet_ads.setCurrentIndex(1)  # Chọn mặc định là "Ads"

        # Thiết lập giá trị mặc định cho port doma
        if hasattr(self.ui, 'lineEdit_port_doma'):
            self.ui.lineEdit_port_doma.setText("60708")
            # Kết nối sự kiện thay đổi giá trị port doma
            self.ui.lineEdit_port_doma.textChanged.connect(self.port_doma_changed)

        # Kết nối sự kiện thay đổi giá trị spinBox_so_lan_add_card
        if hasattr(self.ui, 'spinBox_so_lan_add_card'):
            self.ui.spinBox_so_lan_add_card.valueChanged.connect(self.save_spinbox_values)

        # Kết nối sự kiện thay đổi giá trị spinBox_view
        if hasattr(self.ui, 'spinBox_view'):
            # Thiết lập phạm vi cho spinBox_view
            self.ui.spinBox_view.setMinimum(1)
            self.ui.spinBox_view.setMaximum(10)

            # Tải giá trị đã lưu hoặc sử dụng mặc định
            saved_view_per_row = self.load_view_per_row_value()
            if saved_view_per_row is not None:
                self.ui.spinBox_view.setValue(saved_view_per_row)
                self.log_message(f"Đã tải giá trị số thiết bị trên 1 hàng: {saved_view_per_row}")
            else:
                self.ui.spinBox_view.setValue(5)  # Giá trị mặc định là 5

            self.ui.spinBox_view.valueChanged.connect(self.save_spinbox_values)

        # Kết nối sự kiện thay đổi giá trị spinBox_error
        if hasattr(self.ui, 'spinBox_error'):
            # Thiết lập phạm vi cho spinBox_error (1-50 lần thất bại)
            self.ui.spinBox_error.setMinimum(1)
            self.ui.spinBox_error.setMaximum(50)

            # Tải giá trị đã lưu hoặc sử dụng mặc định
            saved_max_failures = self.load_max_failures_value()
            if saved_max_failures is not None:
                self.ui.spinBox_error.setValue(saved_max_failures)
                self.log_message(f"Đã tải giá trị giới hạn thất bại: {saved_max_failures}")
            else:
                self.ui.spinBox_error.setValue(10)  # Giá trị mặc định là 10

            self.ui.spinBox_error.valueChanged.connect(self.save_spinbox_values)

        # Thiết lập giá trị mặc định và kết nối sự kiện thay đổi giá trị comboBox_restore
        if hasattr(self.ui, 'comboBox_restore'):
            self.ui.comboBox_restore.setCurrentIndex(0)  # Chọn mặc định là "Restore"
            self.ui.comboBox_restore.currentIndexChanged.connect(self.restore_option_changed)
            # Ghi log giá trị mặc định
            self.log_message("✅ Đã thiết lập chế độ mặc định: Restore (API: restore)")

        # Kết nối nút Start với phương thức start_actions
        self.ui.start_stop_Button.clicked.connect(self.start_actions)

        # Load danh sách thiết bị ADB
        self.load_adb_devices()

        # Khởi tạo password_manager và tải mật khẩu từ file mail.txt
        password_manager.load_passwords_from_mail_file()
        # Tải mật khẩu từ bảng
        self.ui.tableWidget.itemChanged.connect(self.update_password_from_table)
        # Tải mật khẩu từ bảng khi ứng dụng khởi động
        QTimer.singleShot(1000, lambda: password_manager.load_passwords_from_table(self.ui.tableWidget))

        # ===== KHỞI TẠO PROXY =====
        # Tải proxy từ file và khởi tạo auto-assign
        self.load_proxies()
        # Tải email-proxy mapping từ mail.txt
        self.load_email_proxy_mapping()
        self.update_auto_assign_button()
        # Cập nhật hiển thị ban đầu
        self.update_proxy_display()
        # Tự động kiểm tra và gán proxy sau khi load thiết bị
        QTimer.singleShot(2000, self.check_and_auto_assign_proxies_on_startup)

        # Kiểm tra và cập nhật quyền truy cập tab Proxy
        QTimer.singleShot(1000, self.check_and_update_proxy_tab_access)


    def force_update_ui(self):
        """
        Cập nhật giao diện người dùng một cách cưỡng bức.
        Phương thức này được gọi định kỳ bởi QTimer để đảm bảo UI luôn được cập nhật.
        """
        # Cập nhật giao diện người dùng
        QApplication.processEvents()

        # Cập nhật lại một lần nữa để đảm bảo tất cả các thay đổi được xử lý
        QApplication.processEvents()

        # Kiểm tra xem có đang chạy chức năng add card không
        if hasattr(self, 'is_adding_card') and self.is_adding_card:
            # Cập nhật trạng thái của tất cả các thiết bị đang thêm thẻ
            for row in range(self.ui.tableWidget.rowCount()):
                widget = self.ui.tableWidget.cellWidget(row, 0)
                if widget:
                    checkbox = widget.findChild(QCheckBox)
                    if checkbox and checkbox.isChecked():
                        # Cập nhật UI cho thiết bị này
                        status_item = self.ui.tableWidget.item(row, 5)
                        if status_item:
                            # Lưu trạng thái hiện tại
                            current_text = status_item.text()
                            # Đặt lại trạng thái để kích hoạt cập nhật UI
                            status_item.setText(current_text + " ")
                            QApplication.processEvents()
                            # Đặt lại trạng thái ban đầu
                            status_item.setText(current_text)
                            QApplication.processEvents()

        # Kiểm tra xem có đang chạy chức năng mua game không
        if self.is_mua_game_running:
            # Cập nhật trạng thái của tất cả các thiết bị đang mua game
            for row in range(self.ui.tableWidget.rowCount()):
                widget = self.ui.tableWidget.cellWidget(row, 0)
                if widget:
                    checkbox = widget.findChild(QCheckBox)
                    if checkbox and checkbox.isChecked():
                        # Cập nhật UI cho thiết bị này
                        status_item = self.ui.tableWidget.item(row, 5)
                        if status_item:
                            # Lưu trạng thái hiện tại
                            current_text = status_item.text()
                            # Đặt lại trạng thái để kích hoạt cập nhật UI
                            status_item.setText(current_text + " ")
                            QApplication.processEvents()
                            # Đặt lại trạng thái ban đầu
                            status_item.setText(current_text)
                            QApplication.processEvents()

    def update_password_from_table(self, item):
        """
        Cập nhật mật khẩu từ bảng khi có thay đổi.
        :param item: Item đã thay đổi.
        """
        # Chỉ xử lý khi thay đổi ở cột Pass (cột 2)
        if item.column() == 2:
            row = item.row()
            password = item.text().strip()

            # Lấy serial từ cột Devices (cột 1)
            serial_item = self.ui.tableWidget.item(row, 1)
            if serial_item:
                serial = serial_item.text().strip()
                # Cập nhật mật khẩu vào password_manager
                password_manager.set_password(serial, password, row)
                self.log_message(f"✅ Đã cập nhật mật khẩu cho thiết bị {serial} (dòng {row+1})")

    def update_checkbox_all_icon(self):
        """
        Cập nhật icon của checkBoxALL theo trạng thái hiện tại.
        """
        if hasattr(self.ui, 'checkBoxALL'):
            from PyQt5 import QtGui
            icon = QtGui.QIcon()
            if self.ui.checkBoxALL.isChecked():
                # Trạng thái checked - hiển thị icon checkbox_on
                icon.addPixmap(QtGui.QPixmap("icon/checkbox_on.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
                icon.addPixmap(QtGui.QPixmap("icon/checkbox_on.png"), QtGui.QIcon.Normal, QtGui.QIcon.On)
            else:
                # Trạng thái unchecked - hiển thị icon checkbox_off
                icon.addPixmap(QtGui.QPixmap("icon/checkbox_off.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
                icon.addPixmap(QtGui.QPixmap("icon/checkbox_off.png"), QtGui.QIcon.Normal, QtGui.QIcon.On)
            self.ui.checkBoxALL.setIcon(icon)

    def toggle_all_checkboxes(self, state):
        """
        Chọn hoặc bỏ chọn tất cả checkbox trong cột đầu tiên của bảng.
        :param state: Trạng thái của checkbox "Select All".
        """
        for row in range(self.ui.tableWidget.rowCount()):
            widget = self.ui.tableWidget.cellWidget(row, 0)
            if isinstance(widget, QWidget):
                checkbox = widget.findChild(QCheckBox)
                if checkbox:
                    checkbox.setChecked(state == Qt.Checked)

    def send_column_to_device(self, column_index, is_totp=False):
        """
        Gửi dữ liệu từ một cột trong bảng tới thiết bị đã chọn.
        :param column_index: Chỉ số cột cần gửi.
        :param is_totp: Nếu True, dữ liệu là mã TOTP và cần được xử lý bằng pyotp.
        """
        with concurrent.futures.ThreadPoolExecutor(max_workers=15) as executor:
            for row in range(self.ui.tableWidget.rowCount()):
                widget = self.ui.tableWidget.cellWidget(row, 0)
                if widget:
                    checkbox = widget.findChild(QCheckBox)
                    if checkbox and checkbox.isChecked():
                        serial_item = self.ui.tableWidget.item(row, 1)
                        data_item = self.ui.tableWidget.item(row, column_index)

                        if serial_item and data_item:
                            serial = serial_item.text().strip()
                            raw_value = data_item.text().strip()

                            if is_totp:
                                try:
                                    clean_secret = raw_value.replace(" ", "").replace("-", "").upper()
                                    if not clean_secret:
                                        raise ValueError("Chuỗi OTP trống")
                                    if not all(c in 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567' for c in clean_secret):
                                        raise ValueError("Sai định dạng Base32")
                                    padded_secret = clean_secret + ('=' * ((8 - len(clean_secret) % 8) % 8))
                                    totp = pyotp.TOTP(padded_secret)
                                    text = totp.now()
                                    print(f"✅ OTP: {text} - dòng {row+1}")
                                except Exception as e:
                                    error_msg = f"Lỗi TOTP: {str(e)}"
                                    print(f"⚠️ {error_msg} - dòng {row+1}")
                                    if self.ui.tableWidget.item(row, 5):
                                        self.ui.tableWidget.item(row, 5).setText(error_msg)
                                    continue
                            else:
                                text = raw_value

                            if text:
                                executor.submit(self.send_to_device, row, serial, text)

    def send_to_device(self, row, serial, text):
        """
        Gửi văn bản đến thiết bị.
        """
        status_item = self.ui.tableWidget.item(row, 5)
        try:
            if status_item:
                self.queue_status_update(row, "Đang xử lý...")

            # Xử lý văn bản có chứa ký tự đặc biệt
            special_chars = ['|', '&', '<', '>', '(', ')', '[', ']', '{', '}', '^', ';', '"', "'", '`', '$', '\\']

            if '|' in text:
                # Phương pháp đặc biệt cho văn bản chứa ký tự pipe (|)
                # Chia văn bản thành các phần tại vị trí ký tự |
                parts = text.split('|')

                # Gửi phần đầu tiên
                if parts[0]:
                    text_for_adb = parts[0].replace(' ', '%s')
                    subprocess.run([ADB_PATH, "-s", serial, "shell", "input", "text", text_for_adb], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                    time.sleep(random.uniform(0.5, 0.8))  # Ngẫu nhiên từ 0.5 đến 1.5 giây

                # Gửi các phần còn lại, mỗi phần được bắt đầu bằng ký tự |
                for part in parts[1:]:
                    # Gửi ký tự |
                    # Sử dụng đường dẫn ADB đã tìm thấy
                    adb_cmd = f'"{ADB_PATH}" -s {serial} shell "input text \\|"'
                    subprocess.run(adb_cmd, shell=True, check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                    time.sleep(random.uniform(0.5, 0.8))  # Ngẫu nhiên từ 0.1 đến 0.5 giây

                    # Gửi phần văn bản sau ký tự |
                    if part:
                        text_for_adb = part.replace(' ', '%s')
                        subprocess.run([ADB_PATH, "-s", serial, "shell", "input", "text", text_for_adb], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                        time.sleep(random.uniform(0.3, 1.0))  # Ngẫu nhiên từ 0.01 đến 1.0 giây

            elif any(char in text for char in special_chars):
                # Nếu văn bản chứa các ký tự đặc biệt khác, sử dụng phương pháp chia nhỏ
                # Chia văn bản thành các phần: phần thông thường và phần đặc biệt
                current_part = ""
                for char in text:
                    if char in special_chars:
                        # Gửi phần thông thường đã tích lũy
                        if current_part:
                            text_for_adb = current_part.replace(' ', '%s')
                            subprocess.run([ADB_PATH, "-s", serial, "shell", "input", "text", text_for_adb], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                            current_part = ""

                        # Gửi ký tự đặc biệt
                        # Sử dụng đường dẫn ADB đã tìm thấy
                        adb_cmd = f'"{ADB_PATH}" -s {serial} shell "input text \\{char}"'
                        subprocess.run(adb_cmd, shell=True, check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                    else:
                        current_part += char

                # Gửi phần thông thường cuối cùng nếu có
                if current_part:
                    text_for_adb = current_part.replace(' ', '%s')
                    subprocess.run([ADB_PATH, "-s", serial, "shell", "input", "text", text_for_adb], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                    time.sleep(random.uniform(0.2, 1.0))  # Ngẫu nhiên từ 0.01 đến 1.0 giây

            else:
                # Nếu văn bản không chứa ký tự đặc biệt, gửi toàn bộ văn bản một lần
                # Thay thế khoảng trắng bằng %s cho ADB
                text_for_adb = text.replace(' ', '%s')
                subprocess.run([ADB_PATH, "-s", serial, "shell", "input", "text", text_for_adb], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                time.sleep(random.uniform(0.3, 1.0))  # Ngẫu nhiên từ 0.01 đến 1.0 giây

            if self.ui.checkBox_enter.isChecked():
                subprocess.run([ADB_PATH, "-s", serial, "shell", "input", "keyevent", "66"], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

            if status_item:
                self.queue_status_update(row, "✅ Thành công")
                self.log_message(f"✅ Gửi thành công đến thiết bị {serial} (dòng {row + 1})")

        except FileNotFoundError:
            error_msg = f"Không tìm thấy ADB tại đường dẫn: {ADB_PATH}"
            if status_item:
                status_item.setText(f"❌ {error_msg}")
            self.log_message(f"❌ {error_msg} - Thiết bị {serial} (dòng {row + 1})")
        except Exception as e:
            error_msg = f"Lỗi: {str(e)}"
            if status_item:
                status_item.setText(f"❌ {error_msg}")
            self.log_message(f"❌ {error_msg} - Thiết bị {serial} (dòng {row + 1})")

    def get_selected_devices(self):
        """
        Lấy danh sách các thiết bị đã được chọn.
        :return: List of tuples (row, serial)
        """
        selected_devices = []
        for row in range(self.ui.tableWidget.rowCount()):
            widget = self.ui.tableWidget.cellWidget(row, 0)
            if widget:
                checkbox = widget.findChild(QCheckBox)
                if checkbox and checkbox.isChecked():
                    serial_item = self.ui.tableWidget.item(row, 1)
                    if serial_item:
                        selected_devices.append((row, serial_item.text().strip()))
        return selected_devices

    def go_to_home(self):
        """
        Gửi phím Home đến các thiết bị đã chọn để trở về màn hình chính.
        """
        selected_devices = []

        for row in range(self.ui.tableWidget.rowCount()):
            widget = self.ui.tableWidget.cellWidget(row, 0)
            if widget:
                checkbox = widget.findChild(QCheckBox)
                if checkbox and checkbox.isChecked():
                    serial_item = self.ui.tableWidget.item(row, 1)
                    if serial_item:
                        selected_devices.append((row, serial_item.text().strip()))

        if not selected_devices:
            QMessageBox.warning(self, "Cảnh báo", "Vui lòng chọn ít nhất một thiết bị!")
            return

        # Gửi phím Home cho từng thiết bị được chọn
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            for row, device in selected_devices:
                executor.submit(self.press_home_key, row, device)

    def press_home_key(self, row, serial):
        """
        Gửi phím Home đến thiết bị chỉ định.
        :param row: Dòng trong bảng.
        :param serial: Số serial của thiết bị.
        """
        status_item = self.ui.tableWidget.item(row, 5)
        try:
            # Cập nhật trạng thái đang xử lý
            if status_item:
                status_item.setText("Đang về Home...")
                QApplication.processEvents()

            # Gửi phím Home (keycode 3) sử dụng đường dẫn ADB đã tìm thấy
            subprocess.run([ADB_PATH, "-s", serial, "shell", "input", "keyevent", "3"],
                          check=True,
                          creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == 'win32' else 0)

            if status_item:
                status_item.setText("✅ Đã về Home")
                self.log_message(f"✅ Đã gửi phím Home đến thiết bị {serial} (dòng {row+1})")

        except FileNotFoundError:
            error_msg = f"Không tìm thấy ADB tại đường dẫn: {ADB_PATH}"
            self.log_message(f"❌ {error_msg} - dòng {row+1}")
            if status_item:
                status_item.setText(f"❌ {error_msg}")
        except subprocess.CalledProcessError as e:
            error_msg = f"Lỗi ADB: {e.stderr.decode() if e.stderr else str(e)}"
            self.log_message(f"❌ {error_msg} - dòng {row+1}")
            if status_item:
                status_item.setText(f"❌ {error_msg}")
        except Exception as e:
            error_msg = f"Lỗi: {str(e)}"
            self.log_message(f"❌ {error_msg} - dòng {row+1}")
            if status_item:
                status_item.setText(f"❌ {error_msg}")


    # Tải danh sách thiết bị ADB

    def load_adb_devices(self): # Tải danh sách thiết bị ADB
        """
        Tải danh sách thiết bị ADB và hiển thị lên bảng.
        """
        try:
            # Sử dụng đường dẫn ADB đã tìm thấy
            result = subprocess.run([ADB_PATH, "devices"], capture_output=True, text=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

            # Kiểm tra xem daemon có đang chạy không
            if result.stderr and "daemon not running" in result.stderr:
                self.log_message("ADB daemon chưa chạy, đang khởi động...")
                subprocess.run([ADB_PATH, "start-server"], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                result = subprocess.run([ADB_PATH, "devices"], capture_output=True, text=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

            # Kiểm tra kết quả
            if result.returncode != 0:
                error_msg = f"Lỗi khi chạy ADB: {result.stderr}"
                self.log_message(f"❌ {error_msg}")
                QMessageBox.critical(self, "Lỗi ADB", error_msg)
                return

            lines = result.stdout.strip().split("\n")[1:]
            if not lines or all(not line.strip() for line in lines):
                QMessageBox.warning(self, "Cảnh báo", "Không có thiết bị nào được kết nối!")
                return

            self.ui.tableWidget.setRowCount(0)

            for i, line in enumerate(lines):
                if not line.strip():
                    continue
                parts = line.split()
                if len(parts) == 2:
                    serial, status = parts
                    self.ui.tableWidget.insertRow(i)

                    # Cột 0: Checkbox
                    checkbox = QCheckBox()
                    checkbox_widget = QWidget()
                    layout = QHBoxLayout(checkbox_widget)
                    layout.addWidget(checkbox)
                    layout.setAlignment(Qt.AlignCenter)
                    layout.setContentsMargins(0, 0, 0, 0)
                    self.ui.tableWidget.setCellWidget(i, 0, checkbox_widget)

                    # Cột 2: Serial
                    self.ui.tableWidget.setItem(i, 1, QTableWidgetItem(serial))

                    # Cột 2,3,4: Pass, Authen (TOTP secret), Data
                    self.ui.tableWidget.setItem(i, 2, QTableWidgetItem())  # Pass
                    self.ui.tableWidget.setItem(i, 3, QTableWidgetItem())  # Authen
                    self.ui.tableWidget.setItem(i, 4, QTableWidgetItem())  # Data

                    # Cột 6: Trạng thái
                    self.ui.tableWidget.setItem(i, 5, QTableWidgetItem(status))

            # Hiển thị thông báo thành công
            self.log_message(f"✅ Đã tìm thấy {len(lines)} thiết bị kết nối")

        except FileNotFoundError:
            error_msg = f"Không tìm thấy ADB tại đường dẫn: {ADB_PATH}"
            self.log_message(f"❌ {error_msg}")

            # Hiển thị thông báo lỗi chi tiết
            QMessageBox.critical(self, "Lỗi ADB",
                f"{error_msg}\n\n"
                "Nguyên nhân có thể là:\n"
                "1. Thư mục platform-tools không được bao gồm trong gói đóng gói\n"
                "2. ADB không được cài đặt trên máy tính\n\n"
                "Giải pháp:\n"
                "1. Đảm bảo thư mục platform-tools được bao gồm trong gói đóng gói\n"
                "2. Cài đặt ADB từ https://developer.android.com/studio/releases/platform-tools"
            )
        except Exception as e:
            error_msg = f"Lỗi khi lấy danh sách thiết bị ADB: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            QMessageBox.critical(self, "Lỗi", error_msg)

    def keyPressEvent(self, event):
        """
        Xử lý sự kiện bàn phím, cho phép dán nội dung clipboard vào bảng bằng Ctrl+V.
        :param event: Sự kiện bàn phím.
        """
        if event.matches(QKeySequence.Paste):
            clipboard = QGuiApplication.clipboard()
            data = clipboard.text()
            self.paste_to_table(data)
        else:
            super().keyPressEvent(event)

    def paste_to_table(self, clipboard_text):
        """
        Dán dữ liệu từ clipboard vào bảng.
        :param clipboard_text: Dữ liệu từ clipboard (dạng chuỗi, các cột cách nhau bằng tab).
        """
        selected = self.ui.tableWidget.selectedIndexes()
        if not selected:
            return

        start_row = selected[0].row()
        start_col = selected[0].column()

        rows = clipboard_text.strip().split("\n")
        for i, row_data in enumerate(rows):
            columns = row_data.split("\t")
            for j, cell in enumerate(columns):
                row = start_row + i
                col = start_col + j
                if row < self.ui.tableWidget.rowCount() and col < self.ui.tableWidget.columnCount():
                    item = QTableWidgetItem(cell)
                    self.ui.tableWidget.setItem(row, col, item)

    # HÀM KHÔNG SỬ DỤNG - CÓ THỂ XÓA
    # def export_logs(self):
    #     """
    #     Xuất nội dung nhật ký từ tab Logs ra file.
    #     """
    #     file_name, _ = QFileDialog.getSaveFileName(self, "Lưu Logs", "", "Text Files (*.txt);;All Files (*)")
    #     if file_name:
    #         try:
    #             with open(file_name, 'w', encoding='utf-8') as file:
    #                 file.write(self.ui.textEdit_logs.toPlainText())
    #             # Log thông báo lưu logs (không hiển thị message box)
    #             self.log_message("✅ Logs đã được lưu thành công!")
    #         except Exception as e:
    #             QMessageBox.critical(self, "Lỗi", f"Không thể lưu logs: {str(e)}")

    def queue_status_update(self, row, text):
        """
        Queue status update để tránh UI freeze
        """
        self.ui_update_queue.append(('status', row, text))

    def process_ui_updates(self):
        """
        Process queued UI updates để tránh blocking
        """
        if not self.ui_update_queue:
            return

        # Process up to 5 updates per timer tick để tránh overload
        updates_to_process = min(5, len(self.ui_update_queue))

        for _ in range(updates_to_process):
            if not self.ui_update_queue:
                break

            update = self.ui_update_queue.pop(0)
            update_type, row, text = update

            if update_type == 'status':
                try:
                    status_item = self.ui.tableWidget.item(row, 5)
                    if status_item:
                        status_item.setText(text)
                except Exception as e:
                    print(f"❌ Lỗi cập nhật UI: {e}")

    def log_message(self, message):
        """
        Ghi thông báo vào console.
        """
        # Thêm timestamp và in ra console
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        print(log_entry)

    def setup_button_tooltips(self):
        """
        Thiết lập tooltip (textbox hiển thị khi rê chuột) cho các button
        """
        try:
            # Tooltip cho các button chính
            if hasattr(self.ui, 'changer'):
                self.ui.changer.setToolTip("🔄 Change Info\n\nThay đổi thông tin thiết bị.\n.")

            if hasattr(self.ui, 'restore'):
                self.ui.restore.setToolTip("📧 Restore Email\n.")

            if hasattr(self.ui, 'pushButton_Mua'):
                self.ui.pushButton_Mua.setToolTip("💰 Mua Game\n\nThực hiện mua game.\n.")

            if hasattr(self.ui, 'close_payment_Button'):
                self.ui.close_payment_Button.setToolTip("🔒 Đóng Hồ Sơ\n\nĐóng hồ sơ thanh toán trên Google.\nXóa thông tin thanh toán.")

            if hasattr(self.ui, 'xoa_the_close_Button'):
                self.ui.xoa_the_close_Button.setToolTip("🗑️ Xóa Thẻ Close\n\nXóa thẻ thanh toán.\nXoá thẻ close, thẻ ver....")

            if hasattr(self.ui, 'add_card_Button'):
                self.ui.add_card_Button.setToolTip("📱 Add Ads\n\nThêm thẻ Google Ads.\n....")

            if hasattr(self.ui, 'toolButton_wallet'):
                self.ui.toolButton_wallet.setToolTip("💳 Add Wallet\n\nThêm thẻ Google Wallet.\n...")

            if hasattr(self.ui, 'chplay'):
                self.ui.chplay.setToolTip("🎮 ChPlay Login\n\nĐăng nhập Google Play.\nHỗ trợ xử lý kháng email.")

            if hasattr(self.ui, 'backup'):
                self.ui.backup.setToolTip("💾 Backup\n\nSao lưu dữ liệu thiết bị")

            self.log_message("✅ Đã thiết lập tooltip cho các button chính")

        except Exception as e:
            self.log_message(f"❌ Lỗi khi thiết lập tooltip: {e}")

    def setup_shortcuts(self):
        """Thiết lập phím tắt cho các chức năng"""
        try:
            from PyQt5.QtWidgets import QShortcut
            from PyQt5.QtGui import QKeySequence

            # Ctrl+B: Backup thiết bị đã chọn
            self.backup_shortcut = QShortcut(QKeySequence("Ctrl+B"), self)
            self.backup_shortcut.activated.connect(self.backup_button_clicked)

            self.log_message("✅ Đã thiết lập phím tắt: Ctrl+B (Backup)")

        except Exception as e:
            self.log_message(f"❌ Lỗi khi thiết lập phím tắt: {e}")

    def adjust_ui_for_dpi(self):
        """
        Điều chỉnh giao diện dựa trên DPI của hệ thống để đảm bảo hiển thị đúng trên các màn hình khác nhau.
        """
        # Lấy thông tin về màn hình hiện tại
        screen = QGuiApplication.primaryScreen()
        dpi = screen.logicalDotsPerInch()

        # Ghi log thông tin DPI
        self.log_message(f"Thông tin màn hình: DPI = {dpi}")

        # Điều chỉnh font chữ cho các button dựa trên DPI
        buttons = [
            self.ui.add_card_Button, self.ui.xoa_the_close_Button,
            self.ui.close_payment_Button, self.ui.paste_pass_Button,
            self.ui.view_Button, self.ui.pase_data_Button,
            self.ui.pase_authen_Button, self.ui.home_Button,
            self.ui.start_stop_Button
        ]

        # Điều chỉnh kích thước font và padding cho các button
        for button in buttons:
            if button is not None:
                # Lấy stylesheet hiện tại
                current_style = button.styleSheet()

                # Điều chỉnh padding dựa trên DPI
                if dpi > 120:  # Màn hình DPI cao
                    # Giảm padding để có thêm không gian cho chữ
                    new_style = current_style.replace("padding: 6px 12px;", "padding: 2px 6px;")
                    # Đặt font chữ nhỏ hơn
                    font = button.font()
                    font.setPointSize(7)  # Giảm kích thước font
                    button.setFont(font)
                else:
                    # Đối với màn hình DPI thấp, giữ nguyên padding
                    new_style = current_style

                # Áp dụng stylesheet mới
                button.setStyleSheet(new_style)

        # Điều chỉnh kích thước font cho các label nếu cần
        labels = [
            self.ui.label_errro, self.ui.label_suscess, self.ui.label_da_xu_ly
        ]

        for label in labels:
            if label is not None:
                font = label.font()
                if dpi > 120:  # Màn hình DPI cao
                    font.setPointSize(7)
                else:
                    font.setPointSize(8)
                label.setFont(font)

        # Ghi log hoàn thành điều chỉnh
        self.log_message("Đã điều chỉnh giao diện theo DPI của hệ thống")

    def load_slider_value(self):
        """
        Tải giá trị kích thước màn hình đã lưu từ file cấu hình.
        Trả về None nếu không có giá trị nào được lưu.
        """
        try:
            # Đảm bảo thư mục data tồn tại
            if not os.path.exists("data"):
                os.makedirs("data")

            # Đường dẫn đến file cấu hình
            config_file = os.path.join("data", "config.txt")

            # Kiểm tra xem file cấu hình có tồn tại không
            if not os.path.exists(config_file):
                return None

            # Đọc file cấu hình
            with open(config_file, "r") as file:
                for line in file:
                    if line.startswith("screen_width="):
                        width_str = line.strip().split("=")[1]
                        try:
                            width = int(width_str)
                            # Đảm bảo giá trị nằm trong khoảng hợp lệ
                            if 200 <= width <= 518:
                                return width
                        except ValueError:
                            pass

            return None
        except Exception as e:
            self.log_message(f"Lỗi khi tải giá trị kích thước màn hình: {str(e)}")
            return None

    def save_slider_value(self, width):
        """
        Lưu giá trị kích thước màn hình vào file cấu hình.
        """
        try:
            # Đảm bảo thư mục data tồn tại
            if not os.path.exists("data"):
                os.makedirs("data")

            # Đường dẫn đến file cấu hình
            config_file = os.path.join("data", "config.txt")

            # Đọc file cấu hình hiện tại (nếu có)
            config_lines = []
            if os.path.exists(config_file):
                with open(config_file, "r") as file:
                    config_lines = file.readlines()

            # Tìm và cập nhật dòng screen_width nếu có
            found = False
            for i, line in enumerate(config_lines):
                if line.startswith("screen_width="):
                    config_lines[i] = f"screen_width={width}\n"
                    found = True
                    break

            # Nếu không tìm thấy, thêm dòng mới
            if not found:
                config_lines.append(f"screen_width={width}\n")

            # Ghi lại file cấu hình
            with open(config_file, "w") as file:
                file.writelines(config_lines)

            self.log_message(f"Đã lưu giá trị kích thước màn hình: {width}")
        except Exception as e:
            self.log_message(f"Lỗi khi lưu giá trị kích thước màn hình: {str(e)}")

    def save_spinbox_values(self):
        """
        Lưu giá trị của các spinbox goi1, goi2, goi3 và spinBox_so_lan_add_card vào file cấu hình.
        """
        try:
            # Đảm bảo thư mục data tồn tại
            if not os.path.exists("data"):
                os.makedirs("data")

            # Đường dẫn đến file cấu hình
            config_file = os.path.join("data", "config.txt")

            # Lấy giá trị hiện tại của các spinbox
            goi1_value = self.ui.goi1.value() if hasattr(self.ui, 'goi1') else 0
            goi2_value = self.ui.goi2.value() if hasattr(self.ui, 'goi2') else 0
            goi3_value = self.ui.goi3.value() if hasattr(self.ui, 'goi3') else 0

            # Lấy giá trị của spinBox_so_lan_add_card nếu có
            add_card_value = 0
            if hasattr(self.ui, 'spinBox_so_lan_add_card'):
                add_card_value = self.ui.spinBox_so_lan_add_card.value()

            # Lấy giá trị của spinBox_view nếu có
            view_per_row_value = 5  # Giá trị mặc định
            if hasattr(self.ui, 'spinBox_view'):
                view_per_row_value = self.ui.spinBox_view.value()

            # Lấy giá trị của spinBox_error nếu có
            max_failures_value = 10  # Giá trị mặc định
            if hasattr(self.ui, 'spinBox_error'):
                max_failures_value = self.ui.spinBox_error.value()

            # Đọc file cấu hình hiện tại (nếu có)
            config_lines = []
            if os.path.exists(config_file):
                with open(config_file, "r") as file:
                    config_lines = file.readlines()

            # Tìm và cập nhật các dòng goi1, goi2, goi3, add_card_count, view_per_row, max_failures nếu có
            found_goi1 = found_goi2 = found_goi3 = found_add_card = found_view_per_row = found_max_failures = False
            for i, line in enumerate(config_lines):
                if line.startswith("goi1="):
                    config_lines[i] = f"goi1={goi1_value}\n"
                    found_goi1 = True
                elif line.startswith("goi2="):
                    config_lines[i] = f"goi2={goi2_value}\n"
                    found_goi2 = True
                elif line.startswith("goi3="):
                    config_lines[i] = f"goi3={goi3_value}\n"
                    found_goi3 = True
                elif line.startswith("add_card_count="):
                    config_lines[i] = f"add_card_count={add_card_value}\n"
                    found_add_card = True
                elif line.startswith("view_per_row="):
                    config_lines[i] = f"view_per_row={view_per_row_value}\n"
                    found_view_per_row = True
                elif line.startswith("max_failures="):
                    config_lines[i] = f"max_failures={max_failures_value}\n"
                    found_max_failures = True

            # Nếu không tìm thấy, thêm dòng mới
            if not found_goi1 and hasattr(self.ui, 'goi1'):
                config_lines.append(f"goi1={goi1_value}\n")
            if not found_goi2 and hasattr(self.ui, 'goi2'):
                config_lines.append(f"goi2={goi2_value}\n")
            if not found_goi3 and hasattr(self.ui, 'goi3'):
                config_lines.append(f"goi3={goi3_value}\n")
            if not found_add_card and hasattr(self.ui, 'spinBox_so_lan_add_card'):
                config_lines.append(f"add_card_count={add_card_value}\n")
            if not found_view_per_row and hasattr(self.ui, 'spinBox_view'):
                config_lines.append(f"view_per_row={view_per_row_value}\n")
            if not found_max_failures and hasattr(self.ui, 'spinBox_error'):
                config_lines.append(f"max_failures={max_failures_value}\n")

            # Ghi lại file cấu hình
            with open(config_file, "w") as file:
                file.writelines(config_lines)

            # Log thông tin đã lưu
            log_message = "Đã lưu giá trị: "
            if hasattr(self.ui, 'goi1') and hasattr(self.ui, 'goi2') and hasattr(self.ui, 'goi3'):
                log_message += f"Gói 1={goi1_value}, Gói 2={goi2_value}, Gói 3={goi3_value}"
            if hasattr(self.ui, 'spinBox_so_lan_add_card'):
                if "Gói" in log_message:
                    log_message += f", Số lần add card={add_card_value}"
                else:
                    log_message += f"Số lần add card={add_card_value}"
            if hasattr(self.ui, 'spinBox_error'):
                if "Gói" in log_message or "Số lần add card" in log_message:
                    log_message += f", Giới hạn lỗi={max_failures_value}"
                else:
                    log_message += f"Giới hạn lỗi={max_failures_value}"

            self.log_message(log_message)
            return True
        except Exception as e:
            self.log_message(f"Lỗi khi lưu giá trị spinbox: {str(e)}")
            return False

    def load_spinbox_values(self):
        """
        Tải giá trị của các spinbox goi1, goi2, goi3 và spinBox_so_lan_add_card từ file cấu hình.
        Trả về tuple (goi1_value, goi2_value, goi3_value) nếu thành công, None nếu thất bại.
        Đồng thời, nếu có giá trị add_card_count, sẽ tự động cập nhật spinBox_so_lan_add_card.
        """
        try:
            # Đảm bảo thư mục data tồn tại
            if not os.path.exists("data"):
                return None

            # Đường dẫn đến file cấu hình
            config_file = os.path.join("data", "config.txt")

            # Kiểm tra xem file cấu hình có tồn tại không
            if not os.path.exists(config_file):
                return None

            # Đọc file cấu hình
            goi1_value = goi2_value = goi3_value = None
            add_card_value = None
            with open(config_file, "r") as file:
                for line in file:
                    if line.startswith("goi1="):
                        try:
                            goi1_value = int(line.strip().split("=")[1])
                        except (ValueError, IndexError):
                            pass
                    elif line.startswith("goi2="):
                        try:
                            goi2_value = int(line.strip().split("=")[1])
                        except (ValueError, IndexError):
                            pass
                    elif line.startswith("goi3="):
                        try:
                            goi3_value = int(line.strip().split("=")[1])
                        except (ValueError, IndexError):
                            pass
                    elif line.startswith("add_card_count="):
                        try:
                            add_card_value = int(line.strip().split("=")[1])
                        except (ValueError, IndexError):
                            pass

            # Nếu tìm thấy giá trị add_card_count, cập nhật spinBox_so_lan_add_card
            if add_card_value is not None and hasattr(self.ui, 'spinBox_so_lan_add_card'):
                self.ui.spinBox_so_lan_add_card.setValue(add_card_value)
                self.log_message(f"Đã tải giá trị số lần add card: {add_card_value}")

            # Nếu tìm thấy giá trị max_failures, cập nhật spinBox_error
            max_failures_value = self.load_max_failures_value()
            if max_failures_value is not None and hasattr(self.ui, 'spinBox_error'):
                self.ui.spinBox_error.setValue(max_failures_value)
                self.log_message(f"Đã tải giá trị giới hạn lỗi: {max_failures_value}")



            # Nếu tìm thấy tất cả các giá trị gói, trả về tuple
            if goi1_value is not None and goi2_value is not None and goi3_value is not None:
                return (goi1_value, goi2_value, goi3_value)
            return None
        except Exception as e:
            self.log_message(f"Lỗi khi tải giá trị spinbox: {str(e)}")
            return None

    def load_view_per_row_value(self):
        """
        Tải giá trị view_per_row từ file config.
        :return: Giá trị view_per_row hoặc None nếu không tìm thấy.
        """
        config_file = "data/config.txt"
        if not os.path.exists(config_file):
            return None

        try:
            with open(config_file, "r") as file:
                for line in file:
                    if line.startswith("view_per_row="):
                        try:
                            view_per_row_value = int(line.strip().split("=")[1])
                            return view_per_row_value
                        except (ValueError, IndexError):
                            pass
            return None

        except Exception as e:
            self.log_message(f"❌ Lỗi khi tải giá trị view_per_row: {str(e)}")
            return None

    def load_max_failures_value(self):
        """
        Tải giá trị max_failures từ file cấu hình.
        Trả về giá trị max_failures nếu thành công, None nếu thất bại.
        """
        try:
            # Đảm bảo thư mục data tồn tại
            if not os.path.exists("data"):
                return None

            # Đường dẫn đến file cấu hình
            config_file = os.path.join("data", "config.txt")

            if not os.path.exists(config_file):
                return None

            # Đọc file cấu hình
            with open(config_file, "r") as file:
                lines = file.readlines()

            # Tìm giá trị max_failures
            for line in lines:
                if line.startswith("max_failures="):
                    try:
                        max_failures_value = int(line.split("=")[1].strip())
                        return max_failures_value
                    except (ValueError, IndexError):
                        pass

            return None
        except Exception as e:
            self.log_message(f"Lỗi khi tải giá trị max_failures: {str(e)}")
            return None

    def update_slider_value(self, value):
        """
        Cập nhật hiển thị giá trị của thanh trượt kích thước màn hình.
        """
        # Tính toán chiều rộng dựa trên giá trị thanh trượt (0-100)
        # Phạm vi chiều rộng từ 200 đến 518 pixel
        min_width = 200
        max_width = 518
        width_range = max_width - min_width
        width = min_width + int((value / 100.0) * width_range)

        # Tính toán chiều cao tương ứng để giữ tỷ lệ khung hình
        # Tỷ lệ khung hình gốc là 9:16 (như 518:921)
        aspect_ratio = 16.0 / 9.0
        height = int(width * aspect_ratio)

        # Cập nhật hiển thị giá trị
        if hasattr(self.ui, 'wide') and hasattr(self.ui, 'high'):
            # Cập nhật hiển thị
            self.ui.wide.setText(str(width))
            self.ui.high.setText(str(height))

            # Cập nhật nhãn kích thước hiển thị
            if hasattr(self.ui, 'label_6'):
                self.ui.label_6.setText(f"Kích thước hiển thị ({width}x{height})")

        # Chỉ lưu giá trị khi không đang khởi tạo
        if not hasattr(self, '_initializing_slider') or not self._initializing_slider:
            self.save_slider_value(width)

    def update_slider_value_without_save(self, value):
        """
        Cập nhật hiển thị giá trị của thanh trượt kích thước màn hình mà không lưu vào file.
        Dùng khi khởi tạo để tránh ghi đè giá trị đã tải.
        """
        # Tính toán chiều rộng dựa trên giá trị thanh trượt (0-100)
        # Phạm vi chiều rộng từ 200 đến 518 pixel
        min_width = 200
        max_width = 518
        width_range = max_width - min_width
        width = min_width + int((value / 100.0) * width_range)

        # Tính toán chiều cao tương ứng để giữ tỷ lệ khung hình
        # Tỷ lệ khung hình gốc là 9:16 (như 518:921)
        aspect_ratio = 16.0 / 9.0
        height = int(width * aspect_ratio)

        # Cập nhật hiển thị giá trị
        if hasattr(self.ui, 'wide') and hasattr(self.ui, 'high'):
            # Cập nhật hiển thị
            self.ui.wide.setText(str(width))
            self.ui.high.setText(str(height))

            # Cập nhật nhãn kích thước hiển thị
            if hasattr(self.ui, 'label_6'):
                self.ui.label_6.setText(f"Kích thước hiển thị ({width}x{height})")

        # Lưu kích thước mặc định để sử dụng sau này
        self.default_scrcpy_width = width
        aspect_ratio = 16.0 / 9.0
        self.default_scrcpy_height = int(width * aspect_ratio)

    def toggle_wifi(self):
        """
        Bật/tắt wifi trên các thiết bị đã chọn.
        Sử dụng lệnh ADB để điều khiển wifi trên thiết bị Android.
        """
        # Lấy danh sách thiết bị đã chọn
        selected_devices = []
        for row in range(self.ui.tableWidget.rowCount()):
            widget = self.ui.tableWidget.cellWidget(row, 0)
            if widget:
                checkbox = widget.findChild(QCheckBox)
                if checkbox and checkbox.isChecked():
                    serial_item = self.ui.tableWidget.item(row, 1)
                    if serial_item:
                        selected_devices.append((row, serial_item.text().strip()))

        if not selected_devices:
            QMessageBox.warning(self, "Cảnh báo", "Vui lòng chọn ít nhất một thiết bị!")
            # Đặt lại trạng thái nút nếu không có thiết bị nào được chọn
            self.ui.button_wifi.setChecked(not self.ui.button_wifi.isChecked())
            return

        # Xác định trạng thái wifi (bật hoặc tắt) dựa trên trạng thái nút
        wifi_enabled = self.ui.button_wifi.isChecked()

        # Hiển thị thông báo về hành động đang thực hiện
        action_msg = "BẬT" if wifi_enabled else "TẮT"
        self.log_message(f"🔄 Đang {action_msg} wifi trên {len(selected_devices)} thiết bị đã chọn...")

        # Thực hiện bật/tắt wifi trên từng thiết bị đã chọn
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = []
            for row, serial in selected_devices:
                futures.append(executor.submit(self.set_wifi_state, row, serial, wifi_enabled))

            # Chờ tất cả các tác vụ hoàn thành
            for future in concurrent.futures.as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    self.log_message(f"❌ Lỗi khi thay đổi trạng thái wifi: {str(e)}")

        # Hiển thị thông báo hoàn thành
        self.log_message(f"✅ Đã {action_msg} wifi trên {len(selected_devices)} thiết bị đã chọn")

    def set_wifi_state(self, row, serial, enable):
        """
        Đặt trạng thái wifi trên một thiết bị cụ thể.

        :param row: Dòng trong bảng.
        :param serial: Số serial của thiết bị.
        :param enable: True để bật wifi, False để tắt wifi.
        """
        status_item = self.ui.tableWidget.item(row, 5)
        action_msg = "BẬT" if enable else "TẮT"

        try:
            # Cập nhật trạng thái đang xử lý
            if status_item:
                status_item.setText(f"Đang {action_msg} wifi...")
                QApplication.processEvents()

            # Sử dụng lệnh ADB để bật/tắt wifi
            # Phương pháp 1: Sử dụng svc wifi
            wifi_state = "enable" if enable else "disable"
            subprocess.run(
                [ADB_PATH, "-s", serial, "shell", "svc", "wifi", wifi_state],
                check=True,
                creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
            )

            # Đợi một chút để đảm bảo lệnh được thực hiện
            time.sleep(1)

            # Kiểm tra xem wifi đã thực sự được bật/tắt chưa
            result = subprocess.run(
                [ADB_PATH, "-s", serial, "shell", "settings", "get", "global", "wifi_on"],
                capture_output=True, text=True,
                creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
            )

            # Phân tích kết quả
            current_state = result.stdout.strip()
            expected_state = "1" if enable else "0"

            if current_state == expected_state:
                if status_item:
                    status_item.setText(f"✅ Đã {action_msg} wifi")
                self.log_message(f"✅ Đã {action_msg} wifi trên thiết bị {serial} (dòng {row+1})")
            else:
                # Thử phương pháp 2 nếu phương pháp 1 không thành công
                # Sử dụng settings put global
                subprocess.run(
                    [ADB_PATH, "-s", serial, "shell", "settings", "put", "global", "wifi_on", expected_state],
                    check=True,
                    creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
                )

                if status_item:
                    status_item.setText(f"✅ Đã {action_msg} wifi (phương pháp 2)")
                self.log_message(f"✅ Đã {action_msg} wifi trên thiết bị {serial} (dòng {row+1}) bằng phương pháp 2")

        except subprocess.CalledProcessError as e:
            error_msg = f"Lỗi khi {action_msg.lower()} wifi: {e.stderr.decode() if e.stderr else str(e)}"
            if status_item:
                status_item.setText(f"❌ {error_msg}")
            self.log_message(f"❌ {error_msg} - thiết bị {serial} (dòng {row+1})")

        except Exception as e:
            error_msg = f"Lỗi: {str(e)}"
            if status_item:
                status_item.setText(f"❌ {error_msg}")
            self.log_message(f"❌ {error_msg} - thiết bị {serial} (dòng {row+1})")



    """Các chuỗi hành động khác như xoá hồ sơ, thêm ads, thêm ví... có thể được định nghĩa ở đây"""

    def port_doma_changed(self, text):
        """
        Xử lý sự kiện khi giá trị port doma thay đổi.
        :param text: Giá trị mới của port doma.
        """
        try:
            # Kiểm tra xem giá trị có phải là số nguyên không
            port = int(text)
            # Kiểm tra xem port có nằm trong khoảng hợp lệ không (1-65535)
            if port < 1 or port > 65535:
                self.log_message(f"⚠️ Port doma không hợp lệ: {port}. Port phải nằm trong khoảng 1-65535.")
                # Đặt lại giá trị mặc định
                self.ui.lineEdit_port_doma.setText("60708")
            else:
                self.log_message(f"✅ Đã thiết lập port doma: {port}")
        except ValueError:
            # Nếu giá trị không phải là số nguyên
            if text:  # Chỉ hiển thị cảnh báo nếu trường không trống
                self.log_message(f"⚠️ Port doma không hợp lệ: {text}. Port phải là số nguyên.")
                # Đặt lại giá trị mặc định
                self.ui.lineEdit_port_doma.setText("60708")

    def restore_option_changed(self, index):
        """
        Xử lý sự kiện khi giá trị comboBox_restore thay đổi.
        :param index: Chỉ số mới của comboBox_restore.
        """
        if index == 0:  # Restore
            action_type = "restore"
            action_display = "Restore"
        elif index == 1:  # Changer
            action_type = "changeinfo"
            action_display = "Change info"
        else:
            return

        self.log_message(f"✅ Đã chọn chế độ: {action_display} (API: {action_type})")

    def start_actions(self):
        """
        Xử lý sự kiện khi nhấn nút Start/Stop.
        Thực hiện các hành động dựa trên các checkbox và combobox đã chọn.
        Có thể dừng bằng cách nhấn nút Start lần nữa.
        """
        # Kiểm tra xem có đang chạy không
        if self.is_start_actions_running:
            # Nếu đang chạy, dừng quá trình
            self.log_message("⏹️ Đang dừng quá trình start_actions...")
            self.stop_start_actions = True

            # Reset button về trạng thái bình thường
            self.ui.start_stop_Button.setChecked(False)
            self.ui.start_stop_Button.setText("Start")
            self.is_start_actions_running = False

            # Dừng tất cả các process con nếu có
            self.stop_all_sub_processes()

            self.log_message("✅ Đã dừng quá trình start_actions")
            return

        # Kiểm tra license cho tính năng auto functions
        app = QApplication.instance()
        allowed_features = app.property("allowed_features")

        # Chỉ khóa auto functions khi là ALL_BLOCKED
        # PROXY không khóa auto functions, chỉ khóa tab Proxy
        if allowed_features and allowed_features.upper() == "ALL_BLOCKED":
            QMessageBox.warning(self, "Tính năng bị giới hạn",
                               "Tất cả tính năng tự động đã bị khóa do giới hạn license.")
            self.log_message("❌ Tất cả tính năng tự động bị khóa do license ALL_BLOCKED")
            return

        # Kiểm tra xem có thiết bị nào được chọn không
        selected_devices = []
        for row in range(self.ui.tableWidget.rowCount()):
            widget = self.ui.tableWidget.cellWidget(row, 0)
            if widget:
                checkbox = widget.findChild(QCheckBox)
                if checkbox and checkbox.isChecked():
                    serial_item = self.ui.tableWidget.item(row, 1)
                    if serial_item:
                        selected_devices.append((row, serial_item.text().strip()))

        if not selected_devices:
            QMessageBox.warning(self, "Cảnh báo", "Vui lòng chọn ít nhất một thiết bị!")
            return

        # Lấy giá trị port doma
        port_doma = "60708"  # Giá trị mặc định
        if hasattr(self.ui, 'lineEdit_port_doma'):
            port_doma = self.ui.lineEdit_port_doma.text().strip()
            if not port_doma:
                port_doma = "60708"

        # Kiểm tra các checkbox và combobox theo thứ tự từ trên xuống dưới
        actions_to_perform = []

        # 1. Kiểm tra checkbox restore email (thứ tự đầu tiên)
        if hasattr(self.ui, 'checkBox_restore_email') and self.ui.checkBox_restore_email.isChecked():
            # Kiểm tra loại hành động (restore hoặc changeinfo)
            if hasattr(self.ui, 'comboBox_restore'):
                action_index = self.ui.comboBox_restore.currentIndex()
                if action_index == 0:  # Restore
                    actions_to_perform.append(("restore_email", "Restore email"))
                elif action_index == 1:  # Changer
                    actions_to_perform.append(("changer", "Change info"))

        # 2. Kiểm tra checkbox chplay (thứ tự thứ hai)
        if hasattr(self.ui, 'checkBox_chplay') and self.ui.checkBox_chplay.isChecked():
            actions_to_perform.append(("chplay", "ChPlay login"))

        # 3. Kiểm tra checkbox thêm ví/thêm ads (thứ tự thứ ba)
        if hasattr(self.ui, 'checkBox_add_wallet_ads') and self.ui.checkBox_add_wallet_ads.isChecked():
            # Lấy giá trị combobox
            if hasattr(self.ui, 'comboBox_wallet_ads'):
                action_index = self.ui.comboBox_wallet_ads.currentIndex()
                if action_index == 0:  # Add Wallet
                    actions_to_perform.append(("add_wallet", "Thêm ví"))
                elif action_index == 1:  # Add Ads
                    actions_to_perform.append(("addcardads", "Thêm thẻ Ads"))

        # 4. Kiểm tra checkbox xóa thẻ/đóng hồ sơ (thứ tự thứ tư)
        if hasattr(self.ui, 'checkBox_xoa_close_payment') and self.ui.checkBox_xoa_close_payment.isChecked():
            # Lấy giá trị combobox
            if hasattr(self.ui, 'comboBox_del_close'):
                action_index = self.ui.comboBox_del_close.currentIndex()
                if action_index == 0:  # Xóa thẻ close
                    actions_to_perform.append(("delete_card", "Xóa thẻ close"))
                elif action_index == 1:  # Đóng hồ sơ
                    actions_to_perform.append(("close_payment", "Đóng hồ sơ"))

        # 5. Kiểm tra checkbox mua (thứ tự thứ năm)
        if hasattr(self.ui, 'checkBox_mua') and self.ui.checkBox_mua.isChecked():
            actions_to_perform.append(("mua", "Mua"))

        if not actions_to_perform:
            QMessageBox.warning(self, "Cảnh báo", "Vui lòng chọn ít nhất một hành động để thực hiện!")
            return

        # Lưu actions vào file action.txt để có thể tự động restart
        self.save_actions_to_file(actions_to_perform)

        # Đặt trạng thái đang chạy và reset cờ dừng
        self.is_start_actions_running = True
        self.stop_start_actions = False

        # Chuyển button sang trạng thái stop
        self.ui.start_stop_Button.setChecked(True)
        self.ui.start_stop_Button.setText("Stop")

        # Hiển thị thông báo về các hành động sẽ thực hiện
        actions_str = " → ".join([action[1] for action in actions_to_perform])
        self.log_message(f"Thực hiện các hành động theo thứ tự: {actions_str}")

        # Thực hiện các hành động tuần tự
        for action_index, (action_name, action_desc) in enumerate(actions_to_perform):
            # Kiểm tra cờ dừng trước mỗi action
            if self.stop_start_actions:
                self.log_message(f"⚠️ Đã dừng start_actions trước khi thực hiện: {action_desc}")
                break

            self.log_message(f"Bắt đầu thực hiện hành động {action_index + 1}/{len(actions_to_perform)}: {action_desc}")

            # Đánh thức tất cả thiết bị trước khi bắt đầu action mới
            self.log_message(f"📱 Đánh thức thiết bị trước khi thực hiện: {action_desc}")
            self.wake_up_all_selected_devices()

            # Cập nhật trạng thái của tất cả các thiết bị được chọn
            for row, _ in selected_devices:  # Sử dụng _ để bỏ qua biến không sử dụng
                status_item = self.ui.tableWidget.item(row, 5)
                if status_item:
                    status_item.setText(f"Đang thực hiện: {action_desc}...")
                    QApplication.processEvents()

            # Thực hiện hành động tương ứng
            try:
                if action_name == "delete_card":
                    self.delete_card()
                elif action_name == "close_payment":
                    self.close_payment()
                elif action_name == "addcardads":
                    self.log_message(f"🔄 Đang thực hiện add card ads...")
                    self.is_adding_card = True
                    self.stop_adding_card = False
                    try:
                        self.addcardads()
                        self.log_message(f"✅ Đã hoàn thành add card ads")
                    finally:
                        self.is_adding_card = False
                elif action_name == "add_wallet":
                    self.log_message(f"🔄 Đang thực hiện add wallet...")
                    self.add_wallet_sync()  # Sử dụng phiên bản đồng bộ
                    self.log_message(f"✅ Đã hoàn thành add wallet")
                elif action_name == "chplay":
                    self.log_message(f"🔄 Đang thực hiện chplay login...")
                    self.is_chplay_running = True
                    self.stop_chplay = False
                    try:
                        # Truyền thông tin bỏ qua captcha khi chạy auto
                        self.chplay_login_sync(skip_captcha=True)
                        self.log_message(f"✅ Đã hoàn thành chplay login")
                    except Exception as e:
                        self.log_message(f"❌ Lỗi trong chplay login: {e}")
                        # Dừng an toàn nếu có lỗi
                        self.safe_stop_chplay_threads()
                    finally:
                        self.is_chplay_running = False
                elif action_name == "restore_email":
                    self.log_message(f"🔄 Đang thực hiện restore email...")
                    self.is_restoring_email = True
                    self.stop_restoring_email = False
                    try:
                        self.restore_email()
                        self.log_message(f"✅ Đã hoàn thành restore email")
                    finally:
                        self.is_restoring_email = False
                elif action_name == "changer":
                    self.log_message(f"🔄 Đang thực hiện change info...")
                    self.is_changing_info = True
                    self.stop_changing_info = False
                    try:
                        self.changer()
                        self.log_message(f"✅ Đã hoàn thành change info")
                    finally:
                        self.is_changing_info = False
                elif action_name == "mua":
                    self.log_message(f"🔄 Đang thực hiện mua game...")
                    self.mua_game()
            except Exception as e:
                self.log_message(f"❌ Lỗi khi thực hiện hành động {action_desc}: {str(e)}")
                # Hiển thị thông báo lỗi
                QMessageBox.critical(self, "Lỗi", f"Lỗi khi thực hiện hành động {action_desc}: {str(e)}")
                # Cập nhật trạng thái của tất cả các thiết bị được chọn
                for row, _ in selected_devices:
                    status_item = self.ui.tableWidget.item(row, 5)
                    if status_item:
                        status_item.setText(f"❌ Lỗi: {action_desc}")
                        QApplication.processEvents()

            # Đợi một chút giữa các hành động
            QApplication.processEvents()

            # Đợi một chút trước khi chuyển sang bước tiếp theo
            self.log_message(f"Đợi 3 giây trước khi chuyển sang bước tiếp theo...")
            time.sleep(5)

            self.log_message(f"Đã hoàn thành hành động {action_index + 1}/{len(actions_to_perform)}: {action_desc}")

        # Cập nhật trạng thái của tất cả các thiết bị được chọn
        for row, _ in selected_devices:  # Sử dụng _ để bỏ qua biến không sử dụng
            status_item = self.ui.tableWidget.item(row, 5)
            if status_item:
                status_item.setText("✅ Đã hoàn thành tất cả các hành động")
                QApplication.processEvents()

        # Kiểm tra auto restart cho bất kỳ action nào (không chỉ email actions)
        if not self.stop_start_actions:
            self.log_message("🔄 Đã hoàn thành tất cả hành động - Kiểm tra auto restart...")
            self.check_and_auto_restart_if_emails_available()

        # Reset trạng thái khi hoàn thành hoặc bị dừng
        self.is_start_actions_running = False
        self.stop_start_actions = False
        self.ui.start_stop_Button.setChecked(False)
        self.ui.start_stop_Button.setText("Start")

        if self.stop_start_actions:
            self.log_message("⚠️ Quá trình start_actions đã bị dừng")
        else:
            self.log_message("✅ Quá trình start_actions đã hoàn thành")

    def wake_up_screen(self, serial):
        """
        Đánh thức màn hình thiết bị để tránh bị đen khi chuyển action
        Tương tự như click chuột phải để đánh thức
        """
        try:
            # Gửi phím POWER để đánh thức màn hình
            cmd = [ADB_PATH, "-s", serial, "shell", "input", "keyevent", "26"]  # KEYCODE_POWER
            result = subprocess.run(cmd, capture_output=True, text=True,
                                  creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

            if result.returncode == 0:
                self.log_message(f"📱 Đã đánh thức màn hình thiết bị {serial}")

                # Đợi một chút để màn hình bật hoàn toàn
                time.sleep(1)

                # Kiểm tra xem màn hình đã sáng chưa bằng cách chụp ảnh thử
                screenshot_cmd = [ADB_PATH, "-s", serial, "exec-out", "screencap", "-p"]
                screenshot_result = subprocess.run(screenshot_cmd, capture_output=True,
                                                 creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

                if screenshot_result.returncode == 0 and len(screenshot_result.stdout) > 1000:
                    self.log_message(f"✅ Màn hình thiết bị {serial} đã sáng và sẵn sàng")
                    return True
                else:
                    # Nếu vẫn chưa sáng, thử swipe để unlock
                    self.log_message(f"🔓 Thử swipe để unlock thiết bị {serial}")
                    swipe_cmd = [ADB_PATH, "-s", serial, "shell", "input", "swipe", "500", "1000", "500", "500"]
                    subprocess.run(swipe_cmd, capture_output=True,
                                 creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                    time.sleep(1)
                    return True
            else:
                self.log_message(f"⚠️ Không thể đánh thức màn hình thiết bị {serial}: {result.stderr}")
                return False

        except Exception as e:
            self.log_message(f"❌ Lỗi khi đánh thức màn hình thiết bị {serial}: {str(e)}")
            return False

    def wake_up_all_selected_devices(self):
        """Đánh thức tất cả thiết bị được chọn"""
        try:
            selected_devices = []
            for row in range(self.ui.tableWidget.rowCount()):
                checkbox_item = self.ui.tableWidget.item(row, 0)
                if checkbox_item and checkbox_item.checkState() == Qt.Checked:
                    serial_item = self.ui.tableWidget.item(row, 1)
                    if serial_item:
                        serial = serial_item.text()
                        selected_devices.append((row, serial))

            if not selected_devices:
                return

            self.log_message(f"📱 Đang đánh thức {len(selected_devices)} thiết bị được chọn...")

            # Đánh thức từng thiết bị
            for row, serial in selected_devices:
                self.wake_up_screen(serial)

            self.log_message(f"✅ Đã hoàn thành đánh thức {len(selected_devices)} thiết bị")

        except Exception as e:
            self.log_message(f"❌ Lỗi khi đánh thức thiết bị: {str(e)}")

    def safe_stop_chplay_threads(self):
        """An toàn dừng tất cả chplay threads để tránh crash"""
        try:
            self.log_message("🛑 Đang dừng chplay threads an toàn...")

            # Dừng chplay_sync_threads nếu có
            if hasattr(self, 'chplay_sync_threads') and self.chplay_sync_threads:
                self.log_message(f"🔄 Đang dừng {len(self.chplay_sync_threads)} chplay sync threads...")

                for i, thread in enumerate(self.chplay_sync_threads):
                    try:
                        if thread and hasattr(thread, 'stop'):
                            self.log_message(f"⏹️ Dừng chplay sync thread {i+1}")
                            thread.stop()
                        elif thread and hasattr(thread, 'terminate'):
                            self.log_message(f"⏹️ Terminate chplay sync thread {i+1}")
                            thread.terminate()
                    except Exception as e:
                        self.log_message(f"⚠️ Lỗi khi dừng chplay sync thread {i+1}: {e}")
                        # Tiếp tục dừng các thread khác
                        continue

                # Clear danh sách
                self.chplay_sync_threads.clear()
                self.log_message("✅ Đã clear danh sách chplay sync threads")

            # Dừng chplay_threads nếu có (từ chplay_login thường)
            if hasattr(self, 'chplay_threads') and self.chplay_threads:
                self.log_message(f"🔄 Đang dừng {len(self.chplay_threads)} chplay threads...")

                for i, thread in enumerate(self.chplay_threads):
                    try:
                        if thread and hasattr(thread, 'stop'):
                            self.log_message(f"⏹️ Dừng chplay thread {i+1}")
                            thread.stop()
                        elif thread and hasattr(thread, 'terminate'):
                            self.log_message(f"⏹️ Terminate chplay thread {i+1}")
                            thread.terminate()
                    except Exception as e:
                        self.log_message(f"⚠️ Lỗi khi dừng chplay thread {i+1}: {e}")
                        # Tiếp tục dừng các thread khác
                        continue

                # Clear danh sách
                self.chplay_threads.clear()
                self.log_message("✅ Đã clear danh sách chplay threads")

            # Set stop flags
            self.stop_chplay = True
            self.is_chplay_running = False

            self.log_message("✅ Đã dừng tất cả chplay threads an toàn")

        except Exception as e:
            self.log_message(f"❌ Lỗi trong safe_stop_chplay_threads: {e}")
            # Đảm bảo set flags dù có lỗi
            try:
                self.stop_chplay = True
                self.is_chplay_running = False
            except:
                pass

    def stop_all_sub_processes(self):
        """Dừng tất cả các sub processes"""
        try:
            self.log_message("🛑 Đang dừng tất cả sub processes...")

            # Dừng AddAds nếu đang chạy
            if hasattr(self, 'is_adding_card') and self.is_adding_card:
                self.stop_adding_card = True
                self.log_message("⚠️ Đã đặt cờ dừng AddAds")

            # Dừng Delete Card nếu đang chạy
            if hasattr(self, 'is_deleting_card') and self.is_deleting_card:
                self.stop_deleting_card = True
                self.log_message("⚠️ Đã đặt cờ dừng Delete Card")

            # Dừng Close Payment nếu đang chạy
            if hasattr(self, 'is_closing_payment') and self.is_closing_payment:
                self.stop_closing_payment = True
                self.log_message("⚠️ Đã đặt cờ dừng Close Payment")

            # Dừng Mua Game nếu đang chạy
            if hasattr(self, 'is_mua_game_running') and self.is_mua_game_running:
                self.stop_mua_game = True
                self.log_message("⚠️ Đã đặt cờ dừng Mua Game")

            # Dừng Restore Email nếu đang chạy
            if hasattr(self, 'is_restoring_email') and self.is_restoring_email:
                self.stop_restoring_email = True
                self.log_message("⚠️ Đã đặt cờ dừng Restore Email")

            # Dừng Change Info nếu đang chạy
            if hasattr(self, 'is_changing_info') and self.is_changing_info:
                self.stop_changing_info = True
                self.log_message("⚠️ Đã đặt cờ dừng Change Info")

            # Dừng ChPlay nếu đang chạy (sử dụng hàm an toàn)
            if hasattr(self, 'is_chplay_running') and self.is_chplay_running:
                self.safe_stop_chplay_threads()
                if hasattr(self, 'chplay_multi_thread'):
                    try:
                        self.chplay_multi_thread.stop_all()
                    except Exception as e:
                        self.log_message(f"⚠️ Lỗi khi dừng chplay_multi_thread: {e}")
                self.log_message("⚠️ Đã dừng ChPlay an toàn")

            # Dừng ChPlay Sync nếu đang chạy
            if hasattr(self, 'chplay_sync_completion_event'):
                try:
                    self.chplay_sync_completion_event.set()
                    self.log_message("⚠️ Đã báo hiệu dừng ChPlay Sync")
                except Exception as e:
                    self.log_message(f"⚠️ Lỗi khi set completion event: {e}")

            self.log_message("✅ Đã gửi tín hiệu dừng đến tất cả sub processes")

        except Exception as e:
            self.log_message(f"❌ Lỗi khi dừng sub processes: {str(e)}")

    def add_card(self):
        """Thêm thẻ mới vào file card.txt"""
        try:
            # Mở file card.txt để thêm thẻ mới
            card_file_path = path_manager.get_data_path("card.txt")
            self.log_message(f"🔍 Đường dẫn file card.txt: {card_file_path}")

            # Kiểm tra xem thư mục data có tồn tại không
            data_dir = os.path.dirname(card_file_path)
            if not os.path.exists(data_dir):
                os.makedirs(data_dir)
                self.log_message(f"✅ Đã tạo thư mục {data_dir}")

            # Hiển thị hộp thoại nhập thẻ mới
            card_info, ok = QInputDialog.getText(self, "Thêm thẻ mới", "Nhập thông tin thẻ (format: số thẻ|tháng|năm|cvv):")
            if ok and card_info:
                # Kiểm tra định dạng thẻ
                if not self.validate_card_format(card_info):
                    QMessageBox.warning(self, "Lỗi", "Định dạng thẻ không hợp lệ. Vui lòng nhập theo format: số thẻ|tháng|năm|cvv")
                    return

                # Thêm thẻ vào file
                with open(card_file_path, "a") as file:
                    file.write(card_info + "\n")

                self.log_message(f"✅ Đã thêm thẻ mới vào file: {card_info}")

                # Đợi một chút để đảm bảo file đã được ghi hoàn tất
                time.sleep(0.5)

                # Cập nhật thống kê thẻ
                self.log_message("🔄 Đang cập nhật thống kê thẻ sau khi thêm thẻ mới...")
                self.update_card_stats()

                # Log thông báo thêm thẻ (không hiển thị message box)
                self.log_message(f"✅ Đã thêm thẻ mới và cập nhật thống kê thẻ thành công")
        except Exception as e:
            QMessageBox.critical(self, "Lỗi", f"Không thể thêm thẻ mới: {str(e)}")
            self.log_message(f"❌ Lỗi khi thêm thẻ mới: {str(e)}")
            import traceback
            self.log_message(f"❌ Chi tiết lỗi: {traceback.format_exc()}")

    def validate_card_format(self, card_info):
        """Kiểm tra định dạng thẻ"""
        parts = card_info.split("|")
        if len(parts) != 4:
            return False

        card_number, month, year, cvv = parts

        # Kiểm tra số thẻ (16 chữ số)
        if not card_number.isdigit() or len(card_number) != 16:
            return False

        # Kiểm tra tháng (01-12)
        if not month.isdigit() or int(month) < 1 or int(month) > 12:
            return False

        # Kiểm tra năm (2 chữ số)
        if not year.isdigit() or len(year) != 2:
            return False

        # Kiểm tra CVV (3 chữ số)
        if not cvv.isdigit() or len(cvv) != 3:
            return False

        return True

    def addcardads(self):
        """Thực hiện chuỗi hành động thêm thẻ trong app Google Ads"""
        # Kiểm tra license - chỉ ALL_BLOCKED mới bị khóa
        app = QApplication.instance()
        allowed_features = app.property("allowed_features")
        if allowed_features and allowed_features.upper() == "ALL_BLOCKED":
            QMessageBox.warning(self, "Tính năng bị giới hạn",
                               "Tính năng thêm thẻ Ads đã bị khóa do giới hạn license.")
            self.log_message("❌ Tính năng thêm thẻ Ads bị khóa do license ALL_BLOCKED")
            return

        # Nếu đang trong quá trình thêm thẻ, dừng quá trình
        if hasattr(self, 'is_adding_card') and self.is_adding_card:
            # Đặt cờ dừng và hiển thị thông báo
            self.log_message("⏹️ Đang dừng quá trình thêm thẻ...")
            self.stop_adding_card = True

            # Dừng thread nếu đang chạy
            if hasattr(self, 'add_card_thread') and self.add_card_thread.isRunning():
                self.log_message("🛑 Đang dừng AddCard thread...")
                self.add_card_thread.terminate()  # Dừng thread mạnh mẽ
                self.add_card_thread.wait(3000)  # Đợi tối đa 3 giây
                if self.add_card_thread.isRunning():
                    self.log_message("⚠️ Thread không dừng được, kill thread")
                    self.add_card_thread.quit()
                    self.add_card_thread.wait()

            # Reset button về trạng thái bình thường
            self.ui.add_card_Button.setChecked(False)
            self.is_adding_card = False
            return
        # Kiểm tra xem thư mục template có tồn tại không
        template_dir = "template"
        if not os.path.exists(template_dir):
            os.makedirs(template_dir)
            self.log_message(f"Đã tạo thư mục {template_dir}")

        # Kiểm tra xem các file template cần thiết có tồn tại không
        missing_templates = []
        for template_name in ["fix_it.png", "add_payment_ads.png", "add_gredit.png", "card_number.png", "save_card.png"]:
            template_path = os.path.join(template_dir, template_name)
            if not os.path.exists(template_path):
                missing_templates.append(template_name)

        # Kiểm tra các file template tùy chọn
        optional_templates = []
        for template_name in ["unti.png", "dau_x.png"]:
            template_path = os.path.join(template_dir, template_name)
            if not os.path.exists(template_path):
                optional_templates.append(template_name)


        # Kiểm tra xem có thiết bị nào được chọn không
        selected_devices = []
        for row in range(self.ui.tableWidget.rowCount()):
            widget = self.ui.tableWidget.cellWidget(row, 0)
            if widget:
                checkbox = widget.findChild(QCheckBox)
                if checkbox and checkbox.isChecked():
                    serial_item = self.ui.tableWidget.item(row, 1)
                    if serial_item:
                        selected_devices.append((row, serial_item.text().strip()))

        if not selected_devices:
            QMessageBox.warning(self, "Cảnh báo", "Vui lòng chọn ít nhất một thiết bị!")
            return

        # Lấy giá trị từ spinBox_so_lan_add_card (chỉ cho addads)
        num_loops = 1  # Giá trị mặc định
        if hasattr(self.ui, 'spinBox_so_lan_add_card'):
            num_loops = self.ui.spinBox_so_lan_add_card.value()
            self.log_message(f"Lấy số lần lặp từ spinBox_so_lan_add_card: {num_loops}")

        # Hiển thị thông báo về số lần lặp
        self.log_message(f"Thực hiện {num_loops} lần add quy trình thêm thẻ")

        # Cập nhật trạng thái của tất cả các thiết bị được chọn
        for row, _ in selected_devices:  # Sử dụng _ để bỏ qua biến không sử dụng
            status_item = self.ui.tableWidget.item(row, 5)
            if status_item:
                status_item.setText(f"Đang thực hiện add card {num_loops} lần ...")
                QApplication.processEvents()

        # Đặt trạng thái đang thêm thẻ
        self.is_adding_card = True
        self.stop_adding_card = False

        # Chuyển button sang trạng thái stop (hiển thị stop icon)
        self.ui.add_card_Button.setChecked(True)

        # Cập nhật giao diện ngay lập tức
        QApplication.processEvents()
        self.log_message("🔄 Đã bắt đầu quá trình thêm thẻ.")

        # Tạo một hàm để xử lý từng thiết bị
        def process_device(row_serial_tuple):
            row, serial = row_serial_tuple
            try:
                # Kiểm tra cờ dừng trước khi bắt đầu (cả stop_adding_card và stop_start_actions)
                if self.stop_adding_card or (hasattr(self, 'stop_start_actions') and self.stop_start_actions):
                    status = "⚠️ Đã dừng theo yêu cầu"
                    status_item = self.ui.tableWidget.item(row, 5)
                    if status_item:
                        status_item.setText(status)
                    self.log_message(f"{status} - Thiết bị {serial} (dòng {row + 1})")
                    # Đặt cờ dừng cho addcardads nếu stop_start_actions được kích hoạt
                    if hasattr(self, 'stop_start_actions') and self.stop_start_actions:
                        self.stop_adding_card = True
                    return status

                # Cập nhật trạng thái
                status_item = self.ui.tableWidget.item(row, 5)
                if status_item:
                    status_item.setText(f"Đang thực hiện add card 0/{num_loops} lần...")
                    QApplication.processEvents()

                # Tạo đối tượng add_ads với số lần lặp, chỉ số thiết bị và tổng số thiết bị
                device_index = selected_devices.index((row, serial))
                total_devices = len(selected_devices)
                # Truyền đường dẫn ADB vào đối tượng add_ads
                device = add_ads(serial, num_loops, device_index, total_devices, adb_path=ADB_PATH)

                # Tạo hàm cập nhật trạng thái (giống addwallet)
                def update_status():
                    if status_item:
                        # Hiển thị thông tin thẻ hiện tại (giống addwallet)
                        card_info = ""
                        if hasattr(device, 'current_card_info') and device.current_card_info:
                            card_last4 = device.current_card_info['card_number'][-4:]
                            card_info = f" - Thẻ {card_last4}"

                        # Hiển thị vòng lặp hiện tại / tổng số vòng lặp + thông tin thẻ
                        status_text = f"🎯 AddAds: {device.current_loop}/{num_loops}{card_info}"
                        status_item.setText(status_text)

                        # Thêm thông báo log để hiển thị trạng thái hiện tại
                        self.log_message(f"🔄 {status_text} - Thiết bị {serial}")
                        # Đảm bảo giao diện được cập nhật ngay lập tức
                        QApplication.processEvents()
                        # Thêm một lần cập nhật nữa để đảm bảo UI được cập nhật
                        QApplication.processEvents()

                # Đặt hàm cập nhật trạng thái vào device
                device.update_status_func = update_status

                # Tạo hàm kiểm tra cờ dừng để truyền vào đối tượng add_ads
                def check_stop_flag():
                    # Cập nhật giao diện ngay lập tức để đảm bảo cờ dừng được cập nhật
                    QApplication.processEvents()
                    # Kiểm tra cờ dừng (cả stop_adding_card và stop_start_actions)
                    if self.stop_adding_card:
                        return True
                    if hasattr(self, 'stop_start_actions') and self.stop_start_actions:
                        # Nếu stop_start_actions được kích hoạt, cũng đặt stop_adding_card
                        self.stop_adding_card = True
                        return True
                    return False



                # Thực hiện hành động với kiểm tra cờ dừng
                success = device.execute_actions(check_stop_flag=check_stop_flag)

                # Cập nhật trạng thái cuối cùng
                update_status()

                # Kiểm tra cờ dừng sau khi thực hiện hành động (cả stop_adding_card và stop_start_actions)
                if self.stop_adding_card or (hasattr(self, 'stop_start_actions') and self.stop_start_actions):
                    status = "⚠️ Đã dừng thêm thẻ"
                    if status_item:
                        status_item.setText(status)
                    self.log_message(f"{status} - Thiết bị {serial} (dòng {row + 1})")
                    # Đặt cờ dừng cho addcardads nếu stop_start_actions được kích hoạt
                    if hasattr(self, 'stop_start_actions') and self.stop_start_actions:
                        self.stop_adding_card = True
                    return status

                # Cập nhật trạng thái dựa trên kết quả
                if success:
                    # Hiển thị số lần đã thêm thẻ hiện tại
                    status = f"✅ Đã hoàn thành {device.current_loop}/{num_loops} lần add card"
                    status_item = self.ui.tableWidget.item(row, 5)
                else:
                    status = f"❌ Không thể hoàn thành {device.current_loop}/{num_loops} lần thêm thẻ"

                # Cập nhật trạng thái trong bảng
                if status_item:
                    status_item.setText(status)
                self.log_message(f"{status} - Thiết bị {serial} (dòng {row + 1})")
                return status

            except Exception as e:
                error_msg = f"Lỗi khi mở Google Ads: {str(e)}"
                status_item = self.ui.tableWidget.item(row, 5)
                if status_item:
                    status_item.setText(f"❌ {error_msg}")
                self.log_message(f"❌ {error_msg} - Thiết bị {serial} (dòng {row + 1})")
                return error_msg

        # Tạo một luồng riêng biệt để xử lý tất cả các thiết bị
        # Điều này giúp tránh đóng băng giao diện người dùng
        from PyQt5.QtCore import QThread, pyqtSignal

        class AddCardThread(QThread):
            finished = pyqtSignal()
            progress = pyqtSignal(str)

            def __init__(self, parent, selected_devices, process_device_func):
                super().__init__(parent)
                self.selected_devices = selected_devices
                self.process_device_func = process_device_func

            def run(self):
                try:
                    # Sử dụng ThreadPoolExecutor để chạy tất cả các thiết bị đồng thời
                    with concurrent.futures.ThreadPoolExecutor(max_workers=len(self.selected_devices)) as executor:
                        # Bắt đầu tất cả các tác vụ
                        future_to_device = {executor.submit(self.process_device_func, device): device for device in self.selected_devices}

                        # Chờ tất cả các tác vụ hoàn thành
                        for future in concurrent.futures.as_completed(future_to_device):
                            # Cập nhật tiến trình
                            self.progress.emit("Đang xử lý thiết bị...")

                            # Kiểm tra xem có lỗi nào không
                            try:
                                future.result()
                            except Exception as e:
                                self.progress.emit(f"Lỗi: {str(e)}")
                finally:
                    # Phát tín hiệu hoàn thành
                    self.finished.emit()

        # Hàm xử lý khi luồng hoàn thành
        def on_thread_finished():
            # Đặt lại trạng thái
            self.is_adding_card = False
            self.stop_adding_card = False

            # Reset button về trạng thái bình thường
            self.ui.add_card_Button.setChecked(False)

            # KHÔNG cập nhật trạng thái chung để tránh ghi đè trạng thái cá nhân
            # Mỗi thiết bị đã có trạng thái riêng từ process_device

            # Cập nhật giao diện ngay lập tức - gọi nhiều lần để đảm bảo
            QApplication.processEvents()
            QApplication.processEvents()  # Gọi 2 lần để đảm bảo

            # Force repaint table
            self.ui.tableWidget.repaint()

            # Sử dụng QTimer để đảm bảo UI được cập nhật sau một khoảng thời gian ngắn
            from PyQt5.QtCore import QTimer
            def delayed_update():
                QApplication.processEvents()
                self.ui.tableWidget.repaint()
            QTimer.singleShot(100, delayed_update)  # Cập nhật sau 100ms

            self.log_message("✅ Quá trình thêm thẻ đã hoàn thành")

            # Hiển thị thông báo hoàn thành
            if self.stop_adding_card:
                self.log_message("⚠️ Quá trình thêm thẻ đã bị dừng theo yêu cầu")
            else:
                self.log_message(f"✅ Đã hoàn thành {num_loops} lần thêm thẻ trên tất cả các thiết bị!")

                # Cập nhật thống kê thẻ sau khi hoàn thành
                self.update_card_stats()

        # Hàm xử lý khi có tiến trình mới
        def on_progress(message):
            self.log_message(message)
            # Cập nhật giao diện ngay lập tức
            QApplication.processEvents()

        # Tạo và khởi động luồng
        self.add_card_thread = AddCardThread(self, selected_devices, process_device)
        self.add_card_thread.finished.connect(on_thread_finished)
        self.add_card_thread.progress.connect(on_progress)
        self.add_card_thread.start()

        # Đợi thread hoàn thành (để tránh chạy song song với action tiếp theo)
        self.log_message(f"⏳ Đang đợi {len(selected_devices)} thiết bị hoàn thành addcardads...")
        while self.add_card_thread.isRunning():
            # Kiểm tra cờ dừng mỗi giây (cả stop_adding_card và stop_start_actions)
            if self.stop_adding_card or (hasattr(self, 'stop_start_actions') and self.stop_start_actions):
                self.log_message("⚠️ Đã dừng đợi addcardads theo yêu cầu")
                # Đặt cờ dừng cho addcardads nếu stop_start_actions được kích hoạt
                if hasattr(self, 'stop_start_actions') and self.stop_start_actions:
                    self.stop_adding_card = True
                return
            self.add_card_thread.wait(1000)  # Đợi 1 giây
            QApplication.processEvents()
        self.log_message("✅ Tất cả thiết bị đã hoàn thành addcardads")

    def delete_card(self):
        """Xóa thẻ đã đóng từ file card.txt và thực hiện hành động mở Chrome trên thiết bị"""
        # Kiểm tra license - chỉ ALL_BLOCKED mới bị khóa
        app = QApplication.instance()
        allowed_features = app.property("allowed_features")
        if allowed_features and allowed_features.upper() == "ALL_BLOCKED":
            QMessageBox.warning(self, "Tính năng bị giới hạn",
                               "Tính năng xóa thẻ đã bị khóa do giới hạn license.")
            self.log_message("❌ Tính năng xóa thẻ bị khóa do license ALL_BLOCKED")
            return

        # Nếu đang trong quá trình xóa thẻ, dừng quá trình (giống mua)
        if self.is_deleting_card:
            self.log_message("⏹️ Đang dừng quá trình xóa thẻ...")
            self.stop_deleting_card = True

            # Đặt cờ dừng cho tất cả các thiết bị đang chạy (giống mua)
            if hasattr(self, 'stop_flags'):
                for serial, flag in self.stop_flags.items():
                    if isinstance(flag, threading.Event):
                        flag.set()
                        self.log_message(f"⚠️ Đã đặt cờ dừng cho thiết bị {serial}")

            # Reset button về trạng thái bình thường
            self.ui.xoa_the_close_Button.setChecked(False)
            self.is_deleting_card = False
            return

        # Kiểm tra xem có thiết bị nào được chọn không
        selected_devices = []
        for row in range(self.ui.tableWidget.rowCount()):
            widget = self.ui.tableWidget.cellWidget(row, 0)
            if widget:
                checkbox = widget.findChild(QCheckBox)
                if checkbox and checkbox.isChecked():
                    serial_item = self.ui.tableWidget.item(row, 1)
                    if serial_item:
                        selected_devices.append((row, serial_item.text().strip()))

        if not selected_devices:
            QMessageBox.warning(self, "Cảnh báo", "Vui lòng chọn ít nhất một thiết bị!")
            return

        # Kiểm tra và tạo thư mục template nếu chưa tồn tại
        template_dir = os.path.join(BASE_PATH, "template")
        if not os.path.exists(template_dir):
            os.makedirs(template_dir)
            self.log_message(f"✅ Đã tạo thư mục {template_dir}")

        # Đường dẫn đến file card.txt
        card_file_path = os.path.join(BASE_PATH, "data", "card.txt")

        # Kiểm tra xem file có tồn tại không
        if not os.path.exists(card_file_path):
            # Tạo thư mục data nếu chưa tồn tại
            data_dir = os.path.join(BASE_PATH, "data")
            if not os.path.exists(data_dir):
                os.makedirs(data_dir)
                print(f"Đã tạo thư mục {data_dir}")

            # Tạo file card.txt trống
            with open(card_file_path, "w") as file:
                file.write("# Danh sách thẻ (format: số thẻ|tháng|năm|cvv)\n")
            print(f"Đã tạo file {card_file_path}")

        # Cập nhật trạng thái của tất cả các thiết bị được chọn
        for row, _ in selected_devices:  # Sử dụng _ để bỏ qua biến không sử dụng
            status_item = self.ui.tableWidget.item(row, 5)
            if status_item:
                status_item.setText("Mở Chrome...")
                QApplication.processEvents()

        # Đặt trạng thái đang xóa thẻ và reset cờ dừng (giống mua)
        self.is_deleting_card = True
        self.stop_deleting_card = False

        # Chuyển button sang trạng thái stop (hiển thị stop icon)
        self.ui.xoa_the_close_Button.setChecked(True)

        # Khởi tạo stop_flags nếu chưa có (giống mua)
        if not hasattr(self, 'stop_flags'):
            self.stop_flags = {}

        # Khởi tạo counter cho threads delete card (giống mua)
        self.delete_card_thread_count = len(selected_devices)
        self.delete_card_thread_lock = threading.Lock()

        # Cập nhật giao diện ngay lập tức
        QApplication.processEvents()
        self.log_message("Đã bắt đầu quá trình xóa thẻ.")

        # Tạo một hàm để xử lý từng thiết bị
        def process_device(row_serial_tuple):
            row, serial = row_serial_tuple
            try:
                # Kiểm tra cờ dừng
                if self.stop_deleting_card:
                    status = "⚠️ Đã dừng theo yêu cầu"
                    status_item = self.ui.tableWidget.item(row, 5)
                    if status_item:
                        status_item.setText(status)
                    self.log_message(f"{status} - Thiết bị {serial} (dòng {row + 1})")
                    return status

                # Cập nhật trạng thái
                status_item = self.ui.tableWidget.item(row, 5)
                if status_item:
                    status_item.setText("Đang mở Chrome...")
                    QApplication.processEvents()

                # Lấy mật khẩu từ password_manager
                password = password_manager.get_password(serial, row)
                if password:
                    print(f"✅ Đã lấy mật khẩu từ password_manager cho thiết bị {serial}: {password[:2]}{'*' * (len(password) - 2) if len(password) > 2 else '*' * len(password)}")
                else:
                    print(f"⚠️ Không tìm thấy mật khẩu trong password_manager cho thiết bị {serial}")

                    # Thử lấy mật khẩu từ cột Pass (cột thứ 3, index 2) như phương án dự phòng
                    pass_item = self.ui.tableWidget.item(row, 2)  # Cột Pass là cột thứ 3 (index 2)
                    if pass_item and pass_item.text().strip():
                        password = pass_item.text().strip()
                        print(f"✅ Đã lấy mật khẩu từ cột Pass: {password[:2]}{'*' * (len(password) - 2) if len(password) > 2 else '*' * len(password)}")

                        # Cập nhật mật khẩu vào password_manager
                        password_manager.set_password(serial, password, row)
                    else:
                        print(f"⚠️ Không tìm thấy mật khẩu trong cột Pass (row={row}, col=2)")
                        password = ""

                # Tạo đối tượng delete_card_close và thực thi hành động
                # Lấy chỉ số thiết bị trong danh sách thiết bị được chọn
                device_index = selected_devices.index((row, serial))

                # Tạo hàm callback để cập nhật trạng thái trong giao diện người dùng
                def update_status_callback(status):
                    # Cập nhật trạng thái trong bảng
                    status_item = self.ui.tableWidget.item(row, 5)
                    if status_item:
                        status_item.setText(status)
                    # Cập nhật giao diện ngay lập tức
                    QApplication.processEvents()
                    # Ghi log
                    self.log_message(f"{status} - Thiết bị {serial} (dòng {row + 1})")

                # Lấy mã xác thực từ cột authen (cột 3) nếu có
                auth_secret = None
                auth_item = self.ui.tableWidget.item(row, 3)
                if auth_item and auth_item.text().strip():
                    auth_secret = auth_item.text().strip()

                # Tạo đối tượng delete_card_close và truyền mật khẩu + auth_secret trực tiếp vào constructor
                device = delete_card_close(serial, device_index, update_status_callback, adb_path=ADB_PATH, password=password, auth_secret=auth_secret)
                if password:
                    print(f"✅ Đã truyền mật khẩu vào đối tượng delete_card_close: {password[:2]}{'*' * (len(password) - 2) if len(password) > 2 else '*' * len(password)}")
                if auth_secret:
                    print(f"✅ Đã truyền auth_secret vào đối tượng delete_card_close: {auth_secret[:4]}{'*' * (len(auth_secret) - 4) if len(auth_secret) > 4 else '*' * len(auth_secret)}")

                # Tạo biến cờ dừng (giống mua)
                stop_flag = threading.Event()
                # Đảm bảo self.stop_flags đã được khởi tạo
                if not hasattr(self, 'stop_flags'):
                    self.stop_flags = {}
                self.stop_flags[serial] = stop_flag

                # Tạo hàm kiểm tra cờ dừng kết hợp cả cờ cục bộ và cờ toàn cục (giống mua)
                def check_stop_flag():
                    # Kiểm tra cờ dừng toàn cục
                    if self.stop_deleting_card:
                        self.log_message(f"⚠️ Đã nhận tín hiệu dừng toàn cục cho thiết bị {serial}")
                        return True
                    # Kiểm tra cờ dừng cục bộ
                    if stop_flag.is_set():
                        self.log_message(f"⚠️ Đã nhận tín hiệu dừng cục bộ cho thiết bị {serial}")
                        return True
                    return False

                # Thực hiện hành động với kiểm tra cờ dừng
                success = False
                try:
                    # Bắt đầu thực hiện hành động với execute_with_loop (1 lần duy nhất, bỏ retry toàn cục)
                    success = device.execute_with_loop(check_stop_flag=check_stop_flag)

                    # Kiểm tra cờ dừng sau mỗi bước thực hiện
                    if check_stop_flag():
                        status = "⚠️ Đã dừng xoá thẻ"
                        if status_item:
                            status_item.setText(status)
                        self.log_message(f"{status} - Thiết bị {serial} (dòng {row + 1})")
                        return status
                except Exception as e:
                    self.log_message(f"❌ Lỗi khi thực hiện hành động: {str(e)} - Thiết bị {serial} (dòng {row + 1})")
                    success = False

                # Cập nhật trạng thái dựa trên kết quả
                if success:
                    status = "✅ Đã hoàn thành xóa thẻ"
                    self.log_message(f"✅ Hoàn thành xóa thẻ - Thiết bị {serial}")
                else:
                    status = "⚠️ Đã dừng xóa thẻ"
                    self.log_message(f"⚠️ Đã dừng chuỗi hành động xóa thẻ trên thiết bị {serial}")

                # Cập nhật trạng thái trong bảng
                if status_item:
                    status_item.setText(status)

                # Giảm counter và kiểm tra nếu tất cả threads đã hoàn thành (giống mua)
                with self.delete_card_thread_lock:
                    self.delete_card_thread_count -= 1
                    if self.delete_card_thread_count <= 0:
                        # Tất cả threads đã hoàn thành, reset nút
                        self.is_deleting_card = False
                        self.ui.xoa_the_close_Button.setChecked(False)
                        self.log_message("✅ Quá trình xóa thẻ đã hoàn thành")

                return status

            except Exception as e:
                error_msg = f"Lỗi khi xóa thẻ: {str(e)}"
                status_item = self.ui.tableWidget.item(row, 5)
                if status_item:
                    status_item.setText(f"❌ {error_msg}")
                self.log_message(f"❌ {error_msg} - Thiết bị {serial} (dòng {row + 1})")

                # Giảm counter và kiểm tra nếu tất cả threads đã hoàn thành (cả khi có lỗi) - giống mua
                with self.delete_card_thread_lock:
                    self.delete_card_thread_count -= 1
                    if self.delete_card_thread_count <= 0:
                        # Tất cả threads đã hoàn thành, reset nút
                        self.is_deleting_card = False
                        self.ui.xoa_the_close_Button.setChecked(False)
                        self.log_message("✅ Quá trình xóa thẻ đã hoàn thành")

                return error_msg

        # Danh sách để theo dõi threads (giống mua)
        delete_threads = []

        # Thực hiện chuỗi hành động trên từng thiết bị được chọn (giống mua)
        for row, device in selected_devices:
            # Cập nhật trạng thái
            status_item = self.ui.tableWidget.item(row, 5)
            if status_item:
                status_item.setText("Đang thực hiện...")
                QApplication.processEvents()

            # Tạo một luồng mới để thực hiện chuỗi hành động
            thread = threading.Thread(target=process_device, args=((row, device),))
            thread.daemon = True
            thread.start()
            delete_threads.append(thread)

        # Đợi tất cả threads hoàn thành (giống mua)
        self.log_message(f"⏳ Đang đợi {len(delete_threads)} thiết bị hoàn thành xóa thẻ...")
        for thread in delete_threads:
            while thread.is_alive():
                # Kiểm tra cờ dừng mỗi giây
                if self.stop_deleting_card:
                    self.log_message("⚠️ Đã dừng đợi xóa thẻ theo yêu cầu")
                    return
                thread.join(timeout=1.0)  # Đợi 1 giây
                QApplication.processEvents()
        self.log_message("✅ Tất cả thiết bị đã hoàn thành xóa thẻ")

    def close_payment(self):
        """Đóng hồ sơ thanh toán trên thiết bị"""
        # Kiểm tra license - chỉ ALL_BLOCKED mới bị khóa
        app = QApplication.instance()
        allowed_features = app.property("allowed_features")
        if allowed_features and allowed_features.upper() == "ALL_BLOCKED":
            self.log_message("❌ Tính năng đóng hồ sơ bị khóa do license ALL_BLOCKED")
            return

        # Nếu đang trong quá trình đóng hồ sơ, dừng quá trình
        if hasattr(self, 'is_closing_payment') and self.is_closing_payment:
            # Đặt cờ dừng và hiển thị thông báo
            self.log_message("⏹️ Đang dừng quá trình đóng hồ sơ...")
            self.stop_closing_payment = True

            # Nếu có thread đang chạy, đợi nó dừng hoặc terminate
            if hasattr(self, 'close_payment_thread') and self.close_payment_thread.isRunning():
                self.log_message("⏳ Đang đợi thread đóng hồ sơ dừng...")
                # Đợi tối đa 3 giây để thread tự dừng
                if not self.close_payment_thread.wait(3000):
                    self.log_message("⚠️ Thread không dừng trong 3 giây, buộc terminate...")
                    self.close_payment_thread.terminate()
                    self.close_payment_thread.wait()  # Đợi terminate hoàn thành
                self.log_message("✅ Thread đóng hồ sơ đã dừng")

            # Reset button về trạng thái bình thường
            self.ui.close_payment_Button.setChecked(False)
            self.is_closing_payment = False
            return

        # Đường dẫn đến các file template
        template_dir = os.path.join(BASE_PATH, "template")
        chrome_template_path = os.path.join(template_dir, "chrome.png")
        continue_template_path = os.path.join(template_dir, "continue.png")
        yes_im_in_template_path = os.path.join(template_dir, "yes_im_in.png")
        got_it_template_path = os.path.join(template_dir, "got_it.png")
        search_template_path = os.path.join(template_dir, "search.png")
        ver_that_template_path = os.path.join(template_dir, "ver_that.png")
        ok_template_path = os.path.join(template_dir, "ok.png")
        try_another_way_template_path = os.path.join(template_dir, "try_another_way.png")
        continue2_template_path = os.path.join(template_dir, "continue2.png")
        menu_template_path = os.path.join(template_dir, "menu.png")
        setting_template_path = os.path.join(template_dir, "setting.png")
        close_payment_template_path = os.path.join(template_dir, "close_payment.png")
        continue_ver_template_path = os.path.join(template_dir, "continue_ver.png")

        # Đường dẫn đến các file template mới
        why_are_you_template_path = os.path.join(template_dir, "why_are_you.png")
        i_dont_want_template_path = os.path.join(template_dir, "i_dont_want.png")
        i_have_had_template_path = os.path.join(template_dir, "i_have_had.png")

        # Khởi tạo các biến trạng thái
        self.is_closing_payment = False
        self.stop_closing_payment = False

        # Kiểm tra tồn tại của thư mục template
        template_dir = os.path.join(BASE_PATH, "template")
        if not os.path.exists(template_dir):
            os.makedirs(template_dir)
            # Log thông báo tạo thư mục template (không hiển thị message box)
            self.log_message(f"📁 Đã tạo thư mục {template_dir}. Vui lòng đặt các file ảnh template vào thư mục này.")
            return

        # Tạo danh sách các thiết bị được chọn
        selected_devices = []
        for row in range(self.ui.tableWidget.rowCount()):
            widget = self.ui.tableWidget.cellWidget(row, 0)
            if widget:
                checkbox = widget.findChild(QCheckBox)
                if checkbox and checkbox.isChecked():
                    serial_item = self.ui.tableWidget.item(row, 1)
                    if serial_item:
                        serial = serial_item.text().strip()
                        selected_devices.append((row, serial))

        if not selected_devices:
            QMessageBox.warning(self, "Cảnh báo", "Vui lòng chọn ít nhất một thiết bị!")
            return

        # Cập nhật trạng thái của tất cả các thiết bị được chọn
        for row, serial in selected_devices:
            status_item = self.ui.tableWidget.item(row, 5)
            if status_item:
                status_item.setText("Đang chuẩn bị đóng hồ sơ...")
                QApplication.processEvents()

        # Tạo một hàm để xử lý từng thiết bị
        def process_device(row_serial_tuple):
            row, serial = row_serial_tuple
            try:
                # Kiểm tra cờ dừng trước khi bắt đầu
                if self.stop_closing_payment:
                    print("⚠️ Đã nhận tín hiệu dừng từ người dùng trước khi bắt đầu xử lý thiết bị")
                    return "⚠️ Đã dừng theo yêu cầu"

                # Cập nhật trạng thái
                status_item = self.ui.tableWidget.item(row, 5)
                if status_item:
                    status_item.setText("Đang đóng hồ sơ...")
                    QApplication.processEvents()

                # Lấy mật khẩu từ password_manager, ưu tiên từ tableWidget
                password = password_manager.get_password(serial, row, self.ui.tableWidget)
                if password:
                    print(f"✅ Đã lấy mật khẩu cho thiết bị {serial}: {password[:2]}{'*' * (len(password) - 2) if len(password) > 2 else '*' * len(password)}")

                    # Lưu mật khẩu vào biến toàn cục để sử dụng sau này
                    self.current_password = password

                    # Thử gửi mật khẩu trực tiếp từ MainForm.py
                    try:
                        print(f"🔄 Thử gửi mật khẩu trực tiếp từ MainForm.py: {password[:2]}{'*' * (len(password) - 2) if len(password) > 2 else '*' * len(password)}")
                        # Lưu mật khẩu vào file tạm để kiểm tra
                        with open("password_debug.txt", "w") as f:
                            f.write(f"Mật khẩu: {password}")
                    except Exception as e:
                        print(f"❌ Lỗi khi lưu mật khẩu để debug: {str(e)}")
                else:
                    print(f"❌ Không tìm thấy mật khẩu cho thiết bị {serial}")

                    # Đặt mật khẩu mặc định nếu không tìm thấy
                    password = "default_password"
                    self.current_password = password
                    print(f"⚠️ Sử dụng mật khẩu mặc định: {password}")

                # Tạo một hàm để nhập mật khẩu trực tiếp sau khi tìm thấy close_payment.png
                def input_password_after_close_payment(serial, password):
                    try:
                        # Tìm close_payment.png
                        close_payment_template_path = os.path.join("template", "close_payment.png")
                        if not os.path.exists(close_payment_template_path):
                            print(f"⚠️ Không tìm thấy file template close_payment.png")
                            return False

                        # Chụp ảnh màn hình
                        screenshot_path = os.path.join("screen", f"screen_{serial}.png")
                        subprocess.run(["adb", "-s", serial, "exec-out", "screencap", "-p"],
                                      stdout=open(screenshot_path, "wb", creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0), check=True)

                        # Đọc ảnh template và ảnh màn hình
                        import cv2
                        template = cv2.imread(close_payment_template_path)
                        screenshot = cv2.imread(screenshot_path)

                        if template is None or screenshot is None:
                            print(f"❌ Không thể đọc ảnh template hoặc ảnh màn hình")
                            return False

                        # Chuyển đổi ảnh sang thang độ xám
                        template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
                        screenshot_gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)

                        # Tìm kiếm template trong ảnh màn hình
                        result = cv2.matchTemplate(screenshot_gray, template_gray, cv2.TM_CCOEFF_NORMED)
                        _, max_val, _, max_loc = cv2.minMaxLoc(result)

                        # Nếu độ khớp lớn hơn ngưỡng, tap vào vị trí tìm thấy
                        if max_val >= 0.7:
                            # Tính toán tọa độ trung tâm của template
                            h, w = template_gray.shape
                            center_x = max_loc[0] + w // 2
                            center_y = max_loc[1] + h // 2

                            # Tap vào vị trí trung tâm
                            subprocess.run([ADB_PATH, "-s", serial, "shell", "input", "tap",
                                           str(center_x), str(center_y)], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

                            print(f"✅ Đã tìm thấy và tap vào close_payment.png (độ khớp: {max_val:.2f})")

                            # Đợi một chút sau khi tap vào Close Payment
                            time.sleep(2)

                            # Nhập mật khẩu trực tiếp
                            print(f"🔑 Nhập mật khẩu trực tiếp từ MainForm.py: {password[:2]}{'*' * (len(password) - 2)}")

                            # Phương pháp 3: Sử dụng adb shell input events để gửi từng ký tự
                            try:
                                print("🔄 Thử phương pháp 3: Sử dụng adb shell input events")
                                # Xóa trường nhập liệu trước khi nhập mật khẩu mới
                                for _ in range(20):  # Xóa 20 ký tự để đảm bảo trường trống
                                    subprocess.run([ADB_PATH, "-s", serial, "shell", "input", "keyevent", "67"], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)  # KEYCODE_DEL = 67
                                    time.sleep(0.3)

                                # Nhập từng ký tự với độ trễ lớn hơn
                                clean_password = password.replace('|', '')
                                print(f"🔑 Nhập mật khẩu (độ dài: {len(clean_password)} ký tự): {clean_password[:2]}{'*' * (len(clean_password) - 2)}")

                                for char in clean_password:
                                    # Sử dụng lệnh input text cho từng ký tự
                                    subprocess.run([ADB_PATH, "-s", serial, "shell", "input", "text", char], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                                    # Đợi lâu hơn giữa các ký tự
                                    time.sleep(0.5)
                                    print(f"  - Đã nhập ký tự: {char if char in '0123456789' else '*'}")

                                print(f"✅ Đã gửi mật khẩu thành công (phương pháp 3)")
                            except Exception as e3:
                                print(f"❌ Lỗi khi gửi mật khẩu (phương pháp 3): {str(e3)}")

                                # Phương pháp 1: Gửi toàn bộ văn bản cùng một lúc
                                try:
                                    print("🔄 Thử phương pháp 1: Gửi toàn bộ văn bản cùng một lúc")
                                    subprocess.run([ADB_PATH, "-s", serial, "shell", "input", "text", password.replace('|', '')], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                                    print(f"✅ Đã gửi mật khẩu thành công (phương pháp 1)")
                                except Exception as e1:
                                    print(f"❌ Lỗi khi gửi mật khẩu (phương pháp 1): {str(e1)}")

                                    # Phương pháp 2: Gửi từng ký tự một
                                    try:
                                        print("🔄 Thử phương pháp 2: Gửi từng ký tự một")
                                        for char in password.replace('|', ''):
                                            subprocess.run([ADB_PATH, "-s", serial, "shell", "input", "text", char], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                                            time.sleep(0.3)
                                        print(f"✅ Đã gửi mật khẩu thành công (phương pháp 2)")
                                    except Exception as e2:
                                        print(f"❌ Lỗi khi gửi mật khẩu (phương pháp 2): {str(e2)}")

                            # Đợi một chút sau khi nhập mật khẩu
                            time.sleep(2)

                            # Nhấn Enter để xác nhận
                            subprocess.run([ADB_PATH, "-s", serial, "shell", "input", "keyevent", "66"], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                            print("✅ Đã nhấn Enter để xác nhận mật khẩu")

                            # Đợi xác thực
                            time.sleep(5)

                            return True
                        else:
                            print(f"❌ Không tìm thấy close_payment.png (max_val = {max_val:.2f})")
                            return False

                    except Exception as e:
                        print(f"❌ Lỗi khi nhập mật khẩu trực tiếp: {str(e)}")
                        return False

                # Tạo một thread để nhập mật khẩu sau khi tìm thấy close_payment.png
                import threading
                password_thread = threading.Thread(target=input_password_after_close_payment, args=(serial, password))
                password_thread.daemon = True
                password_thread.start()

                # Lấy mã xác thực từ cột authen (cột 3) nếu có
                auth_secret = None
                auth_item = self.ui.tableWidget.item(row, 3)
                if auth_item and auth_item.text().strip():
                    auth_secret = auth_item.text().strip()

                # Tạo đối tượng DeviceActions và truyền mật khẩu + auth_secret trực tiếp vào constructor
                device = DeviceActions(serial, device_index=0, total_devices=len(selected_devices), adb_path=ADB_PATH, password=password, auth_secret=auth_secret)
                print(f"✅ Đã truyền mật khẩu vào đối tượng DeviceActions: {password[:2]}{'*' * (len(password) - 2) if len(password) > 2 else '*' * len(password)}")
                if auth_secret:
                    print(f"✅ Đã truyền auth_secret vào đối tượng DeviceActions: {auth_secret[:4]}{'*' * (len(auth_secret) - 4) if len(auth_secret) > 4 else '*' * len(auth_secret)}")

                # Tạo hàm kiểm tra cờ dừng để truyền vào đối tượng DeviceActions
                def check_stop_flag():
                    # Cập nhật giao diện ngay lập tức để đảm bảo cờ dừng được cập nhật
                    QApplication.processEvents()
                    # Kiểm tra cờ dừng
                    return self.stop_closing_payment

                # Sử dụng phương thức execute_with_loop (1 lần duy nhất, bỏ retry toàn cục)
                result = device.execute_with_loop(
                    chrome_template_path,
                    0.7,  # Giảm ngưỡng xuống 0.7 để dễ nhận diện hơn
                    continue_template_path,
                    yes_im_in_template_path,
                    got_it_template_path,
                    search_template_path,
                    ver_that_template_path,
                    ok_template_path,
                    try_another_way_template_path,
                    continue2_template_path,
                    menu_template_path,
                    setting_template_path,
                    close_payment_template_path,
                    continue_ver_template_path,
                    why_are_you_template_path,
                    i_dont_want_template_path,
                    i_have_had_template_path,
                    check_stop_flag  # Hàm kiểm tra cờ dừng
                )

                if result:
                    status = "✅ Đã đóng hồ sơ thành công"
                else:
                    status = "❌ Không thể mở Chrome"

            except Exception as e:
                status = f"❌ Lỗi đóng hồ sơ: {str(e)}"

            # Cập nhật trạng thái
            status_item = self.ui.tableWidget.item(row, 5)
            if status_item:
                status_item.setText(status)
            self.log_message(f"{status} - Thiết bị {serial} (dòng {row + 1})")
            return status

        # Đặt trạng thái đang đóng hồ sơ
        self.is_closing_payment = True
        self.stop_closing_payment = False

        # Chuyển button sang trạng thái stop (hiển thị stop icon)
        self.ui.close_payment_Button.setChecked(True)

        # Cập nhật giao diện ngay lập tức
        QApplication.processEvents()
        self.log_message("Đã bắt đầu quá trình đóng hồ sơ.")

        # Tạo một luồng riêng biệt để xử lý tất cả các thiết bị
        # Điều này giúp tránh đóng băng giao diện người dùng
        from PyQt5.QtCore import QThread, pyqtSignal

        class ClosePaymentThread(QThread):
            finished = pyqtSignal()
            progress = pyqtSignal(str)

            def __init__(self, parent, selected_devices, process_device_func):
                super().__init__(parent)
                self.selected_devices = selected_devices
                self.process_device_func = process_device_func
                self.parent_ref = parent  # Tham chiếu đến parent để kiểm tra stop flag

            def run(self):
                try:
                    # Sử dụng ThreadPoolExecutor để chạy tất cả các thiết bị đồng thời
                    with concurrent.futures.ThreadPoolExecutor(max_workers=len(self.selected_devices)) as executor:
                        # Bắt đầu tất cả các tác vụ
                        future_to_device = {executor.submit(self.process_device_func, device): device for device in self.selected_devices}

                        # Chờ tất cả các tác vụ hoàn thành với kiểm tra stop flag
                        for future in concurrent.futures.as_completed(future_to_device):
                            # Kiểm tra stop flag trước khi xử lý kết quả
                            if hasattr(self.parent_ref, 'stop_closing_payment') and self.parent_ref.stop_closing_payment:
                                self.progress.emit("⚠️ Đã nhận tín hiệu dừng - Đang hủy các tác vụ còn lại...")

                                # Hủy tất cả các future còn lại
                                for remaining_future in future_to_device:
                                    if not remaining_future.done():
                                        remaining_future.cancel()

                                # Thoát khỏi vòng lặp
                                break

                            # Cập nhật tiến trình
                            self.progress.emit("Đang xử lý thiết bị...")

                            # Kiểm tra xem có lỗi nào không
                            try:
                                future.result()
                            except Exception as e:
                                self.progress.emit(f"Lỗi: {str(e)}")

                except Exception as e:
                    self.progress.emit(f"Lỗi trong ClosePaymentThread: {str(e)}")
                finally:
                    # Phát tín hiệu hoàn thành
                    self.finished.emit()

        # Hàm xử lý khi luồng hoàn thành
        def on_thread_finished():
            # Đặt lại trạng thái
            self.is_closing_payment = False
            self.stop_closing_payment = False

            # Reset button về trạng thái bình thường
            self.ui.close_payment_Button.setChecked(False)

            # Cập nhật trạng thái hoàn thành cho tất cả thiết bị đã chọn
            selected_devices = self.get_selected_devices()
            for row, _ in selected_devices:
                status_item = self.ui.tableWidget.item(row, 5)
                if status_item:
                    status_item.setText("✅ Đã hoàn thành close payment")

            # Cập nhật giao diện ngay lập tức - gọi nhiều lần để đảm bảo
            QApplication.processEvents()
            QApplication.processEvents()  # Gọi 2 lần để đảm bảo

            # Force repaint table
            self.ui.tableWidget.repaint()

            self.log_message("✅ Quá trình đóng hồ sơ đã hoàn thành")

            # Log thông báo hoàn thành (không hiển thị message box)
            if self.stop_closing_payment:
                self.log_message("⚠️ Quá trình đóng hồ sơ đã bị dừng theo yêu cầu")
            else:
                self.log_message("✅ Đã hoàn thành xử lý tất cả các thiết bị!")

        # Hàm xử lý khi có tiến trình mới
        def on_progress(message):
            self.log_message(message)
            # Cập nhật giao diện ngay lập tức
            QApplication.processEvents()

        # Tạo và khởi động luồng
        self.close_payment_thread = ClosePaymentThread(self, selected_devices, process_device)
        self.close_payment_thread.finished.connect(on_thread_finished)
        self.close_payment_thread.progress.connect(on_progress)
        self.close_payment_thread.start()

        # Đợi thread hoàn thành (để tránh chạy song song với action tiếp theo)
        self.log_message(f"⏳ Đang đợi {len(selected_devices)} thiết bị hoàn thành close_payment...")
        while self.close_payment_thread.isRunning():
            # Kiểm tra cờ dừng mỗi giây
            if self.stop_closing_payment:
                self.log_message("⚠️ Đã dừng đợi close_payment theo yêu cầu")
                return
            self.close_payment_thread.wait(1000)  # Đợi 1 giây
            QApplication.processEvents()
        self.log_message("✅ Tất cả thiết bị đã hoàn thành close_payment")

    #cleanup khi đóng ứng dụng

    # HÀM KHÔNG SỬ DỤNG - CÓ THỂ XÓA
    # def restart_adb_server(self):
    #     """
    #     Khởi động lại ADB server.
    #     :return: True nếu thành công, False nếu thất bại.
    #     """
    #     try:
    #         self.log_message("Đang khởi động lại ADB server...")

    #         # Tắt server ADB
    #         subprocess.run([ADB_PATH, "kill-server"],
    #                     stdout=subprocess.DEVNULL,
    #                     stderr=subprocess.DEVNULL,
    #                     creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
    #         time.sleep(1)

    #         # Khởi động lại server ADB
    #         subprocess.run([ADB_PATH, "start-server"],
    #                     stdout=subprocess.DEVNULL,
    #                     stderr=subprocess.DEVNULL,
    #                     creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
    #         time.sleep(1)

    #         self.log_message("✅ Đã khởi động lại ADB server")
    #         return True
    #     except Exception as e:
    #         self.log_message(f"❌ Lỗi khi khởi động lại ADB server: {str(e)}")
    #         return False

    # HÀM KHÔNG SỬ DỤNG - CÓ THỂ XÓA
    # def request_device_authorization(self, serial):
    #     """
    #     Yêu cầu ủy quyền thiết bị bằng cách gửi lệnh ADB.
    #     :param serial: Số serial của thiết bị.
    #     :return: True nếu thành công, False nếu thất bại.
    #     """
    #     try:
    #         self.log_message(f"Đang yêu cầu ủy quyền thiết bị {serial}...")

    #         # Gửi lệnh ADB để yêu cầu ủy quyền
    #         subprocess.run([ADB_PATH, "-s", serial, "shell", "echo", "Requesting authorization"],
    #                     stdout=subprocess.DEVNULL,
    #                     stderr=subprocess.DEVNULL,
    #                     creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

    #         # Đợi một chút để thiết bị hiển thị hộp thoại xác nhận
    #         time.sleep(1)

    #         # Kiểm tra lại trạng thái ủy quyền
    #         return self.check_device_authorization(serial)
    #     except Exception as e:
    #         self.log_message(f"❌ Lỗi khi yêu cầu ủy quyền thiết bị {serial}: {str(e)}")
    #         return False

    def check_device_authorization(self, serial):
        """
        Kiểm tra xem thiết bị có được ủy quyền không.
        :param serial: Số serial của thiết bị.
        :return: True nếu thiết bị đã được ủy quyền, False nếu chưa.
        """
        try:
            # Kiểm tra trạng thái thiết bị
            result = subprocess.run([ADB_PATH, "devices"],
                capture_output=True,
                text=True,
                creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

            # Phân tích kết quả
            output = result.stdout.strip()
            lines = output.split('\n')

            # Tìm thiết bị trong danh sách
            for line in lines:
                if serial in line:
                    # Kiểm tra xem thiết bị có được ủy quyền không
                    if "unauthorized" in line:
                        self.log_message(f"⚠️ Thiết bị {serial} chưa được ủy quyền")
                        return False
                    elif "device" in line:
                        self.log_message(f"✅ Thiết bị {serial} đã được ủy quyền")
                        return True

            # Nếu không tìm thấy thiết bị
            self.log_message(f"⚠️ Không tìm thấy thiết bị {serial} trong danh sách thiết bị")
            return False
        except Exception as e:
            self.log_message(f"❌ Lỗi khi kiểm tra trạng thái ủy quyền của thiết bị {serial}: {str(e)}")
            return False

    def get_device_resolution(self, serial):
        """
        Lấy độ phân giải màn hình của thiết bị Android.
        :param serial: Số serial của thiết bị.
        :return: Tuple (width, height) hoặc None nếu không lấy được.
        """
        try:
            # Kiểm tra xem thiết bị có được ủy quyền không
            if not self.check_device_authorization(serial):
                self.log_message(f"⚠️ Không thể lấy độ phân giải thiết bị {serial} vì thiết bị chưa được ủy quyền")
                return (1080, 1920)

            # Lấy thông tin kích thước màn hình từ thiết bị sử dụng đường dẫn ADB đã tìm thấy
            result = subprocess.run([ADB_PATH, "-s", serial, "shell", "wm", "size"],
                capture_output=True,
                text=True,
                check=True,
                creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

            # Phân tích kết quả để lấy kích thước
            # Kết quả thường có dạng: "Physical size: 1080x1920"
            output = result.stdout.strip()
            if "size:" in output:
                size_part = output.split("size:")[-1].strip()
                if "x" in size_part:
                    width, height = map(int, size_part.split("x"))
                    self.log_message(f"✅ Đã lấy độ phân giải thiết bị {serial}: {width}x{height}")
                    return (width, height)

            # Nếu không lấy được kích thước, trả về giá trị mặc định
            self.log_message(f"⚠️ Không thể lấy độ phân giải thiết bị {serial}, sử dụng giá trị mặc định: 1080x1920")
            return (1080, 1920)
        except Exception as e:
            self.log_message(f"❌ Lỗi khi lấy độ phân giải thiết bị {serial}: {str(e)}")
            # Trả về giá trị mặc định nếu có lỗi
            return (1080, 1920)

    def view_device_screen(self):
        """
        Hiển thị màn hình thiết bị bằng scrcpy.
        """
        # Tắt tất cả các cửa sổ scrcpy đang chạy trước khi mở cửa sổ mới
        self.log_message("Đang tắt tất cả các cửa sổ scrcpy đang chạy...")
        try:
            # Tắt tất cả các tiến trình scrcpy
            subprocess.run(["taskkill", "/F", "/IM", "scrcpy.exe"],
                          stdout=subprocess.DEVNULL,
                          stderr=subprocess.DEVNULL,
                          creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
            self.log_message("✅ Đã tắt tất cả các cửa sổ scrcpy đang chạy")
        except Exception as e:
            self.log_message(f"⚠️ Lỗi khi tắt các cửa sổ scrcpy: {str(e)}")

        # Kiểm tra xem có thiết bị nào được chọn không
        selected_devices = []
        for row in range(self.ui.tableWidget.rowCount()):
            widget = self.ui.tableWidget.cellWidget(row, 0)
            if widget:
                checkbox = widget.findChild(QCheckBox)
                if checkbox and checkbox.isChecked():
                    serial_item = self.ui.tableWidget.item(row, 1)
                    if serial_item:
                        selected_devices.append((row, serial_item.text().strip()))

        if not selected_devices:
            self.log_message("⚠️ Vui lòng chọn ít nhất một thiết bị!")
            return

        # Kiểm tra lại đường dẫn scrcpy (để đảm bảo luôn tìm thấy khi chuyển máy)
        global SCRCPY_PATH
        if SCRCPY_PATH is None or not os.path.exists(SCRCPY_PATH):
            # Tìm scrcpy.exe trong thư mục scrcpy
            SCRCPY_PATH = get_scrcpy_path("scrcpy.exe")
            self.log_message(f"Đang tìm lại đường dẫn scrcpy.exe: {SCRCPY_PATH}")

            # Nếu không tìm thấy scrcpy.exe, thử tìm thư mục scrcpy
            if SCRCPY_PATH is None:
                scrcpy_dir = get_scrcpy_path()
                if scrcpy_dir:
                    # Tìm scrcpy.exe trong thư mục scrcpy
                    SCRCPY_PATH = os.path.join(scrcpy_dir, "scrcpy.exe")
                    if os.path.exists(SCRCPY_PATH):
                        self.log_message(f"✅ Đã tìm thấy scrcpy.exe tại: {SCRCPY_PATH}")
                    else:
                        # Tìm kiếm trong các thư mục con
                        self.log_message(f"⚠️ Không tìm thấy scrcpy.exe trong thư mục gốc {scrcpy_dir}, tìm kiếm trong thư mục con...")
                        found = False
                        for root, _, files in os.walk(scrcpy_dir):
                            if "scrcpy.exe" in files:
                                SCRCPY_PATH = os.path.join(root, "scrcpy.exe")
                                self.log_message(f"✅ Đã tìm thấy scrcpy.exe tại: {SCRCPY_PATH} (trong thư mục con)")
                                found = True
                                break

                        if not found:
                            self.log_message(f"⚠️ Không tìm thấy scrcpy.exe trong thư mục {scrcpy_dir} và các thư mục con")
                            SCRCPY_PATH = None

        # Kiểm tra xem scrcpy có sẵn không
        if SCRCPY_PATH is None:
            # Hiển thị thông báo lỗi
            self.log_message("❌ Không tìm thấy scrcpy. Vui lòng cài đặt scrcpy và thử lại.")
            return

        # Lấy giá trị từ thanh trượt kích thước (nếu có)
        try:
            # Kiểm tra xem thanh trượt có tồn tại không
            if hasattr(self.ui, 'horizontalSlider') and self.ui.horizontalSlider is not None:
                # Lấy giá trị từ thanh trượt (0-100)
                slider_value = self.ui.horizontalSlider.value()
                # Chuyển đổi giá trị thanh trượt thành tỷ lệ kích thước (0.3-1.0)
                size_ratio = 0.3 + (slider_value / 100.0) * 0.7
                # Làm tròn đến 2 chữ số thập phân
                size_ratio = round(size_ratio, 2)
            else:
                # Nếu không có thanh trượt, sử dụng giá trị mặc định
                size_ratio = 0.8

            # Ghi log giá trị kích thước
            self.log_message(f"Sử dụng tỷ lệ kích thước: {size_ratio}")
        except Exception as e:
            # Nếu có lỗi, sử dụng giá trị mặc định
            size_ratio = 0.8
            self.log_message(f"Lỗi khi lấy giá trị thanh trượt: {str(e)}, sử dụng giá trị mặc định: {size_ratio}")

        # Tính toán kích thước cửa sổ dựa trên giá trị thanh trượt
        # Phạm vi chiều rộng từ 200 đến 518 pixel
        min_width = 200
        max_width = 518
        width_range = max_width - min_width

        # Tính toán chiều rộng dựa trên giá trị thanh trượt (0-100)
        slider_value = 0
        if hasattr(self.ui, 'horizontalSlider'):
            slider_value = self.ui.horizontalSlider.value()
        window_width = min_width + int((slider_value / 100.0) * width_range)

        # Lấy số lượng cửa sổ trên một hàng từ spinBox_view
        max_windows_per_row = 5  # Giá trị mặc định
        if hasattr(self.ui, 'spinBox_view'):
            max_windows_per_row = self.ui.spinBox_view.value()
            self.log_message(f"📱 Số thiết bị trên 1 hàng: {max_windows_per_row}")

        # Khoảng cách cố định giữa các cửa sổ
        window_spacing_x = 5  # Khoảng cách ngang giữa các cửa sổ
        window_spacing_y = 35  # Khoảng cách dọc giữa các hàng (tăng lên để tránh chồng lấp)

        # Tính toán vị trí ban đầu (góc trên bên trái)
        start_x = 5
        start_y = 35

        # Mở tất cả các thiết bị được chọn
        for i, (row, serial) in enumerate(selected_devices):
            try:
                # Cập nhật trạng thái
                status_item = self.ui.tableWidget.item(row, 5)
                if status_item:
                    status_item.setText(f"Đang view thiết bị (kích thước: {int(size_ratio*100)}%)...")
                    QApplication.processEvents()

                # Kiểm tra xem thiết bị có được ủy quyền không
                if not self.check_device_authorization(serial):
                    # Hiển thị thông báo lỗi
                    error_msg = f"Thiết bị {serial} chưa được ủy quyền"
                    self.log_message(f"⚠️ {error_msg}")
                    if status_item:
                        status_item.setText(f"❌ Không tìm thấy thiết bị {serial}")

                    # Thử khởi động lại ADB server
                    self.restart_adb_server()

                    # Thử yêu cầu ủy quyền thiết bị
                    if self.request_device_authorization(serial):
                        self.log_message(f"✅ Đã tìm thấy T bị {serial} được kết nối")
                        if status_item:
                            status_item.setText(f"Đang mở scrcpy (kích thước: {int(size_ratio*100)}%)...")
                            QApplication.processEvents()
                    else:
                        # Ghi log thông báo lỗi
                        self.log_message(f"❌ Thiết bị {serial} chưa được ủy quyền. Vui lòng kiểm tra lại thiết bị.")

                        # Thử lại một lần
                        if self.request_device_authorization(serial):
                            self.log_message(f"✅ Đã tìm thấy TB {serial} thành công sau khi thử lại")
                            if status_item:
                                status_item.setText(f"Đang view thiết bị (kích thước: {int(size_ratio*100)}%)...")
                                QApplication.processEvents()
                        else:
                            self.log_message(f"❌ Không thể ủy quyền thiết bị {serial} sau khi thử lại")
                            continue  # Bỏ qua thiết bị này và tiếp tục với thiết bị tiếp theo

                # Lấy độ phân giải của thiết bị
                device_width, device_height = self.get_device_resolution(serial)

                # Tính toán tỷ lệ khung hình thực tế của thiết bị
                device_ratio = device_height / device_width

                # Tính toán lại chiều cao cửa sổ dựa trên tỷ lệ khung hình thực tế
                adjusted_height = int(window_width * device_ratio)

                # Tính toán vị trí cho cửa sổ hiện tại
                current_row = i // max_windows_per_row  # Hàng hiện tại
                current_col = i % max_windows_per_row   # Cột hiện tại

                # Tính toán tọa độ x, y với khoảng cách giữa các cửa sổ
                x_position = start_x + current_col * (window_width + window_spacing_x)
                y_position = start_y + current_row * (adjusted_height + window_spacing_y)

                # Chuẩn bị môi trường để chạy scrcpy
                env = os.environ.copy()

                # Thêm thư mục chứa scrcpy.exe vào PATH để đảm bảo tìm thấy các DLL
                scrcpy_dir = os.path.dirname(SCRCPY_PATH)
                if scrcpy_dir:
                    if "PATH" in env:
                        env["PATH"] = scrcpy_dir + os.pathsep + env["PATH"]
                    else:
                        env["PATH"] = scrcpy_dir

                # Chạy scrcpy với thiết bị đã chọn
                # Tạo lệnh scrcpy với các tùy chọn
                cmd = [
                    SCRCPY_PATH,  # Sử dụng đường dẫn scrcpy đã tìm thấy
                    "-s", serial,
                    "--window-title", f"Màn hình thiết bị {serial}",
                    "--window-width", str(window_width),  # Đặt chiều rộng cửa sổ cố định
                    "--window-height", str(adjusted_height),  # Chiều cao điều chỉnh theo tỷ lệ thực tế
                    "--window-x", str(x_position),  # Vị trí X của cửa sổ
                    "--window-y", str(y_position),   # Vị trí Y của cửa sổ
                    "--max-fps", "60",  # Tăng FPS tối đa
                    "--crop", f"{device_width}:{device_height}:0:0"  # Cắt bỏ viền đen với độ phân giải thực tế
                ]

                try:
                    # Chạy scrcpy trong một tiến trình riêng biệt với đầy đủ thông tin lỗi
                    # Sử dụng biến môi trường đã chuẩn bị để đảm bảo tìm thấy các DLL
                    process = subprocess.Popen(cmd, stderr=subprocess.PIPE, stdout=subprocess.PIPE, text=True,
                                             env=env,
                                             creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

                    # Đợi một chút để xem có lỗi ngay lập tức không
                    time.sleep(1)

                    # Kiểm tra xem tiến trình đã kết thúc chưa
                    if process.poll() is not None:
                        # Tiến trình đã kết thúc, có thể có lỗi
                        _, stderr = process.communicate()
                        if process.returncode != 0:
                            error_msg = f"Lỗi khi chạy scrcpy: {stderr}"
                            self.log_message(f"❌ {error_msg}")
                            if status_item:
                                status_item.setText(f"❌ Lỗi scrcpy")

                            # Kiểm tra xem lỗi có phải là "unauthorized" không
                            if "unauthorized" in stderr:
                                # Ghi log thông báo lỗi
                                self.log_message(f"❌ Thiết bị {serial} chưa được ủy quyền. Vui lòng kiểm tra lại thiết bị và mở Doma nếu chưa mở.")

                                # Thử khởi động lại ADB server
                                try:
                                    if ADB_PATH and os.path.exists(ADB_PATH):
                                        self.log_message("Đang thử khởi động lại ADB server...")
                                        # Tắt server ADB
                                        subprocess.run([ADB_PATH, "kill-server"],
                                                    stdout=subprocess.DEVNULL,
                                                    stderr=subprocess.DEVNULL,
                                                    creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                                        time.sleep(1)

                                        # Khởi động lại server ADB
                                        subprocess.run([ADB_PATH, "start-server"],
                                                    stdout=subprocess.DEVNULL,
                                                    stderr=subprocess.DEVNULL,
                                                    creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                                        self.log_message("Đã khởi động lại ADB server. Vui lòng kiểm tra thiết bị và thử lại.")
                                except Exception as e:
                                    self.log_message(f"Lỗi khi khởi động lại ADB server: {str(e)}")
                            else:
                                # Ghi log thông báo lỗi
                                self.log_message(f"❌ Lỗi khi chạy scrcpy cho thiết bị {serial}: 1. Kiểm tra lại kết nối USB 2. Thử ngắt kết nối và kết nối lại thiết bị")
                            continue  # Tiếp tục với thiết bị tiếp theo

                    # Cập nhật trạng thái thành công
                    if status_item:
                        status_item.setText("✅ Đã view thiết bị")
                    self.log_message(f"✅ Đã view thiết bị {serial} (dòng {row + 1})")

                except Exception as e:
                    error_msg = f"Lỗi khi chạy scrcpy: {str(e)}"
                    self.log_message(f"❌ {error_msg}")
                    if status_item:
                        status_item.setText(f"❌ Lỗi scrcpy")
                    self.log_message(f"❌ Lỗi khi chạy scrcpy cho thiết bị {serial}: 1. Kiểm tra lại kết nối USB 2. Thử ngắt kết nối và kết nối lại thiết bị")

            except Exception as e:
                error_msg = f"Lỗi khi mở scrcpy: {str(e)}"
                self.log_message(f"❌ {error_msg}")
                if status_item:
                    status_item.setText(f"❌ Lỗi")
                self.log_message(f"❌ Lỗi khi mở scrcpy cho thiết bị {serial}: 1. Kiểm tra lại kết nối USB 2. Thử ngắt kết nối và kết nối lại thiết bị")

    # Phương thức save_screenshot không còn cần thiết vì ảnh đã được mở trực tiếp bằng ứng dụng mặc định
    def update_card_stats(self):
        """
        Đọc dữ liệu từ file card.txt và cập nhật các ô số
        """
        try:
            self.log_message(f"🔄 Bắt đầu cập nhật thống kê thẻ...")

            # Đường dẫn đến file card.txt sử dụng path_manager
            try:
                # Thử sử dụng get_txt_path (phương pháp đơn giản nhất)
                card_file_path = path_manager.get_txt_path("card.txt")
                self.log_message(f"🔍 Đường dẫn file card.txt từ get_txt_path: {card_file_path}")
            except Exception as e:
                self.log_message(f"⚠️ Lỗi khi sử dụng get_txt_path: {str(e)}")
                # Sử dụng get_data_path như phương án dự phòng
                card_file_path = path_manager.get_data_path("card.txt")
                self.log_message(f"🔍 Đường dẫn file card.txt từ get_data_path: {card_file_path}")

            # Kiểm tra xem file có tồn tại không
            if not os.path.exists(card_file_path):
                self.log_message(f"⚠️ Không tìm thấy file {card_file_path}")
                return

            # Đọc dữ liệu từ file
            with open(card_file_path, "r") as file:
                lines = file.readlines()

            # Tính toán các thống kê
            total_cards = 0
            processed_cards = 0
            success_cards = 0
            error_cards = 0
            expired_cards = 0

            for line in lines:
                line = line.strip()
                if not line or line.startswith("#"):
                    continue

                total_cards += 1

                # Kiểm tra xem thẻ đã được xử lý chưa bằng cách phân tích cấu trúc thẻ
                # Chia thẻ thành các phần theo dấu |
                parts = line.split("|")
                if len(parts) >= 4:
                    # Phần cuối cùng có thể chứa thông tin trạng thái
                    last_part = parts[-1]

                    # Kiểm tra xem phần cuối có chứa các hậu tố không
                    if "-ok" in last_part or "-error" in last_part or "-expired" in last_part or "-used" in last_part:
                        processed_cards += 1

                        # Kiểm tra kết quả xử lý
                        if "-ok" in last_part:
                            success_cards += 1
                        elif "-error" in last_part:
                            error_cards += 1
                        elif "-expired" in last_part:
                            expired_cards += 1
                else:
                    # Nếu không thể chia thành các phần, sử dụng cách kiểm tra đơn giản hơn
                    if "-ok" in line:
                        processed_cards += 1
                        success_cards += 1
                    elif "-error" in line:
                        processed_cards += 1
                        error_cards += 1
                    elif "-expired" in line:
                        processed_cards += 1
                        expired_cards += 1
                    elif "-used" in line:
                        processed_cards += 1

            # Cập nhật các ô số
            if hasattr(self.ui, 'label_total_card'):
                self.ui.label_total_card.setText(str(total_cards))

            if hasattr(self.ui, 'label_processed'):
                self.ui.label_processed.setText(str(processed_cards))

            if hasattr(self.ui, 'label_Success'):
                self.ui.label_Success.setText(str(success_cards))

            if hasattr(self.ui, 'label_Error'):
                self.ui.label_Error.setText(str(error_cards))

            if hasattr(self.ui, 'label_expired'):
                self.ui.label_expired.setText(str(expired_cards))

            # Cập nhật giao diện ngay lập tức
            QApplication.processEvents()

            return True

        except Exception as e:
            self.log_message(f"❌ Lỗi khi cập nhật thống kê thẻ: {str(e)}")
            import traceback
            self.log_message(f"❌ Chi tiết lỗi: {traceback.format_exc()}")
            return False

    def open_mail_file(self):
        """Mở và xử lý file mail.txt"""
        try:
            # Đường dẫn đến file mail.txt sử dụng path_manager
            try:
                # Thử sử dụng get_txt_path (phương pháp đơn giản nhất)
                mail_file_path = path_manager.get_txt_path("mail.txt")
                self.log_message(f"🔍 Đường dẫn file mail.txt từ get_txt_path: {mail_file_path}")
            except Exception as e:
                self.log_message(f"⚠️ Lỗi khi sử dụng get_txt_path: {str(e)}")
                # Sử dụng get_data_path như phương án dự phòng
                mail_file_path = path_manager.get_data_path("mail.txt")
                self.log_message(f"🔍 Đường dẫn file mail.txt từ get_data_path: {mail_file_path}")

            # Kiểm tra xem thư mục data có tồn tại không
            data_dir = os.path.dirname(mail_file_path)
            if not os.path.exists(data_dir):
                os.makedirs(data_dir)
                self.log_message(f"✅ Đã tạo thư mục {data_dir}")

            # Kiểm tra xem file mail.txt có tồn tại không
            if not os.path.exists(mail_file_path):
                # Nếu file không tồn tại, tạo file mới
                with open(mail_file_path, "w") as file:
                    file.write("# Danh sách email\n")
                print(f"Đã tạo file {mail_file_path}")

            # Mở file mail.txt bằng ứng dụng mặc định
            try:
                # Sử dụng webbrowser để mở file - hoạt động trên tất cả các hệ điều hành
                webbrowser.open(mail_file_path)
                self.log_message(f"📂 Đã mở file {mail_file_path}")
            except Exception as e:
                self.log_message(f"❌ Lỗi khi mở file: {str(e)}")

        except Exception as e:
            QMessageBox.critical(self, "Lỗi", f"Không thể mở file mail.txt: {str(e)}")
            print(f"Lỗi khi mở file mail.txt: {str(e)}")

    def open_card_file(self):
        """Mở và xử lý file card.txt"""
        try:
            # Đường dẫn đến file card.txt sử dụng path_manager
            card_file_path = path_manager.get_data_path("card.txt")

            # Kiểm tra xem thư mục data có tồn tại không
            data_dir = os.path.dirname(card_file_path)
            if not os.path.exists(data_dir):
                os.makedirs(data_dir)
                print(f"Đã tạo thư mục {data_dir}")

            # Kiểm tra xem file card.txt có tồn tại không
            if not os.path.exists(card_file_path):
                # Nếu file không tồn tại, tạo file mới
                with open(card_file_path, "w") as file:
                    file.write("# Danh sách thẻ (format: số thẻ|tháng|năm|cvv)\n")
                print(f"Đã tạo file {card_file_path}")

            # Mở file card.txt bằng ứng dụng mặc định
            try:
                # Sử dụng webbrowser để mở file - hoạt động trên tất cả các hệ điều hành
                webbrowser.open(card_file_path)
                self.log_message(f"📂 Đã mở file {card_file_path}")
            except Exception as e:
                self.log_message(f"❌ Lỗi khi mở file: {str(e)}")

        except Exception as e:
            QMessageBox.critical(self, "Lỗi", f"Không thể mở file card.txt: {str(e)}")
            print(f"Lỗi khi mở file card.txt: {str(e)}")

    def open_data_folder(self):
        """Mở thư mục data"""
        try:
            # Lấy đường dẫn đến thư mục data
            data_dir = path_manager.get_data_path("")

            # Kiểm tra xem thư mục data có tồn tại không
            if not os.path.exists(data_dir):
                os.makedirs(data_dir)
                self.log_message(f"✅ Đã tạo thư mục {data_dir}")

            # Mở thư mục data bằng ứng dụng mặc định
            try:
                # Sử dụng webbrowser để mở thư mục - hoạt động trên tất cả các hệ điều hành
                webbrowser.open(data_dir)
                self.log_message(f"📂 Đã mở thư mục {data_dir}")
            except Exception as e:
                self.log_message(f"❌ Lỗi khi mở thư mục: {str(e)}")

                # Thử phương pháp thay thế nếu webbrowser không hoạt động
                try:
                    if sys.platform == 'win32':
                        # Sử dụng explorer trên Windows
                        os.startfile(data_dir)
                    # Các phương pháp thay thế cho các hệ điều hành khác
                    # (Đã bỏ code không thể truy cập)
                    self.log_message(f"📂 Đã mở thư mục {data_dir} bằng phương pháp thay thế")
                except Exception as e2:
                    self.log_message(f"❌ Lỗi khi mở thư mục bằng phương pháp thay thế: {str(e2)}")
                    QMessageBox.critical(self, "Lỗi", f"Không thể mở thư mục data: {str(e2)}")

        except Exception as e:
            self.log_message(f"❌ Lỗi khi mở thư mục data: {str(e)}")
            QMessageBox.critical(self, "Lỗi", f"Không thể mở thư mục data: {str(e)}")

    def show_license_manager(self):
        """Hiển thị License Manager Dialog"""
        try:
            dialog = LicenseManagerDialog(self)
            result = dialog.exec_()

            if result == QDialog.Accepted:
                # License đã được lưu, cập nhật lại thông tin ứng dụng
                settings = QSettings("AutoK", "License")
                saved_key = settings.value("license_key", "")

                if saved_key:
                    result = license_manager.check_license(saved_key)

                    # Xử lý kết quả trả về (có thể là 3 hoặc 4 phần tử)
                    if len(result) == 4:
                        valid, message, features, user_info = result
                    else:
                        valid, message, features = result
                        user_info = {}

                    if valid:
                        # Cập nhật lại allowed_features cho ứng dụng
                        app = QApplication.instance()
                        app.setProperty("allowed_features", features)
                        self.log_message(f"✅ Đã cập nhật license. Tính năng: {features}")

                        # Kiểm tra và cập nhật trạng thái tab proxy
                        self.check_and_update_proxy_tab_access()
                    else:
                        self.log_message(f"❌ License không hợp lệ: {message}")

        except Exception as e:
            QMessageBox.critical(self, "Lỗi", f"Không thể mở License Manager:\n{e}")
            self.log_message(f"❌ Lỗi mở License Manager: {e}")

    def check_and_update_proxy_tab_access(self):
        """Kiểm tra và cập nhật quyền truy cập tab Proxy dựa trên license"""
        try:
            app = QApplication.instance()
            allowed_features = app.property("allowed_features")

            # Kiểm tra xem có quyền sử dụng proxy không
            proxy_allowed = True  # Mặc định cho phép

            # Logic khóa tab Proxy đơn giản:
            # 1. ALL: Cho phép tab Proxy
            # 2. ALL_BLOCKED: Khóa tab Proxy
            # 3. PROXY: Khóa tab Proxy (chỉ cho phép auto functions)
            # 4. Bất kỳ giá trị khác: Cho phép tab Proxy
            if allowed_features:
                features_upper = allowed_features.upper()
                if features_upper == "ALL":
                    proxy_allowed = True
                    self.log_message("🔍 License ALL - Cho phép tab Proxy")
                elif features_upper == "ALL_BLOCKED":
                    proxy_allowed = False
                    self.log_message("🔍 License ALL_BLOCKED - Khóa tab Proxy")
                elif features_upper == "PROXY":
                    proxy_allowed = False
                    self.log_message("🔍 License PROXY - Khóa tab Proxy (chỉ cho phép auto functions)")
                else:
                    proxy_allowed = True
                    self.log_message(f"🔍 License {allowed_features} - Cho phép tab Proxy")

            # Tìm tab Proxy trong tabWidget_Proxy
            if hasattr(self.ui, 'tabWidget_Proxy'):
                proxy_tab_index = -1
                for i in range(self.ui.tabWidget_Proxy.count()):
                    if self.ui.tabWidget_Proxy.tabText(i) == "Proxy":
                        proxy_tab_index = i
                        break

                if proxy_tab_index >= 0:
                    # Vô hiệu hóa hoặc kích hoạt tab
                    self.ui.tabWidget_Proxy.setTabEnabled(proxy_tab_index, proxy_allowed)

                    if not proxy_allowed:
                        self.log_message("🔒 Tab Proxy đã bị khóa do giới hạn license")
                        # Chuyển về tab khác nếu đang ở tab Proxy
                        if self.ui.tabWidget_Proxy.currentIndex() == proxy_tab_index:
                            self.ui.tabWidget_Proxy.setCurrentIndex(0)  # Chuyển về tab đầu tiên

                        # Tắt Auto-Assign khi khóa tab Proxy
                        self.disable_auto_assign_when_proxy_blocked()
                    else:
                        self.log_message("🔓 Tab Proxy đã được kích hoạt")

        except Exception as e:
            self.log_message(f"❌ Lỗi khi cập nhật quyền truy cập tab Proxy: {e}")

    def disable_auto_assign_when_proxy_blocked(self):
        """Tắt Auto-Assign khi tab Proxy bị khóa"""
        try:
            # Tắt checkbox Auto-Assign nếu có
            if hasattr(self.ui, 'checkBox_auto_assign'):
                self.ui.checkBox_auto_assign.setChecked(False)
                self.ui.checkBox_auto_assign.setEnabled(False)
                self.log_message("🔒 Auto-Assign đã bị tắt do tab Proxy bị khóa")

            # Tắt auto assign trong proxy config nếu có
            if hasattr(self, 'proxy_config'):
                self.proxy_config['auto_assign'] = False

        except Exception as e:
            self.log_message(f"❌ Lỗi khi tắt Auto-Assign: {e}")

    def add_wallet(self):
        """Thực hiện chuỗi hành động thêm ví Google Wallet trên các thiết bị đã chọn"""
        # Kiểm tra license - chỉ ALL_BLOCKED mới bị khóa
        app = QApplication.instance()
        allowed_features = app.property("allowed_features")
        if allowed_features and allowed_features.upper() == "ALL_BLOCKED":
            QMessageBox.warning(self, "Tính năng bị giới hạn",
                               "Tính năng thêm ví đã bị khóa do giới hạn license.")
            self.log_message("❌ Tính năng thêm ví bị khóa do license ALL_BLOCKED")
            return

        # Nếu đang trong quá trình thêm ví, dừng quá trình
        if hasattr(self, 'is_adding_wallet') and self.is_adding_wallet:
            # Đặt cờ dừng và hiển thị thông báo
            self.log_message("⏹️ Đang dừng quá trình thêm ví...")
            self.stop_adding_wallet = True
            # Reset button về trạng thái bình thường
            self.ui.toolButton_wallet.setChecked(False)
            self.is_adding_wallet = False
            return

        # Kiểm tra xem có thiết bị nào được chọn không
        selected_devices = []
        for row in range(self.ui.tableWidget.rowCount()):
            widget = self.ui.tableWidget.cellWidget(row, 0)
            if widget:
                checkbox = widget.findChild(QCheckBox)
                if checkbox and checkbox.isChecked():
                    serial_item = self.ui.tableWidget.item(row, 1)
                    if serial_item:
                        selected_devices.append((row, serial_item.text().strip()))

        if not selected_devices:
            QMessageBox.warning(self, "Cảnh báo", "Vui lòng chọn ít nhất một thiết bị!")
            return

        # Kiểm tra và tạo thư mục template nếu chưa tồn tại
        template_dir = path_manager.get_template_path()
        if not os.path.exists(template_dir):
            os.makedirs(template_dir)
            self.log_message(f"✅ Đã tạo thư mục {template_dir}")

        # Cập nhật trạng thái của tất cả các thiết bị được chọn
        for row, _ in selected_devices:
            status_item = self.ui.tableWidget.item(row, 5)
            if status_item:
                status_item.setText("Đang chuẩn bị thêm ví...")
                QApplication.processEvents()

        # Đặt trạng thái đang thêm ví
        self.is_adding_wallet = True
        self.stop_adding_wallet = False

        # Chuyển button sang trạng thái stop (hiển thị stop icon)
        self.ui.toolButton_wallet.setChecked(True)

        # Cập nhật giao diện ngay lập tức
        QApplication.processEvents()
        self.log_message("🔄 Đã bắt đầu quá trình thêm ví Google Wallet.")

        # Tạo một hàm để xử lý từng thiết bị
        def process_device(row_serial_tuple):
            row, serial = row_serial_tuple
            try:
                # Kiểm tra cờ dừng trước khi bắt đầu
                if self.stop_adding_wallet:
                    status = "⚠️ Đã dừng thêm ví theo yêu cầu"
                    status_item = self.ui.tableWidget.item(row, 5)
                    if status_item:
                        status_item.setText(status)
                    self.log_message(f"{status} - Thiết bị {serial} (dòng {row + 1})")
                    return status

                # Cập nhật trạng thái
                status_item = self.ui.tableWidget.item(row, 5)
                if status_item:
                    status_item.setText("Đang thêm ví...")
                    QApplication.processEvents()

                # Lấy chỉ số thiết bị trong danh sách thiết bị được chọn
                device_index = selected_devices.index((row, serial))

                # Lấy số lượng thẻ cần thêm từ spinBox_so_lan_add_card
                num_cards = 1  # Giá trị mặc định
                if hasattr(self.ui, 'spinBox_so_lan_add_card'):
                    num_cards = self.ui.spinBox_so_lan_add_card.value()
                    self.log_message(f"Số lượng thẻ cần thêm: {num_cards}")

                # Tạo đối tượng add_wallet và thực thi hành động
                device = add_wallet(serial, num_loops=1, device_index=device_index, total_devices=len(selected_devices), adb_path=ADB_PATH)

                # Lấy giá trị giới hạn thất bại từ spinBox_error
                max_failures = 10  # Giá trị mặc định
                if hasattr(self.ui, 'spinBox_error'):
                    max_failures = self.ui.spinBox_error.value()
                    self.log_message(f"Lấy giới hạn thất bại từ spinBox_error: {max_failures}")
                else:
                    # Fallback: đọc từ config.txt nếu không có spinBox_error
                    try:
                        config_path = os.path.join("data", "config.txt")
                        if os.path.exists(config_path):
                            with open(config_path, 'r', encoding='utf-8') as f:
                                for line in f:
                                    if line.strip().startswith('max_failures='):
                                        max_failures = int(line.strip().split('=')[1])
                                        self.log_message(f"Lấy giới hạn thất bại từ config.txt: {max_failures}")
                                        break
                    except Exception as e:
                        self.log_message(f"⚠️ Không thể đọc cấu hình max_failures: {e}, sử dụng mặc định 10")

                # Thiết lập giới hạn thất bại cho device
                device.set_max_consecutive_failures(max_failures)

                # Tạo hàm cập nhật trạng thái
                def update_status():
                    if status_item:
                        # Hiển thị thông tin thẻ hiện tại
                        card_info = ""
                        if hasattr(device, 'current_card_info') and device.current_card_info:
                            card_last4 = device.current_card_info['card_number'][-4:]
                            card_info = f" - Thẻ {card_last4}"

                        # Hiển thị thông tin thất bại liên tiếp
                        failure_info = ""
                        if hasattr(device, 'consecutive_failures') and device.consecutive_failures > 0:
                            failure_info = f" (❌{device.consecutive_failures})"

                        # Hiển thị thẻ đang thêm / tổng số thẻ + thông tin thất bại
                        status_text = f"🎯 Wallet: {device.current_card}/{device.total_cards}{card_info}{failure_info}"

                        # Sử dụng queue để tránh UI freeze
                        self.queue_status_update(row, status_text)

                        # Thêm thông báo log để hiển thị trạng thái hiện tại
                        self.log_message(f"🔄 {status_text} - Thiết bị {serial}")

                # Đặt hàm cập nhật trạng thái vào device
                device.update_status_func = update_status

                # Tạo hàm kiểm tra cờ dừng để truyền vào đối tượng add_wallet
                def check_stop_flag():
                    # Force cập nhật giao diện nhiều lần để đảm bảo cờ dừng được cập nhật
                    QApplication.processEvents()
                    QApplication.processEvents()  # Double check

                    # Kiểm tra cờ dừng với debug log
                    stop_status = self.stop_adding_wallet
                    if stop_status:
                        print(f"🛑 STOP FLAG DETECTED: stop_adding_wallet = {stop_status}")
                        self.log_message(f"🛑 Đã phát hiện tín hiệu dừng - Thiết bị {serial}")

                    return stop_status

                # Thực hiện hành động với stop flag và số lượng thẻ
                success = device.execute_actions(stop_flag_func=check_stop_flag, num_cards=num_cards)

                # Kiểm tra cờ dừng sau khi thực hiện hành động
                if self.stop_adding_wallet:
                    status = "⚠️ Đã dừng thêm ví"
                    if status_item:
                        status_item.setText(status)
                    self.log_message(f"{status} - Thiết bị {serial} (dòng {row + 1})")
                    return status

                # Cập nhật trạng thái dựa trên số thẻ thành công thực tế
                successful_cards = getattr(device, 'successful_cards', 0)  # Số thẻ đã thêm thành công thực tế
                total_cards = getattr(device, 'total_cards', num_cards)  # Tổng số thẻ cần thêm
                consecutive_failures = getattr(device, 'consecutive_failures', 0)  # Số lần thất bại liên tiếp
                max_consecutive_failures = getattr(device, 'max_consecutive_failures', 10)  # Giới hạn thất bại

                # Kiểm tra kết quả dựa trên số thẻ thành công thực tế và lý do dừng
                if consecutive_failures >= max_consecutive_failures:
                    status = f"🛑 Dừng do {max_consecutive_failures} lần thất bại liên tiếp ({successful_cards}/{total_cards} thẻ)"
                elif successful_cards == total_cards and successful_cards > 0:
                    status = f"✅ Đã hoàn thành thêm ví ({successful_cards}/{total_cards} thẻ)"
                elif successful_cards > 0:
                    status = f"⚠️ Hoàn thành một phần ({successful_cards}/{total_cards} thẻ)"
                elif success:
                    # Trường hợp success=True nhưng không có thẻ nào thành công (có thể do lỗi logic)
                    status = f"⚠️ Hoàn thành nhưng không thêm được thẻ nào ({successful_cards}/{total_cards} thẻ)"
                else:
                    status = "❌ Không thể hoàn thành thêm ví"

                # Cập nhật trạng thái trong bảng với real-time update
                if status_item:
                    status_item.setText(status)
                    # Đảm bảo UI được cập nhật ngay lập tức như thông tin tiến trình
                    QApplication.processEvents()
                    # Thêm một lần cập nhật nữa để đảm bảo UI được cập nhật
                    QApplication.processEvents()
                self.log_message(f"{status} - Thiết bị {serial} (dòng {row + 1})")
                return status

            except Exception as e:
                error_msg = f"Lỗi khi thêm ví: {str(e)}"
                status_item = self.ui.tableWidget.item(row, 5)
                if status_item:
                    status_item.setText(f"❌ {error_msg}")
                    # Đảm bảo UI được cập nhật ngay lập tức
                    QApplication.processEvents()
                    QApplication.processEvents()
                self.log_message(f"❌ {error_msg} - Thiết bị {serial} (dòng {row + 1})")
                return error_msg

        # Tạo một luồng riêng biệt để xử lý tất cả các thiết bị
        from PyQt5.QtCore import QThread, pyqtSignal

        class AddWalletThread(QThread):
            finished = pyqtSignal()
            progress = pyqtSignal(str)

            def __init__(self, parent, selected_devices, process_device_func):
                super().__init__(parent)
                self.selected_devices = selected_devices
                self.process_device_func = process_device_func

            def run(self):
                try:
                    # Sử dụng ThreadPoolExecutor để chạy tất cả các thiết bị đồng thời
                    with concurrent.futures.ThreadPoolExecutor(max_workers=len(self.selected_devices)) as executor:
                        # Bắt đầu tất cả các tác vụ
                        future_to_device = {executor.submit(self.process_device_func, device): device for device in self.selected_devices}

                        # Chờ tất cả các tác vụ hoàn thành
                        for future in concurrent.futures.as_completed(future_to_device):
                            # Cập nhật tiến trình
                            self.progress.emit("Đang xử lý thiết bị...")

                            # Kiểm tra xem có lỗi nào không
                            try:
                                future.result()
                            except Exception as e:
                                self.progress.emit(f"Lỗi: {str(e)}")
                finally:
                    # Phát tín hiệu hoàn thành
                    self.finished.emit()

        # Hàm xử lý khi luồng hoàn thành
        def on_thread_finished():
            # Đặt lại trạng thái
            self.is_adding_wallet = False
            self.stop_adding_wallet = False

            # Reset button về trạng thái bình thường
            self.ui.toolButton_wallet.setChecked(False)

            # Cập nhật trạng thái hoàn thành cho tất cả thiết bị đã chọn
            try:
                selected_devices = self.get_selected_devices()
                for row, _ in selected_devices:
                    status_item = self.ui.tableWidget.item(row, 5)
                    if status_item:
                        status_item.setText("✅ Đã hoàn thành add wallet")
            except Exception as e:
                print(f"❌ Lỗi khi cập nhật trạng thái add wallet: {e}")

            # Cập nhật giao diện ngay lập tức - gọi nhiều lần để đảm bảo
            QApplication.processEvents()
            QApplication.processEvents()  # Gọi 2 lần để đảm bảo

            # Force repaint table
            self.ui.tableWidget.repaint()

            # Log thông báo hoàn thành thêm ví (không hiển thị message box)
            self.log_message("✅ Đã hoàn thành thêm ví trên tất cả các thiết bị!")

        # Hàm xử lý khi có tiến trình mới
        def on_progress(message):
            self.log_message(message)
            # Cập nhật giao diện ngay lập tức
            QApplication.processEvents()

        # Tạo và khởi động luồng
        self.add_wallet_thread = AddWalletThread(self, selected_devices, process_device)
        self.add_wallet_thread.finished.connect(on_thread_finished)
        self.add_wallet_thread.progress.connect(on_progress)
        self.add_wallet_thread.start()

        # Chạy thread trong background để không block UI
        self.log_message(f"🔄 Đã bắt đầu add_wallet trên {len(selected_devices)} thiết bị trong background...")

        # Tạo background thread để đợi add_wallet_thread hoàn thành
        import threading
        def wait_for_addwallet_async():
            try:
                # Đợi thread hoàn thành
                while self.add_wallet_thread.isRunning():
                    # Kiểm tra cờ dừng mỗi giây
                    if self.stop_adding_wallet:
                        from PyQt5.QtCore import QTimer
                        QTimer.singleShot(0, lambda: self.log_message("⚠️ Đã dừng đợi add_wallet theo yêu cầu"))
                        return
                    self.add_wallet_thread.wait(1000)  # Đợi 1 giây

                # Update UI từ main thread khi hoàn thành
                from PyQt5.QtCore import QTimer
                QTimer.singleShot(0, lambda: self.log_message("✅ Tất cả thiết bị đã hoàn thành add_wallet"))

            except Exception as e:
                from PyQt5.QtCore import QTimer
                QTimer.singleShot(0, lambda: self.log_message(f"❌ Lỗi trong wait_for_addwallet_async: {e}"))

        wait_thread = threading.Thread(target=wait_for_addwallet_async, daemon=True)
        wait_thread.start()

    def add_wallet_sync(self):
        """Phiên bản đồng bộ của add_wallet() - đợi hoàn thành trước khi return (dùng trong action sequence)"""
        # Gọi add_wallet() gốc và đợi hoàn thành
        self.add_wallet()

        # Đợi thread hoàn thành
        if hasattr(self, 'add_wallet_thread'):
            self.log_message("⏳ Đang đợi add_wallet hoàn thành...")

            while self.add_wallet_thread.isRunning():
                # Kiểm tra cờ dừng
                if self.stop_adding_wallet or self.stop_start_actions:
                    self.log_message("⚠️ Đã nhận tín hiệu dừng - Dừng add_wallet sync")
                    break

                # Đợi và cập nhật UI
                self.add_wallet_thread.wait(1000)  # Đợi 1 giây
                QApplication.processEvents()

            self.log_message("✅ Add_wallet đã hoàn thành, tiếp tục với action tiếp theo")

    def mua_game(self):
        """
        Thực hiện chuỗi hành động mua game trên các thiết bị đã chọn hoặc dừng quá trình nếu đang chạy.
        """
        # Kiểm tra license - chỉ ALL_BLOCKED mới bị khóa
        app = QApplication.instance()
        allowed_features = app.property("allowed_features")
        if allowed_features and allowed_features.upper() == "ALL_BLOCKED":
            QMessageBox.warning(self, "Tính năng bị giới hạn",
                               "Tính năng Mua Game đã bị khóa do giới hạn license.")
            self.log_message("❌ Tính năng Mua Game bị khóa do license ALL_BLOCKED")
            return

        # Nếu đang chạy, dừng quá trình
        if self.is_mua_game_running:
            self.log_message("⏹️ Đang dừng quá trình mua game...")
            self.stop_mua_game = True

            # Đặt cờ dừng cho tất cả các thiết bị đang chạy
            if hasattr(self, 'stop_flags'):
                for serial, flag in self.stop_flags.items():
                    if isinstance(flag, threading.Event):
                        flag.set()
                        self.log_message(f"⚠️ Đã đặt cờ dừng cho thiết bị {serial}")

            # Reset button về trạng thái bình thường
            self.ui.pushButton_Mua.setChecked(False)
            self.is_mua_game_running = False
            return

        # Lấy danh sách thiết bị đã chọn
        selected_devices = []
        for row in range(self.ui.tableWidget.rowCount()):
            widget = self.ui.tableWidget.cellWidget(row, 0)
            if widget:
                checkbox = widget.findChild(QCheckBox)
                if checkbox and checkbox.isChecked():
                    serial_item = self.ui.tableWidget.item(row, 1)
                    if serial_item:
                        selected_devices.append((row, serial_item.text().strip()))

        # Kiểm tra xem có thiết bị nào được chọn không
        if not selected_devices:
            QMessageBox.warning(self, "Cảnh báo", "Vui lòng chọn ít nhất một thiết bị!")
            return

        # Đặt trạng thái đang chạy và reset cờ dừng
        self.is_mua_game_running = True
        self.stop_mua_game = False

        # Chuyển button sang trạng thái stop (hiển thị stop icon)
        self.ui.pushButton_Mua.setChecked(True)

        # Khởi tạo stop_flags nếu chưa có
        if not hasattr(self, 'stop_flags'):
            self.stop_flags = {}

        # Khởi tạo counter cho threads mua game
        self.mua_game_thread_count = len(selected_devices)
        self.mua_game_thread_lock = threading.Lock()

        # Danh sách để theo dõi threads
        mua_threads = []

        # Thực hiện chuỗi hành động trên từng thiết bị được chọn
        for row, device in selected_devices:
            # Cập nhật trạng thái
            status_item = self.ui.tableWidget.item(row, 5)
            if status_item:
                status_item.setText("Đang thực hiện...")
                QApplication.processEvents()

            # Tạo một luồng mới để thực hiện chuỗi hành động
            thread = threading.Thread(target=self.execute_mua_game, args=(row, device))
            thread.daemon = True
            thread.start()
            mua_threads.append(thread)

        # Đợi tất cả threads hoàn thành
        self.log_message(f"⏳ Đang đợi {len(mua_threads)} thiết bị hoàn thành mua game...")
        for thread in mua_threads:
            while thread.is_alive():
                # Kiểm tra cờ dừng mỗi giây
                if self.stop_mua_game:
                    self.log_message("⚠️ Đã dừng đợi mua game theo yêu cầu")
                    return
                thread.join(timeout=1.0)  # Đợi 1 giây
                QApplication.processEvents()
        self.log_message("✅ Tất cả thiết bị đã hoàn thành mua game")

    def execute_mua_game(self, row, serial):
        """
        Thực hiện chuỗi hành động mua game trên một thiết bị.
        :param row: Dòng trong bảng.
        :param serial: Số serial của thiết bị.
        """
        try:
            # Cập nhật trạng thái
            status_item = self.ui.tableWidget.item(row, 5)
            if status_item:
                status_item.setText("Đang thực hiện mua game...")
                QApplication.processEvents()

            # Lấy mật khẩu từ password_manager, ưu tiên từ tableWidget
            password = password_manager.get_password(serial, row, self.ui.tableWidget)
            if password:
                self.log_message(f"✅ Đã lấy mật khẩu cho thiết bị {serial}: {password[:2]}{'*' * (len(password) - 2) if len(password) > 2 else '*' * len(password)}")
            else:
                self.log_message(f"❌ Không tìm thấy mật khẩu cho thiết bị {serial}")
                password = ""  # Đặt mật khẩu rỗng nếu không tìm thấy

            # Tạo đối tượng mua_game
            from mua import mua_game
            # Lấy danh sách thiết bị đã chọn để tính device_index
            selected_devices = []
            for r in range(self.ui.tableWidget.rowCount()):
                widget = self.ui.tableWidget.cellWidget(r, 0)
                if widget:
                    checkbox = widget.findChild(QCheckBox)
                    if checkbox and checkbox.isChecked():
                        serial_item = self.ui.tableWidget.item(r, 1)
                        if serial_item:
                            selected_devices.append((r, serial_item.text().strip()))

            # Lấy chỉ số thiết bị trong danh sách thiết bị được chọn
            device_index = 0
            for i, (r, s) in enumerate(selected_devices):
                if r == row and s == serial:
                    device_index = i
                    break

            total_devices = len(selected_devices)
            self.log_message(f"🔍 Thiết bị {serial} có chỉ số {device_index}/{total_devices}")
            # Lấy giá trị của các spinbox
            goi1_value = self.ui.goi1.value() if hasattr(self.ui, 'goi1') else 5
            goi2_value = self.ui.goi2.value() if hasattr(self.ui, 'goi2') else 5
            goi3_value = self.ui.goi3.value() if hasattr(self.ui, 'goi3') else 30

            # Log thông tin về giá trị spinbox
            self.log_message(f"🔍 Giá trị spinbox: Gói 1={goi1_value}, Gói 2={goi2_value}, Gói 3={goi3_value}")

            # Truyền mật khẩu và giá trị spinbox vào đối tượng mua_game
            mua_instance = mua_game(serial, device_index, total_devices, ADB_PATH, password,
                                   goi1_value, goi2_value, goi3_value)

            # Tạo hàm cập nhật trạng thái
            def update_status():
                if status_item and hasattr(mua_instance, 'current_package') and hasattr(mua_instance, 'package_count'):
                    if mua_instance.current_package > 0:
                        # Xác định tổng số lần mua dựa trên gói hiện tại
                        total_count = 0
                        if mua_instance.current_package == 1:
                            total_count = goi1_value
                        elif mua_instance.current_package == 2:
                            total_count = goi2_value
                        elif mua_instance.current_package == 3:
                            total_count = goi3_value

                        # Hiển thị thêm thứ tự thẻ nếu có
                        card_info = ""
                        if hasattr(mua_instance, 'current_card_number') and mua_instance.current_card_number > 0:
                            card_info = f" - Thẻ {mua_instance.current_card_number}"

                        # Cập nhật trạng thái trên cột log trong bảng
                        status_text = f"🎯 Gói {mua_instance.current_package}: {mua_instance.package_count}/{total_count}{card_info}"
                        status_item.setText(status_text)

                        # Thêm thông báo log để hiển thị trạng thái hiện tại
                        self.log_message(f"🔄 {status_text} - Thiết bị {serial}")

                        # Đảm bảo giao diện được cập nhật ngay lập tức
                        QApplication.processEvents()
                        # Thêm một lần cập nhật nữa để đảm bảo UI được cập nhật
                        QApplication.processEvents()
                    else:
                        # Hiển thị thứ tự thẻ khi không trong vòng lặp gói
                        card_info = ""
                        if hasattr(mua_instance, 'current_card_number') and mua_instance.current_card_number > 0:
                            card_info = f" - Thẻ {mua_instance.current_card_number}"

                        status_text = f"Đang thực hiện mua game{card_info}..."
                        status_item.setText(status_text)
                        QApplication.processEvents()
                        # Thêm một lần cập nhật nữa để đảm bảo UI được cập nhật
                        QApplication.processEvents()

            # Đặt hàm cập nhật trạng thái vào mua_instance
            mua_instance.update_status_func = update_status

            # Tạo biến cờ dừng
            stop_flag = threading.Event()
            # Đảm bảo self.stop_flags đã được khởi tạo
            if not hasattr(self, 'stop_flags'):
                self.stop_flags = {}
            self.stop_flags[serial] = stop_flag

            # Tạo hàm kiểm tra cờ dừng kết hợp cả cờ cục bộ và cờ toàn cục
            def check_stop_flag():
                # Kiểm tra cờ dừng toàn cục
                if self.stop_mua_game:
                    self.log_message(f"⚠️ Đã nhận tín hiệu dừng toàn cục cho thiết bị {serial}")
                    return True
                # Kiểm tra cờ dừng cục bộ
                if stop_flag.is_set():
                    self.log_message(f"⚠️ Đã nhận tín hiệu dừng cục bộ cho thiết bị {serial}")
                    return True
                return False

            # Thực hiện chuỗi hành động
            success = mua_instance.execute_actions(check_stop_flag)

            # Cập nhật trạng thái
            if status_item:
                if success:
                    status_item.setText(f"✅ Hoàn thành mua game")
                    self.log_message(f"✅ Hoàn thành mua game - Thiết bị {serial}")
                else:
                    status_item.setText(f"⚠️ Đã dừng mua game")
                    self.log_message(f"⚠️ Đã dừng chuỗi hành động mua game trên thiết bị {serial} (Gói {mua_instance.current_package}: {mua_instance.package_count})")

            # Giảm counter và kiểm tra nếu tất cả threads đã hoàn thành
            with self.mua_game_thread_lock:
                self.mua_game_thread_count -= 1
                if self.mua_game_thread_count <= 0:
                    # Tất cả threads đã hoàn thành, reset nút
                    self.is_mua_game_running = False
                    self.ui.pushButton_Mua.setChecked(False)
                    self.log_message("✅ Quá trình mua game đã hoàn thành")

        except Exception as e:
            error_msg = f"Lỗi: {str(e)}"
            self.log_message(f"❌ {error_msg} - Thiết bị {serial} (dòng {row+1})")
            if status_item:
                status_item.setText(f"❌ {error_msg}")

            # Giảm counter và kiểm tra nếu tất cả threads đã hoàn thành (cả khi có lỗi)
            with self.mua_game_thread_lock:
                self.mua_game_thread_count -= 1
                if self.mua_game_thread_count <= 0:
                    # Tất cả threads đã hoàn thành, reset nút
                    self.is_mua_game_running = False
                    self.ui.pushButton_Mua.setChecked(False)
                    self.log_message("✅ Quá trình mua game đã hoàn thành")

    def restore_email(self, is_manual=False):
        """Thực hiện restore email thông qua API

        :param is_manual: True nếu được gọi từ button restore thủ công, False nếu từ auto start
        """

        # Kiểm tra Auto-Assign và lên lịch đổi proxy sau 20s nếu được bật
        if hasattr(self, 'auto_assign_enabled') and self.auto_assign_enabled:
            self.log_message("🔄 Auto-Assign ON - Sẽ tự động đổi proxy sau 20 giây...")
            # Lên lịch đổi proxy sau 20 giây
            QTimer.singleShot(20000, self.auto_rotate_proxy_after_restore)
        else:
            self.log_message("ℹ️ Auto-Assign OFF - Bỏ qua đổi proxy tự động")

        # Kiểm tra xem có thiết bị nào được chọn không
        selected_devices = []
        for row in range(self.ui.tableWidget.rowCount()):
            widget = self.ui.tableWidget.cellWidget(row, 0)
            if widget:
                checkbox = widget.findChild(QCheckBox)
                if checkbox and checkbox.isChecked():
                    serial_item = self.ui.tableWidget.item(row, 1)
                    if serial_item:
                        selected_devices.append((row, serial_item.text().strip()))

        if not selected_devices:
            self.log_message("⚠️ Vui lòng chọn ít nhất một thiết bị!")
            return

        # Lấy giá trị port doma
        port_doma = "60708"  # Giá trị mặc định
        if hasattr(self.ui, 'lineEdit_port_doma'):
            port_doma = self.ui.lineEdit_port_doma.text().strip()
            if not port_doma:
                port_doma = "60708"

        # Đọc email từ file mail.txt sử dụng path_manager
        try:
            # Thử sử dụng get_txt_path (phương pháp đơn giản nhất)
            mail_file_path = path_manager.get_txt_path("mail.txt")
            self.log_message(f"🔍 Đường dẫn file mail.txt từ get_txt_path: {mail_file_path}")
        except Exception as e:
            self.log_message(f"⚠️ Lỗi khi sử dụng get_txt_path: {str(e)}")
            # Sử dụng get_data_path như phương án dự phòng
            mail_file_path = path_manager.get_data_path("mail.txt")
            self.log_message(f"🔍 Đường dẫn file mail.txt từ get_data_path: {mail_file_path}")

        # Đường dẫn đến file mail_used.txt
        mail_used_path = os.path.join(os.path.dirname(mail_file_path), "mail_used.txt")
        self.log_message(f"🔍 Đường dẫn file mail_used.txt: {mail_used_path}")

        # Đường dẫn đến file email_ver.txt (email bị verification/captcha)
        email_ver_path = os.path.join(os.path.dirname(mail_file_path), "email_ver.txt")
        self.log_message(f"🔍 Đường dẫn file email_ver.txt: {email_ver_path}")

        if not os.path.exists(mail_file_path):
            # Tạo thư mục data nếu chưa tồn tại
            data_dir = os.path.dirname(mail_file_path)
            if not os.path.exists(data_dir):
                os.makedirs(data_dir)
                self.log_message(f"✅ Đã tạo thư mục {data_dir}")

            # Tạo file mail.txt trống
            with open(mail_file_path, "w") as file:
                file.write("# Danh sách email\n")

            self.log_message(f"⚠️ File {mail_file_path} không tồn tại hoặc trống. Đã tạo file mới.")
            return

        # Đọc nội dung file mail.txt
        try:
            with open(mail_file_path, "r") as file:
                all_emails = file.readlines()

            # Lọc các dòng trống
            all_emails = [email.strip() for email in all_emails if email.strip()]

            if not all_emails:
                self.log_message(f"⚠️ File {mail_file_path} không chứa email nào.")
                return

            # Đọc danh sách email đã sử dụng
            used_emails = []
            if os.path.exists(mail_used_path):
                try:
                    with open(mail_used_path, "r") as file:
                        used_emails = file.readlines()
                    used_emails = [email.strip() for email in used_emails if email.strip()]
                    self.log_message(f"Đã đọc {len(used_emails)} email đã sử dụng từ file {mail_used_path}")
                except Exception as e:
                    self.log_message(f"⚠️ Lỗi khi đọc file {mail_used_path}: {str(e)}")
                    used_emails = []

            # Đọc danh sách email bị verification (captcha)
            verification_emails = []
            if os.path.exists(email_ver_path):
                try:
                    with open(email_ver_path, "r", encoding="utf-8") as file:
                        verification_emails = file.readlines()
                    verification_emails = [email.strip() for email in verification_emails if email.strip()]
                    self.log_message(f"Đã đọc {len(verification_emails)} email bị verification từ file {email_ver_path}")
                except Exception as e:
                    self.log_message(f"⚠️ Lỗi khi đọc file {email_ver_path}: {str(e)}")
                    verification_emails = []
            else:
                self.log_message(f"ℹ️ File {email_ver_path} chưa tồn tại - chưa có email nào bị verification")

            # Lọc ra các email chưa sử dụng và không bị verification
            # Bước 1: Loại trừ email đã sử dụng
            emails_not_used = [email for email in all_emails if email not in used_emails]

            # Bước 2: Loại trừ email bị verification (chỉ so sánh phần email, không so sánh toàn bộ dòng)
            available_emails = []
            for email_line in emails_not_used:
                # Lấy phần email từ dòng (format: email|password|auth)
                email_only = email_line.split('|')[0].strip() if '|' in email_line else email_line.strip()

                # Kiểm tra xem email có trong danh sách verification không
                if email_only not in verification_emails:
                    available_emails.append(email_line)

            if not available_emails:
                self.log_message(f"⚠️ Hết email khả dụng.")

                # Lọc lại: chỉ loại trừ email bị verification
                available_emails = []
                for email_line in all_emails:
                    email_only = email_line.split('|')[0].strip() if '|' in email_line else email_line.strip()
                    if email_only not in verification_emails:
                        available_emails.append(email_line)

            # Hiển thị thông tin về số lượng email
            self.log_message(f"Tổng số email: {len(all_emails)}")
            self.log_message(f"Số email đã sử dụng: {len(used_emails)}")
            self.log_message(f"Số email bị verification: {len(verification_emails)}")
            self.log_message(f"Số email khả dụng: {len(available_emails)}")

            # Hiển thị danh sách email bị verification (nếu có)
            if verification_emails:
                if len(verification_emails) <= 3:
                    self.log_message(f"⚠️ Email bị verification: {', '.join(verification_emails)}")
                else:
                    self.log_message(f"⚠️ Email bị verification: {', '.join(verification_emails[:3])}... và {len(verification_emails) - 3} email khác")

            # Sử dụng danh sách email khả dụng
            emails = available_emails

            # Kiểm tra xem số thiết bị có nhiều hơn số email không
            if len(selected_devices) > len(emails):
                self.log_message(f"⚠️ Số thiết bị ({len(selected_devices)}) nhiều hơn số email ({len(emails)}).")
                self.log_message(f"⚠️ Các thiết bị không có email tương ứng sẽ bị bỏ qua.")

                # Giới hạn số thiết bị được xử lý bằng số email có sẵn
                devices_to_process = selected_devices[:len(emails)]

                # Cập nhật trạng thái của các thiết bị không được xử lý
                for i, (row, _) in enumerate(selected_devices):
                    if i >= len(emails):
                        status_item = self.ui.tableWidget.item(row, 5)
                        if status_item:
                            status_item.setText("⚠️ Hết mail")
                            QApplication.processEvents()
                        # Hiển thị thông báo trong cột log
                        self.log_message(f"⚠️ Hết mail, không thể xử lý thiết bị ở dòng {row + 1}")
            else:
                devices_to_process = selected_devices
                # Nếu số email nhiều hơn số máy, hiển thị thông báo
                if len(emails) > len(selected_devices):
                    self.log_message(f"ℹ️ Có {len(emails) - len(selected_devices)} email chưa được sử dụng")
                    # Hiển thị danh sách email chưa sử dụng (chỉ hiển thị 3 email đầu tiên)
                    unused_emails = emails[len(selected_devices):]
                    if len(unused_emails) > 3:
                        self.log_message(f"ℹ️ Email chưa sử dụng: {', '.join(unused_emails[:3])}... và {len(unused_emails) - 3} email khác")
                    else:
                        self.log_message(f"ℹ️ Email chưa sử dụng: {', '.join(unused_emails)}")

            # Cập nhật trạng thái của các thiết bị được xử lý
            for row, _ in devices_to_process:
                status_item = self.ui.tableWidget.item(row, 5)
                if status_item:
                    status_item.setText("Đang chuẩn bị restore email...")
                    QApplication.processEvents()

            # Tạo một hàm để xử lý từng thiết bị
            def process_device(row_serial_tuple, email_index, total_emails=None, actual_email_position=None):
                row, serial = row_serial_tuple
                try:
                    # Kiểm tra cờ dừng trước khi bắt đầu
                    if hasattr(self, 'stop_restoring_email') and self.stop_restoring_email:
                        status = "⚠️ Đã dừng restore email theo yêu cầu"
                        status_item = self.ui.tableWidget.item(row, 5)
                        if status_item:
                            status_item.setText(status)
                        self.log_message(f"{status} - Thiết bị {serial} (dòng {row + 1})")
                        return status

                    # Lấy email tương ứng với thiết bị
                    email = emails[email_index]

                    # Phân tích email để lấy password tương ứng (format: email|password|auth|proxy)
                    email_parts = email.split('|')
                    email_only = email_parts[0].strip() if len(email_parts) > 0 else email
                    password_from_email = email_parts[1].strip() if len(email_parts) > 1 else ""
                    auth_from_email = email_parts[2].strip() if len(email_parts) > 2 else ""

                    # Track email được gán cho thiết bị này (để dùng cho smart proxy assign)
                    device_id = serial  # serial chính là device_id
                    self.device_email_mapping[device_id] = email_only

                    self.log_message(f"🔍 Email: {email_only}")
                    if password_from_email:
                        self.log_message(f"🔍 Password từ email: {password_from_email[:2]}{'*' * (len(password_from_email) - 2) if len(password_from_email) > 2 else '*' * len(password_from_email)}")

                    # Cập nhật password vào bảng để chplay.py có thể sử dụng
                    password_item = self.ui.tableWidget.item(row, 2)
                    if password_item and password_from_email:
                        password_item.setText(password_from_email)
                        self.log_message(f"🔄 Đã cập nhật password vào bảng cho thiết bị dòng {row + 1}")

                    # Cập nhật auth vào bảng nếu có
                    if auth_from_email:
                        auth_item = self.ui.tableWidget.item(row, 3)
                        if auth_item:
                            auth_item.setText(auth_from_email)
                            self.log_message(f"🔄 Đã cập nhật auth vào bảng cho thiết bị dòng {row + 1}")

                    # Cập nhật email vào bảng 
                    if email_only:
                        email_item = self.ui.tableWidget.item(row, 4)
                        if email_item:
                            email_item.setText(email_only)
                            self.log_message(f"🔄 Đã cập nhật email vào bảng cho thiết bị dòng {row + 1}")

                    # Lấy Device ID từ cột Devices (cột 0, index 0)
                    device_item = self.ui.tableWidget.item(row, 0)
                    device_id = device_item.text().strip() if device_item and device_item.text().strip() else ""

                    # In thông tin debug
                    self.log_message(f"🔍 Device ID từ cột Devices: '{device_id}'")

                    if not device_id:
                        # Nếu không có ID, sử dụng serial làm ID
                        device_id = serial
                        self.log_message(f"🔍 Sử dụng serial làm ID vì không có ID trong cột Pass")

                    # Xác định loại hành động (restore hoặc changeinfo)
                    action_type = "restore"
                    action_display = "Restore"
                    if hasattr(self.ui, 'comboBox_restore'):
                        # Lấy giá trị combobox
                        action_index = self.ui.comboBox_restore.currentIndex()
                        if action_index == 0:  # Restore
                            action_type = "restore"
                            action_display = "Restore"
                        elif action_index == 1:  # Changer
                            action_type = "changeinfo"
                            action_display = "Change info"

                    # Tạo thông tin số thứ tự email - sử dụng vị trí thực tế thay vì email_index
                    if actual_email_position and total_emails:
                        email_progress = f"{actual_email_position}/{total_emails}"
                    elif total_emails:
                        email_progress = f"{email_index + 1}/{total_emails}"
                    else:
                        email_progress = f"{email_index + 1}"

                    # Cập nhật trạng thái
                    status_item = self.ui.tableWidget.item(row, 5)
                    if status_item:
                        status_item.setText(f"Đang {action_display.lower()} email ({email_progress}): {email_only}")
                        QApplication.processEvents()

                    # Hiển thị thông tin trong log
                    self.log_message(f"🔄 Bắt đầu {action_display.lower()} email ({email_progress}): {email_only}")

                    # Tạo URL API theo action_type
                    if action_type == "changeinfo":
                        # Change info chỉ cần device_id
                        api_url = f"http://127.0.0.1:{port_doma}/?changeinfo={urllib.parse.quote(device_id)}"
                    else:  # restore
                        # Restore cần device_id|email|password|auth (như checkpoint 1)
                        restore_data = f"{device_id}|{email_only}|{password_from_email}|{auth_from_email}"
                        api_url = f"http://127.0.0.1:{port_doma}/?restore={urllib.parse.quote(restore_data)}"

                    # In thông tin debug về URL API
                    self.log_message(f"🔗 Gửi yêu cầu đến URL: {api_url}")

                    # Gửi yêu cầu đến API
                    try:
                        # Cập nhật trạng thái
                        if status_item:
                            status_item.setText(f"Đang gửi yêu cầu {action_display.lower()} email ({email_progress}): {email}")
                            QApplication.processEvents()

                        # Hiển thị thông tin trong log
                        self.log_message(f"🔄 Đang gửi yêu cầu {action_display.lower()} email ({email_progress})...")

                        # Gửi yêu cầu đến API
                        response = requests.get(api_url, timeout=30)

                        # Log chi tiết response để debug
                        self.log_message(f"🔍 DEBUG: Response status code: {response.status_code}")
                        self.log_message(f"🔍 DEBUG: Response headers: {dict(response.headers)}")
                        self.log_message(f"🔍 DEBUG: Response text (first 200 chars): {response.text[:200]}")

                        # Kiểm tra kết quả
                        if response.status_code == 200:
                            # Kiểm tra nội dung response để đảm bảo thực sự thành công
                            try:
                                import json
                                response_data = json.loads(response.text)
                                if response_data.get("status") == "success":
                                    status = f"✅ Đã {action_display.lower()} email ({email_progress}): {email_only}"
                                    self.log_message(f"✅ API trả về thành công - {action_display} email ({email_progress}): {email_only}")
                                    self.log_message(f"🔍 DEBUG: Full API response: {response_data}")

                                    # Đánh dấu email đã sử dụng (ghi toàn bộ dòng email|password|auth)
                                    try:
                                        with open(mail_used_path, "a") as file:
                                            file.write(f"{email}\n")  # Ghi toàn bộ dòng để so sánh chính xác
                                        self.log_message(f"📝 Đã đánh dấu email {email_only} là đã sử dụng")
                                    except Exception as e:
                                        self.log_message(f"⚠️ Lỗi khi đánh dấu email {email_only} là đã sử dụng: {str(e)}")
                                else:
                                    status = f"❌ API trả về lỗi cho email ({email_progress}): {email_only} - {response_data}"
                                    self.log_message(f"❌ API trả về status không thành công: {response_data}")
                                    self.log_message(f"🔍 DEBUG: Expected 'success' but got: {response_data.get('status')}")
                                    return status
                            except json.JSONDecodeError:
                                # Nếu response không phải JSON, coi như thành công nếu HTTP 200
                                status = f"✅ Đã {action_display.lower()} email ({email_progress}): {email_only}"
                                self.log_message(f"✅ API trả về HTTP 200 (non-JSON response) cho email ({email_progress}): {email_only}")

                                # Đánh dấu email đã sử dụng
                                try:
                                    with open(mail_used_path, "a") as file:
                                        file.write(f"{email}\n")
                                    self.log_message(f"📝 Đã đánh dấu email {email_only} là đã sử dụng")
                                except Exception as e:
                                    self.log_message(f"⚠️ Lỗi khi đánh dấu email {email_only} là đã sử dụng: {str(e)}")
                        else:
                            status = f"❌ Không thể {action_display.lower()} email ({email_progress}): {email_only} (HTTP {response.status_code})"
                            # Ghi log lỗi HTTP chi tiết
                            self.log_message(f"❌ Lỗi HTTP {response.status_code} khi gửi yêu cầu đến API")
                            self.log_message(f"❌ Response text: {response.text}")
                            # Dừng quá trình và không tiếp tục với thiết bị tiếp theo
                            return status
                    except requests.RequestException as e:
                        status = f"❌ Lỗi kết nối API: {str(e)}"
                        # Ghi log lỗi kết nối API
                        self.log_message(f"❌ Lỗi kết nối API: {str(e)}")
                        # Dừng quá trình và không tiếp tục với thiết bị tiếp theo
                        return status

                    # Cập nhật trạng thái trong bảng
                    if status_item:
                        status_item.setText(status)
                        QApplication.processEvents()

                    # Hiển thị thông tin trong log
                    self.log_message(f"{status}")
                    return status

                except Exception as e:
                    error_msg = f"Lỗi khi {action_display.lower()} email: {str(e)}"
                    status_item = self.ui.tableWidget.item(row, 5)
                    if status_item:
                        status_item.setText(f"❌ {error_msg}")
                        QApplication.processEvents()
                    self.log_message(f"❌ {error_msg}")
                    return error_msg

            # Xác định loại hành động (restore hoặc changeinfo) cho thông báo
            action_display = "Restore"
            if hasattr(self.ui, 'comboBox_restore'):
                action_index = self.ui.comboBox_restore.currentIndex()
                if action_index == 0:  # Restore
                    action_display = "Restore"
                elif action_index == 1:  # Changer
                    action_display = "Change info"

            # Xử lý từng thiết bị với email tương ứng tuần tự
            # Sử dụng tổng số email trong file thay vì số thiết bị được chọn
            total_emails_in_file = len(all_emails)  # Tổng số email trong file mail.txt

            # Tính toán vị trí email thực tế dựa trên số email đã sử dụng
            emails_used_count = len(used_emails)  # Số email đã sử dụng
            self.log_message(f"Bắt đầu {action_display.lower()} email cho {len(devices_to_process)} thiết bị (tổng {total_emails_in_file} email trong file, đã dùng {emails_used_count}). Quá trình này có thể mất vài phút...")

            # Tạo danh sách để lưu trữ kết quả
            results = []

            # Biến để kiểm tra xem có lỗi kết nối API không
            api_connection_error = False

            # Xử lý từng thiết bị tuần tự
            for i, device in enumerate(devices_to_process):
                # Tính vị trí email thực tế: vị trí hiện tại + số email đã sử dụng
                actual_email_position = emails_used_count + i + 1
                result = process_device(device, i, total_emails_in_file, actual_email_position)  # Thêm actual_email_position
                results.append(result)

                # Kiểm tra xem có lỗi kết nối API không
                if result and isinstance(result, str) and ("LỖI KẾT NỐI API" in result or "HTTP" in result):
                    # Hiển thị thông báo lỗi nổi bật trong log
                    self.log_message("=" * 50)
                    self.log_message("❌ PHÁT HIỆN LỖI KẾT NỐI API HOẶC LỖI HTTP")
                    self.log_message(f"❌ Chi tiết lỗi: {result}")
                    self.log_message("❌ ĐANG DỪNG TẤT CẢ CÁC THIẾT BỊ")
                    self.log_message("=" * 50)

                    # Cập nhật trạng thái của tất cả các thiết bị còn lại
                    for remaining_row, _ in devices_to_process[i+1:]:
                        remaining_status_item = self.ui.tableWidget.item(remaining_row, 5)
                        if remaining_status_item:
                            remaining_status_item.setText("❌ Đã dừng do lỗi kết nối API")
                            QApplication.processEvents()

                    api_connection_error = True
                    break

                # Đợi một chút giữa các thiết bị để tránh quá tải API
                if i < len(devices_to_process) - 1:  # Không đợi sau thiết bị cuối cùng
                    self.log_message(f"Đợi 3 giây trước khi xử lý thiết bị tiếp theo...")
                    time.sleep(1)

            # Nếu có lỗi kết nối API, dừng quá trình và không đợi 50 giây
            if api_connection_error:
                self.log_message("=" * 50)
                self.log_message("❌ ĐÃ DỪNG QUÁ TRÌNH DO LỖI KẾT NỐI API")
                self.log_message("❌ KIỂM TRA XEM API CÓ ĐANG CHẠY KHÔNG")
                self.log_message("❌ KHÔNG ĐỢI 65 GIÂY (do lỗi kết nối API)")
                self.log_message("❌ KHÔNG HOÀN THÀNH QUÁ TRÌNH RESTORE EMAIL")
                self.log_message("=" * 50)

                # Cập nhật trạng thái của tất cả các thiết bị đã chọn
                for row, _ in selected_devices:
                    status_item = self.ui.tableWidget.item(row, 5)
                    if status_item and "❌" not in status_item.text():
                        status_item.setText("❌ Đã dừng do lỗi kết nối API")
                        QApplication.processEvents()

                return

            # Chỉ đợi 65 giây nếu không phải restore thủ công
            if not is_manual:
                # Đợi thêm 65 giây sau khi hoàn thành tất cả các thiết bị để đảm bảo API đã xử lý xong
                self.log_message("Đợi thêm 65 giây để đảm bảo API đã xử lý xong...")

                # Đếm ngược 65 giây để người dùng biết còn bao lâu nữa
                for remaining in range(65, 0, -1):
                    # Kiểm tra cờ dừng
                    if hasattr(self, 'stop_restoring_email') and self.stop_restoring_email:
                        self.log_message("⚠️ Đã dừng restore email theo yêu cầu")
                        return

                    self.log_message(f"Còn {remaining} giây để hoàn thành bước restore...")

                    # Cập nhật trạng thái của tất cả các thiết bị được chọn
                    for row, _ in selected_devices:
                        status_item = self.ui.tableWidget.item(row, 5)
                        if status_item:
                            # Hiển thị số email đã xử lý thực tế - sửa logic để tránh vượt quá tổng số
                            emails_processed_so_far = min(emails_used_count + len(devices_to_process), total_emails_in_file)
                            status_item.setText(f"Đã hoàn thành restore email ({emails_processed_so_far}/{total_emails_in_file}), đợi {remaining}s để hoàn thành...")
                            QApplication.processEvents()

                    time.sleep(1)
                    QApplication.processEvents()

                # Hoàn thành bước restore sau khi đợi 65 giây
                self.log_message("✅ Đã hoàn thành thời gian chờ 65 giây cho bước restore")
            else:
                # Restore thủ công - không cần đợi 65 giây
                self.log_message("✅ Restore thủ công - Bỏ qua thời gian chờ 65 giây")

                # Cập nhật trạng thái cuối cùng cho restore thủ công
                for row, _ in selected_devices:
                    status_item = self.ui.tableWidget.item(row, 5)
                    if status_item:
                        emails_processed_so_far = min(emails_used_count + len(devices_to_process), total_emails_in_file)
                        status_item.setText(f"✅ Đã hoàn thành restore email ({emails_processed_so_far}/{total_emails_in_file})")
                        QApplication.processEvents()

            # Bỏ phần tự động chplay login để tránh xung đột với action sequence
            # Chplay login sẽ được xử lý riêng biệt trong action sequence nếu được chọn
            self.log_message("ℹ️ Đã hoàn thành restore email - Chplay login sẽ được xử lý riêng nếu được chọn trong action sequence")

            # Xác định loại hành động (restore hoặc changeinfo) cho thông báo kết thúc
            action_display = "Restore"
            if hasattr(self.ui, 'comboBox_restore'):
                action_index = self.ui.comboBox_restore.currentIndex()
                if action_index == 0:  # Restore
                    action_display = "Restore"
                elif action_index == 1:  # Changer
                    action_display = "Change info"

            # Hiển thị thông báo hoàn thành
            if len(devices_to_process) < len(selected_devices):
                self.log_message(f"✅ Đã hoàn thành {action_display.lower()} {len(devices_to_process)} email trên {len(devices_to_process)}/{len(selected_devices)} thiết bị!")
            else:
                self.log_message(f"✅ Đã hoàn thành {action_display.lower()} {len(devices_to_process)} email trên tất cả các thiết bị!")

            # Tự động cập nhật trạng thái sau khi restore email
            self.log_message("🔄 Đang cập nhật trạng thái thiết bị sau restore email...")
            QTimer.singleShot(2000, lambda: self.get_device_status(selected_only=True))  # Chỉ thiết bị đã chọn

            # Auto restart sẽ được xử lý ở cuối start_actions() để tránh gọi 2 lần

        except Exception as e:
            self.log_message(f"❌ Lỗi khi đọc file mail.txt: {str(e)}")

    # HÀM KHÔNG SỬ DỤNG - CÓ THỂ XÓA
    # def show_email_verification_stats(self):
    #     """Hiển thị thống kê email bị verification"""
    #     try:
    #         # Lấy đường dẫn file email_ver.txt
    #         email_ver_path = path_manager.get_data_path("email_ver.txt")

    #         if not os.path.exists(email_ver_path):
    #             self.log_message("ℹ️ Chưa có email nào bị verification (captcha)")
    #             return

    #         # Đọc danh sách email bị verification
    #         with open(email_ver_path, "r", encoding="utf-8") as file:
    #             verification_emails = [line.strip() for line in file.readlines() if line.strip()]

    #         if not verification_emails:
    #             self.log_message("ℹ️ File email_ver.txt trống - chưa có email nào bị verification")
    #             return

    #         # Hiển thị thống kê
    #         self.log_message("=" * 50)
    #         self.log_message("📊 THỐNG KÊ EMAIL BỊ VERIFICATION (CAPTCHA)")
    #         self.log_message("=" * 50)
    #         self.log_message(f"Tổng số email bị verification: {len(verification_emails)}")
    #         self.log_message("Danh sách email bị verification:")

    #         for i, email in enumerate(verification_emails, 1):
    #             self.log_message(f"  {i}. {email}")

    #         self.log_message("=" * 50)
    #         self.log_message("ℹ️ Các email này sẽ được tự động bỏ qua khi restore")
    #         self.log_message("ℹ️ Để reset danh sách, xóa file email_ver.txt")

    #     except Exception as e:
    #         self.log_message(f"❌ Lỗi khi hiển thị thống kê email verification: {str(e)}")

    def save_actions_to_file(self, actions_to_perform):
        """Lưu danh sách actions vào file action.txt"""
        try:
            action_file_path = path_manager.get_data_path("action.txt")
            with open(action_file_path, "w", encoding="utf-8") as file:
                for action_name, action_desc in actions_to_perform:
                    file.write(f"{action_name}|{action_desc}\n")
            self.log_message(f"💾 Đã lưu {len(actions_to_perform)} actions vào file action.txt")
        except Exception as e:
            self.log_message(f"❌ Lỗi khi lưu actions vào file: {str(e)}")

    def load_actions_from_file(self):
        """Đọc danh sách actions từ file action.txt"""
        try:
            action_file_path = path_manager.get_data_path("action.txt")
            if not os.path.exists(action_file_path):
                return []

            actions = []
            with open(action_file_path, "r", encoding="utf-8") as file:
                for line in file:
                    line = line.strip()
                    if line and "|" in line:
                        action_name, action_desc = line.split("|", 1)
                        actions.append((action_name.strip(), action_desc.strip()))

            self.log_message(f"📂 Đã đọc {len(actions)} actions từ file action.txt")
            return actions
        except Exception as e:
            self.log_message(f"❌ Lỗi khi đọc actions từ file: {str(e)}")
            return []

    def start_actions_from_file(self):
        """Thực hiện actions từ file action.txt (dùng cho auto restart)"""
        try:
            # Đọc actions từ file
            actions_to_perform = self.load_actions_from_file()

            if not actions_to_perform:
                self.log_message("⚠️ Không có actions trong file action.txt để thực hiện")
                return

            # Kiểm tra thiết bị được chọn
            selected_devices = []
            for row in range(self.ui.tableWidget.rowCount()):
                widget = self.ui.tableWidget.cellWidget(row, 0)
                if widget:
                    checkbox = widget.findChild(QCheckBox)
                    if checkbox and checkbox.isChecked():
                        serial_item = self.ui.tableWidget.item(row, 1)
                        if serial_item:
                            selected_devices.append((row, serial_item.text()))

            if not selected_devices:
                self.log_message("⚠️ Không có thiết bị nào được chọn để thực hiện auto restart")
                return

            # Đặt trạng thái đang chạy
            self.is_start_actions_running = True
            self.stop_start_actions = False
            self.ui.start_stop_Button.setChecked(True)
            self.ui.start_stop_Button.setText("Stop")

            # Hiển thị thông báo
            actions_str = " → ".join([action[1] for action in actions_to_perform])
            self.log_message(f"🔄 Auto restart - Thực hiện: {actions_str}")

            # Thực hiện các actions
            for action_index, (action_name, action_desc) in enumerate(actions_to_perform):
                # Kiểm tra cờ dừng
                if self.stop_start_actions:
                    self.log_message(f"⚠️ Đã dừng auto restart trước khi thực hiện: {action_desc}")
                    break

                self.log_message(f"Bắt đầu thực hiện hành động {action_index + 1}/{len(actions_to_perform)}: {action_desc}")

                # Đánh thức tất cả thiết bị trước khi bắt đầu action mới (auto restart)
                self.log_message(f"📱 Đánh thức thiết bị trước khi thực hiện: {action_desc}")
                self.wake_up_all_selected_devices()

                # Cập nhật trạng thái thiết bị
                for row, _ in selected_devices:
                    status_item = self.ui.tableWidget.item(row, 5)
                    if status_item:
                        status_item.setText(f"Đang thực hiện: {action_desc}...")
                        QApplication.processEvents()

                # Thực hiện action
                try:
                    if action_name == "delete_card":
                        self.delete_card()
                    elif action_name == "close_payment":
                        self.close_payment()
                    elif action_name == "addcardads":
                        self.addcardads()
                    elif action_name == "add_wallet":
                        self.log_message(f"🔄 Đang thực hiện add wallet...")
                        self.add_wallet_sync()  # Sử dụng phiên bản đồng bộ
                        self.log_message(f"✅ Đã hoàn thành add wallet")
                    elif action_name == "chplay":
                        self.log_message(f"🔄 Đang thực hiện chplay login...")
                        self.is_chplay_running = True
                        self.stop_chplay = False
                        try:
                            # Truyền thông tin bỏ qua captcha khi chạy auto restart
                            self.chplay_login_sync(skip_captcha=True)
                            self.log_message(f"✅ Đã hoàn thành chplay login")
                        except Exception as e:
                            self.log_message(f"❌ Lỗi trong chplay login (auto restart): {e}")
                            # Dừng an toàn nếu có lỗi
                            self.safe_stop_chplay_threads()
                        finally:
                            self.is_chplay_running = False
                    elif action_name == "restore_email":
                        self.log_message(f"🔄 Đang thực hiện restore email...")
                        self.is_restoring_email = True
                        self.stop_restoring_email = False
                        try:
                            self.restore_email()
                            self.log_message(f"✅ Đã hoàn thành restore email")
                        finally:
                            self.is_restoring_email = False
                    elif action_name == "changer":
                        self.log_message(f"🔄 Đang thực hiện change info...")
                        self.is_changing_info = True
                        self.stop_changing_info = False
                        try:
                            self.changer()
                            self.log_message(f"✅ Đã hoàn thành change info")
                        finally:
                            self.is_changing_info = False
                    elif action_name == "mua":
                        self.log_message(f"🔄 Đang thực hiện mua game...")
                        self.mua_game()
                    else:
                        self.log_message(f"⚠️ Không nhận diện được hành động: {action_name}")

                except Exception as e:
                    self.log_message(f"❌ Lỗi khi thực hiện {action_desc}: {str(e)}")
                    for row, _ in selected_devices:
                        status_item = self.ui.tableWidget.item(row, 5)
                        if status_item:
                            status_item.setText(f"❌ Lỗi: {action_desc}")
                            QApplication.processEvents()

                # Đợi giữa các actions
                QApplication.processEvents()
                self.log_message(f"Đợi 3 giây trước khi chuyển sang bước tiếp theo...")
                for i in range(3):
                    time.sleep(1)
                    QApplication.processEvents()
                self.log_message(f"Đã hoàn thành hành động {action_index + 1}/{len(actions_to_perform)}: {action_desc}")

            # Cập nhật trạng thái hoàn thành
            for row, _ in selected_devices:
                status_item = self.ui.tableWidget.item(row, 5)
                if status_item:
                    status_item.setText("✅ Đã hoàn thành tất cả các hành động")
                    QApplication.processEvents()

            # Kiểm tra auto restart tiếp theo
            if not self.stop_start_actions:
                self.log_message("🔄 Đã hoàn thành tất cả hành động - Kiểm tra auto restart...")
                self.check_and_auto_restart_if_emails_available()

            # Reset trạng thái
            self.is_start_actions_running = False
            self.stop_start_actions = False
            self.ui.start_stop_Button.setChecked(False)
            self.ui.start_stop_Button.setText("Start")

        except Exception as e:
            self.log_message(f"❌ Lỗi trong start_actions_from_file: {str(e)}")
            # Reset trạng thái khi có lỗi
            self.is_start_actions_running = False
            self.stop_start_actions = False
            self.ui.start_stop_Button.setChecked(False)
            self.ui.start_stop_Button.setText("Start")

    def check_and_auto_restart_if_emails_available(self):
        """
        Kiểm tra xem còn email nào chưa sử dụng không và tự động restart nếu có
        CHỈ restart khi có action cần email (restore_email hoặc changer)
        """
        try:
            # Kiểm tra xem có action nào cần email không
            actions_to_perform = self.load_actions_from_file()
            email_actions = [action for action in actions_to_perform if action[0] in ['restore_email', 'changer']]

            if not email_actions:
                self.log_message("ℹ️ Không có action nào cần email - Bỏ qua auto restart")
                return
            # Đọc email từ file mail.txt
            try:
                mail_file_path = path_manager.get_txt_path("mail.txt")
            except Exception as e:
                mail_file_path = path_manager.get_data_path("mail.txt")

            # Đường dẫn đến file mail_used.txt
            mail_used_path = os.path.join(os.path.dirname(mail_file_path), "mail_used.txt")

            if not os.path.exists(mail_file_path):
                self.log_message(f"⚠️ File {mail_file_path} không tồn tại. Dừng auto restart.")
                return

            # Đọc nội dung file mail.txt
            with open(mail_file_path, "r") as file:
                all_emails = file.readlines()

            # Lọc các dòng trống và comment
            all_emails = [email.strip() for email in all_emails if email.strip() and not email.strip().startswith('#')]

            if not all_emails:
                self.log_message(f"⚠️ File {mail_file_path} không chứa email nào. Dừng auto restart.")
                return

            # Đọc danh sách email đã sử dụng
            used_emails = []
            if os.path.exists(mail_used_path):
                try:
                    with open(mail_used_path, "r") as file:
                        used_emails = file.readlines()
                    used_emails = [email.strip() for email in used_emails if email.strip()]
                except Exception as e:
                    used_emails = []

            # Lọc ra các email chưa sử dụng
            available_emails = [email for email in all_emails if email not in used_emails]

            self.log_message(f"📊 Thống kê email:")
            self.log_message(f"  - Tổng số email: {len(all_emails)}")
            self.log_message(f"  - Đã sử dụng: {len(used_emails)}")
            self.log_message(f"  - Còn lại: {len(available_emails)}")

            if available_emails:
                # Còn email chưa sử dụng - tự động restart
                self.log_message(f"🔄 Còn {len(available_emails)} email chưa sử dụng - Tự động restart sau 10 giây...")

                # Đếm ngược 10 giây
                for remaining in range(10, 0, -1):
                    # Kiểm tra cờ dừng
                    if hasattr(self, 'stop_restoring_email') and self.stop_restoring_email:
                        self.log_message("⚠️ Đã dừng auto restart theo yêu cầu")
                        return

                    self.log_message(f"⏰ Tự động restart trong {remaining} giây... (Có thể dừng bằng cách bỏ chọn thiết bị)")
                    time.sleep(1)
                    QApplication.processEvents()

                    # Debug: Kiểm tra trạng thái thiết bị mỗi 5 giây
                    if remaining % 5 == 0:
                        current_selected = []
                        for row in range(self.ui.tableWidget.rowCount()):
                            widget = self.ui.tableWidget.cellWidget(row, 0)
                            if widget:
                                checkbox = widget.findChild(QCheckBox)
                                if checkbox and checkbox.isChecked():
                                    current_selected.append(row)
                        self.log_message(f"🔍 DEBUG: Hiện có {len(current_selected)} thiết bị được chọn")

                    # Kiểm tra xem còn thiết bị nào được chọn không
                    selected_devices = []
                    for row in range(self.ui.tableWidget.rowCount()):
                        widget = self.ui.tableWidget.cellWidget(row, 0)
                        if widget:
                            checkbox = widget.findChild(QCheckBox)
                            if checkbox and checkbox.isChecked():
                                selected_devices.append(row)

                    if not selected_devices:
                        self.log_message(f"🚨 PHÁT HIỆN: Không còn thiết bị nào được chọn trong countdown!")
                        self.log_message(f"🔍 DEBUG: Có {self.ui.tableWidget.rowCount()} dòng trong bảng")
                        self.log_message(f"🔍 DEBUG: Đây có thể là do người dùng bỏ chọn thiết bị hoặc bug UI")
                        self.log_message(f"⚠️ DỪNG AUTO RESTART - Không còn thiết bị nào được chọn")
                        return

                # Kiểm tra cờ dừng trước khi restart
                if hasattr(self, 'stop_restoring_email') and self.stop_restoring_email:
                    self.log_message("⚠️ Đã dừng auto restart theo yêu cầu")
                    return

                # Tự động gọi lại start_actions với actions từ file
                self.log_message(f"🚀 Tự động restart - Tiếp tục với {len(available_emails)} email còn lại...")
                self.log_message("=" * 60)

                # Gọi lại start_actions với actions đã lưu
                self.start_actions_from_file()
            else:
                # Hết email - dừng hoàn toàn
                self.log_message(f"🎉 ĐÃ HOÀN THÀNH TẤT CẢ EMAIL!")
                self.log_message(f"✅ Đã xử lý hết {len(all_emails)} email trong file mail.txt")
                self.log_message(f"🏁 Quá trình tự động hoàn toàn kết thúc!")

                # Reset danh sách email đã sử dụng để chuẩn bị cho lần chạy tiếp theo
                try:
                    with open(mail_used_path, "w") as file:
                        pass
                    self.log_message(f"🔄 Đã reset danh sách email đã sử dụng để chuẩn bị cho lần chạy tiếp theo")
                except Exception as e:
                    self.log_message(f"⚠️ Lỗi khi reset danh sách email: {str(e)}")

        except Exception as e:
            self.log_message(f"❌ Lỗi khi kiểm tra auto restart: {str(e)}")

    def force_kill_scrcpy(self):
        """Force kill tất cả scrcpy processes"""
        try:
            self.log_message("🔥 Force killing tất cả scrcpy processes...")

            # Cách 1: taskkill với /F /IM
            subprocess.run(["taskkill", "/F", "/IM", "scrcpy.exe"],
                          stdout=subprocess.DEVNULL,
                          stderr=subprocess.DEVNULL,
                          creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

            # Cách 2: taskkill với /F /T (kill cả process tree)
            subprocess.run(["taskkill", "/F", "/T", "/IM", "scrcpy.exe"],
                          stdout=subprocess.DEVNULL,
                          stderr=subprocess.DEVNULL,
                          creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

            # Cách 3: Tìm và kill theo PID
            try:
                result = subprocess.run(["wmic", "process", "where", "name='scrcpy.exe'", "get", "processid", "/value"],
                                      capture_output=True, text=True,
                                      creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                for line in result.stdout.split('\n'):
                    if 'ProcessId=' in line:
                        pid = line.split('=')[1].strip()
                        if pid and pid.isdigit():
                            subprocess.run(["taskkill", "/F", "/PID", pid],
                                          stdout=subprocess.DEVNULL,
                                          stderr=subprocess.DEVNULL,
                                          creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                            self.log_message(f"🔥 Killed scrcpy PID: {pid}")
            except:
                pass

            self.log_message("✅ Đã force kill tất cả scrcpy processes")
        except Exception as e:
            self.log_message(f"❌ Lỗi khi force kill scrcpy: {e}")

    def chplay_login(self):
        """
        Thực hiện đăng nhập bằng nút Google Play trên các thiết bị đã chọn.
        """
        # Kiểm tra license - chỉ ALL_BLOCKED mới bị khóa
        app = QApplication.instance()
        allowed_features = app.property("allowed_features")
        if allowed_features and allowed_features.upper() == "ALL_BLOCKED":
            QMessageBox.warning(self, "Tính năng bị giới hạn",
                               "Tính năng ChPlay đã bị khóa do giới hạn license.")
            self.log_message("❌ Tính năng ChPlay bị khóa do license ALL_BLOCKED")
            return

        # Import lớp ChPlayThread từ chplay.py (như MUA)
        from chplay import ChPlayThread

        # Bỏ test log để tăng tốc

        # Kiểm tra xem có đang chạy không
        if self.is_chplay_running:
            # Đặt cờ dừng ngay lập tức như MUA - đơn giản và hiệu quả
            self.stop_chplay = True
            self.ui.chplay.setChecked(False)
            self.is_chplay_running = False

            # Dừng tất cả threads như MUA
            if hasattr(self, 'chplay_threads'):
                for thread in self.chplay_threads:
                    try:
                        thread.stop()
                    except Exception as e:
                        print(f"❌ Lỗi khi dừng thread: {e}")

            self.log_message("🛑 Đã gửi tín hiệu dừng ChPlay - Threads sẽ dừng tự động")
            self.log_message("✅ ChPlay đã được dừng (theo cách của MUA)")
            return

        # Kiểm tra xem có thiết bị nào được chọn không
        selected_devices = []
        seen_serials = set()  # Để tránh duplicate serial

        for row in range(self.ui.tableWidget.rowCount()):
            widget = self.ui.tableWidget.cellWidget(row, 0)
            if widget:
                checkbox = widget.findChild(QCheckBox)
                if checkbox and checkbox.isChecked():
                    serial_item = self.ui.tableWidget.item(row, 1)
                    if serial_item:
                        serial = serial_item.text().strip()

                        # Kiểm tra xem serial đã được chọn chưa
                        if serial in seen_serials:
                            self.log_message(f"⚠️ Thiết bị {serial} đã được chọn, bỏ qua duplicate")
                            continue

                        seen_serials.add(serial)

                        # Lấy mật khẩu từ tableWidget nếu có
                        password = None
                        password_item = self.ui.tableWidget.item(row, 2)
                        if password_item and password_item.text().strip():
                            password = password_item.text().strip()

                        # Lấy mã xác thực từ cột authen (cột 3) nếu có
                        auth_secret = None
                        auth_item = self.ui.tableWidget.item(row, 3)
                        if auth_item and auth_item.text().strip():
                            auth_secret = auth_item.text().strip()

                        # Lấy data từ cột data (cột 4) nếu có
                        data = None
                        data_item = self.ui.tableWidget.item(row, 4)
                        if data_item and data_item.text().strip():
                            data = data_item.text().strip()

                        # Thêm vào danh sách thiết bị đã chọn
                        selected_devices.append((row, serial, password, auth_secret, data))

        if not selected_devices:
            QMessageBox.warning(self, "Cảnh báo", "Vui lòng chọn ít nhất một thiết bị!")
            return

        # Đặt trạng thái đang chạy và reset cờ dừng
        self.is_chplay_running = True
        self.stop_chplay = False

        # Đánh dấu nút là đã chọn
        self.ui.chplay.setChecked(True)

        # Cập nhật trạng thái các thiết bị trong bảng
        for row, _, _, _, _ in selected_devices:
            status_item = self.ui.tableWidget.item(row, 5)
            if status_item:
                status_item.setText("Mở Google Play...")
                QApplication.processEvents()



        # Dictionary để theo dõi message cuối cùng cho mỗi thiết bị (tránh duplicate)
        last_progress_messages = {}

        # Hàm callback để cập nhật progress (thông báo captcha, etc.)
        def on_progress(message):
            # Parse message để lấy serial và nội dung thực
            if "|" in message:
                serial, actual_message = message.split("|", 1)

                # Kiểm tra xem message này đã được xử lý chưa
                if serial in last_progress_messages and last_progress_messages[serial] == actual_message:
                    return  # Bỏ qua message duplicate

                # Lưu message mới
                last_progress_messages[serial] = actual_message

                # Tìm thiết bị cụ thể theo serial
                for row, dev_serial, _, _, _ in selected_devices:
                    if dev_serial == serial:
                        status_item = self.ui.tableWidget.item(row, 5)
                        if status_item:
                            # Chỉ cập nhật nếu chưa hoàn thành
                            current_text = status_item.text()
                            if not current_text.startswith("✅"):
                                status_item.setText(actual_message)
                                QApplication.processEvents()
                        break
            else:
                # Fallback: cập nhật tất cả nếu không có serial (backward compatibility)
                for row, _, _, _, _ in selected_devices:
                    status_item = self.ui.tableWidget.item(row, 5)
                    if status_item:
                        current_text = status_item.text()
                        if not current_text.startswith("✅"):
                            status_item.setText(message)
                            QApplication.processEvents()

        # Hàm callback để cập nhật kết quả
        def on_result(success, serial):
            # Tìm thiết bị trong danh sách đã chọn
            for row, dev_serial, _, _, _ in selected_devices:
                if dev_serial == serial:
                    # Cập nhật trạng thái trong bảng
                    status_item = self.ui.tableWidget.item(row, 5)
                    if status_item:
                        if success:
                            status_item.setText("✅ Đã đăng nhập Google Play thành công")
                        else:
                            status_item.setText("🚀 Đã mở Google Play")
                        QApplication.processEvents()
                    break

        # Biến để theo dõi số threads đã hoàn thành
        self.chplay_completed_count = 0
        self.chplay_results = {}

        # Hàm callback khi một thread hoàn thành (như MUA)
        def on_finished_single(serial, result):
            self.chplay_completed_count += 1
            self.chplay_results[serial] = result

            # Kiểm tra nếu tất cả threads đã hoàn thành
            if self.chplay_completed_count >= len(selected_devices):
                # Timer đã được loại bỏ để tránh overhead

                # Đặt lại trạng thái
                self.is_chplay_running = False
                self.stop_chplay = False

                # Đặt lại trạng thái nút
                self.ui.chplay.setChecked(False)

                # KHÔNG cập nhật trạng thái chung để tránh ghi đè trạng thái cá nhân
                # Mỗi thiết bị đã có trạng thái riêng từ on_result callback
                print(f"✅ Tất cả {len(selected_devices)} thiết bị chplay đã hoàn thành")

                # Cập nhật giao diện ngay lập tức - gọi nhiều lần để đảm bảo
                QApplication.processEvents()
                QApplication.processEvents()  # Gọi 2 lần để đảm bảo

                # Force repaint table
                self.ui.tableWidget.repaint()

                # Thống kê kết quả
                success_count = sum(1 for result in self.chplay_results.values() if result)
                fail_count = sum(1 for result in self.chplay_results.values() if result is False)

                # Thông báo hoàn thành
                self.log_message(f"✅ Đã hoàn thành quá trình đăng nhập Google Play: {success_count} thành công, {fail_count} thất bại")

        # Chạy từng thiết bị riêng biệt như MUA - không dùng MultiThread
        self.chplay_threads = []  # Lưu danh sách threads để theo dõi

        for row, serial, password, auth_secret, data in selected_devices:
            # Tạo thread riêng cho từng thiết bị như MUA
            thread = ChPlayThread(
                serial=serial,
                device_index=row,
                total_devices=len(selected_devices),
                adb_path=ADB_PATH,
                password=password,
                auth_secret=auth_secret,
                data=data,
                skip_captcha=False,  # Chplay thủ công KHÔNG bỏ qua captcha
                autok_instance=self  # Truyền self để kiểm tra stop flag như MUA
            )

            # Kết nối signals như MUA
            from PyQt5.QtCore import Qt
            thread.result.connect(on_result, Qt.QueuedConnection)
            thread.progress.connect(on_progress, Qt.QueuedConnection)
            thread.finished.connect(lambda serial=serial: on_finished_single(serial, True), Qt.QueuedConnection)

            # Lưu thread và start
            self.chplay_threads.append(thread)
            thread.start()

            self.log_message(f"🚀 Đã khởi động ChPlay thread cho thiết bị {serial} (như MUA)")

        self.log_message(f"✅ Đã khởi động {len(selected_devices)} ChPlay threads (theo cách MUA)")

        # Không cần timer vì đã có callback trực tiếp từ threads
        # Timer gây ra overhead không cần thiết

        # Thông báo
        self.log_message(f"🔄 Đã bắt đầu quá trình đăng nhập Google Play trên {len(selected_devices)} thiết bị")

    def chplay_login_sync(self, skip_captcha=False):
        """
        Thực hiện đăng nhập bằng nút Google Play và ĐỢI HOÀN THÀNH (dùng trong restore_email)
        :param skip_captcha: True để bỏ qua bước chờ vượt captcha khi chạy auto
        """
        # Kiểm tra license - chỉ ALL_BLOCKED mới bị khóa
        app = QApplication.instance()
        allowed_features = app.property("allowed_features")
        if allowed_features and allowed_features.upper() == "ALL_BLOCKED":
            self.log_message("❌ Tính năng ChPlay Sync bị khóa do license ALL_BLOCKED")
            return

        # Import lớp ChPlayThread từ chplay.py (như MUA)
        from chplay import ChPlayThread
        import threading

        # Kiểm tra xem có thiết bị nào được chọn không
        selected_devices = []
        for row in range(self.ui.tableWidget.rowCount()):
            widget = self.ui.tableWidget.cellWidget(row, 0)
            if widget:
                checkbox = widget.findChild(QCheckBox)
                if checkbox and checkbox.isChecked():
                    serial_item = self.ui.tableWidget.item(row, 1)
                    if serial_item:
                        # Lấy mật khẩu từ tableWidget nếu có
                        password = None
                        password_item = self.ui.tableWidget.item(row, 2)
                        if password_item and password_item.text().strip():
                            password = password_item.text().strip()

                        # Lấy mã xác thực từ cột authen (cột 3) nếu có
                        auth_secret = None
                        auth_item = self.ui.tableWidget.item(row, 3)
                        if auth_item and auth_item.text().strip():
                            auth_secret = auth_item.text().strip()

                        # Lấy data từ cột data (cột 4) nếu có
                        data = None
                        data_item = self.ui.tableWidget.item(row, 4)
                        if data_item and data_item.text().strip():
                            data = data_item.text().strip()

                        # Thêm vào danh sách thiết bị đã chọn
                        selected_devices.append((row, serial_item.text().strip(), password, auth_secret, data))

        if not selected_devices:
            self.log_message("⚠️ Không có thiết bị nào được chọn cho chplay login")
            return

        # Cập nhật trạng thái các thiết bị trong bảng
        for row, _, _, _, _ in selected_devices:
            status_item = self.ui.tableWidget.item(row, 5)
            if status_item:
                status_item.setText("Đang mở Google Play...")
                QApplication.processEvents()

        # Biến để theo dõi hoàn thành
        completion_event = threading.Event()
        self.chplay_sync_completion_event = completion_event  # Lưu để có thể dừng từ bên ngoài
        results = {}

        # Hàm callback để cập nhật progress (thông báo captcha, etc.) - SYNC
        def on_progress_sync(message):
            # Parse message để lấy serial và nội dung thực
            if "|" in message:
                serial, actual_message = message.split("|", 1)
                # Tìm thiết bị cụ thể theo serial
                for row, dev_serial, _, _, _ in selected_devices:
                    if dev_serial == serial:
                        status_item = self.ui.tableWidget.item(row, 5)
                        if status_item:
                            # Chỉ cập nhật nếu chưa hoàn thành
                            current_text = status_item.text()
                            if not current_text.startswith("✅"):
                                status_item.setText(actual_message)
                                QApplication.processEvents()
                        break
            else:
                # Fallback: cập nhật tất cả nếu không có serial (backward compatibility)
                for row, _, _, _, _ in selected_devices:
                    status_item = self.ui.tableWidget.item(row, 5)
                    if status_item:
                        current_text = status_item.text()
                        if not current_text.startswith("✅"):
                            status_item.setText(message)
                            QApplication.processEvents()

        # Hàm callback để cập nhật kết quả
        def on_result(success, serial):
            # Tìm thiết bị trong danh sách đã chọn
            for row, dev_serial, _, _, _ in selected_devices:
                if dev_serial == serial:
                    # Cập nhật trạng thái trong bảng
                    status_item = self.ui.tableWidget.item(row, 5)
                    if status_item:
                        if success:
                            status_item.setText("✅ Đăng nhập Google Play thành công")
                        else:
                            status_item.setText("Đã mơ Google Play ok")
                        QApplication.processEvents()
                    break

        # Hàm callback khi tất cả các luồng hoàn thành
        def on_finished(final_results):
            nonlocal results
            results = final_results

            # KHÔNG cập nhật trạng thái chung để tránh ghi đè trạng thái cá nhân
            # Mỗi thiết bị đã có trạng thái riêng từ on_result callback

            # Cập nhật giao diện ngay lập tức - gọi nhiều lần để đảm bảo
            QApplication.processEvents()
            QApplication.processEvents()  # Gọi 2 lần để đảm bảo

            # Force repaint table
            self.ui.tableWidget.repaint()

            # Thống kê kết quả
            success_count = sum(1 for result in results.values() if result)
            fail_count = sum(1 for result in results.values() if result is False)

            # Thông báo hoàn thành
            self.log_message(f"✅ Đã hoàn thành quá trình đăng nhập Google Play: {success_count} thành công, {fail_count} thất bại")

            # Báo hiệu hoàn thành
            completion_event.set()

        # Chạy từng thiết bị riêng biệt như MUA - không dùng MultiThread (SYNC)
        self.chplay_sync_threads = []  # Lưu danh sách threads để theo dõi
        self.chplay_sync_completed_count = 0
        self.chplay_sync_results = {}

        # Hàm callback khi một thread hoàn thành (SYNC)
        def on_finished_sync_single(serial, result):
            self.chplay_sync_completed_count += 1
            self.chplay_sync_results[serial] = result

            # Kiểm tra nếu tất cả threads đã hoàn thành
            if self.chplay_sync_completed_count >= len(selected_devices):
                on_finished(self.chplay_sync_results)

        for row, serial, password, auth_secret, data in selected_devices:
            # Tạo thread riêng cho từng thiết bị như MUA (SYNC)
            thread = ChPlayThread(
                serial=serial,
                device_index=row,
                total_devices=len(selected_devices),
                adb_path=ADB_PATH,
                password=password,
                auth_secret=auth_secret,
                data=data,
                skip_captcha=skip_captcha,  # Truyền thông tin bỏ qua captcha
                autok_instance=self  # Truyền self để kiểm tra stop flag như MUA
            )

            # Kết nối signals như MUA (SYNC)
            from PyQt5.QtCore import Qt
            thread.result.connect(on_result, Qt.QueuedConnection)
            thread.progress.connect(on_progress_sync, Qt.QueuedConnection)
            thread.finished.connect(lambda serial=serial: on_finished_sync_single(serial, True), Qt.QueuedConnection)

            # Lưu thread và start
            self.chplay_sync_threads.append(thread)
            thread.start()

        # Thông báo
        self.log_message(f"🔄 Đã bắt đầu quá trình đăng nhập Google Play trên {len(selected_devices)} thiết bị (SYNC)")

        # ĐỢI HOÀN THÀNH với timeout và kiểm tra stop flag
        self.log_message("⏳ Đang đợi chplay login hoàn thành...")
      
        # Đợi với timeout 1 giây mỗi lần để có thể kiểm tra stop flag
        max_wait_time = 300  # Tối đa 5 phút
        waited_time = 0

        while not completion_event.is_set() and waited_time < max_wait_time:
            # Kiểm tra stop flag
            if self.stop_start_actions or self.stop_chplay:
                self.log_message("⚠️ Đã nhận tín hiệu dừng - Dừng chplay login sync")
                # Dừng chplay threads an toàn
                self.safe_stop_chplay_threads()
                break

            # Đợi 1 giây và cập nhật UI
            completion_event.wait(timeout=1)
            waited_time += 1
            QApplication.processEvents()

            if waited_time % 10 == 0:  # Log mỗi 10 giây
                self.log_message(f"⏳ Vẫn đang đợi chplay login... ({waited_time}s)")

        if completion_event.is_set():
            self.log_message("✅ ChPlay login đã hoàn thành, tiếp tục với action tiếp theo")
        elif waited_time >= max_wait_time:
            self.log_message("⚠️ ChPlay login timeout sau 5 phút - Tiếp tục với action tiếp theo")
        else:
            self.log_message("⚠️ ChPlay login đã bị dừng - Tiếp tục với action tiếp theo")

        # Cleanup như MUA
        if hasattr(self, 'chplay_sync_completion_event'):
            delattr(self, 'chplay_sync_completion_event')
        if hasattr(self, 'chplay_sync_threads'):
            delattr(self, 'chplay_sync_threads')
        if hasattr(self, 'chplay_sync_completed_count'):
            delattr(self, 'chplay_sync_completed_count')
        if hasattr(self, 'chplay_sync_results'):
            delattr(self, 'chplay_sync_results')

    def changer(self):
        """Thực hiện chức năng changer thông qua API"""
        # Kiểm tra xem có thiết bị nào được chọn không
        selected_devices = []
        for row in range(self.ui.tableWidget.rowCount()):
            widget = self.ui.tableWidget.cellWidget(row, 0)
            if widget:
                checkbox = widget.findChild(QCheckBox)
                if checkbox and checkbox.isChecked():
                    serial_item = self.ui.tableWidget.item(row, 1)
                    if serial_item:
                        selected_devices.append((row, serial_item.text().strip()))

        if not selected_devices:
            QMessageBox.warning(self, "Cảnh báo", "Vui lòng chọn ít nhất một thiết bị!")
            return

        # Lấy giá trị port doma
        port_doma = "60708"  # Giá trị mặc định
        if hasattr(self.ui, 'lineEdit_port_doma'):
            port_doma = self.ui.lineEdit_port_doma.text().strip()
            if not port_doma:
                port_doma = "60708"

        # Cập nhật trạng thái của tất cả các thiết bị được xử lý
        for row, device_serial in selected_devices:
            status_item = self.ui.tableWidget.item(row, 5)
            if status_item:
                status_item.setText(f"Đang chuẩn bị change máy: {device_serial}")
                QApplication.processEvents()
                self.log_message(f"🔄 Chuẩn bị change máy cho thiết bị: {device_serial}")

        # Tạo một hàm để xử lý từng thiết bị
        def process_device(row_serial_tuple):
            row, serial = row_serial_tuple
            try:
                # Kiểm tra cờ dừng trước khi bắt đầu
                if hasattr(self, 'stop_changing_info') and self.stop_changing_info:
                    status = "⚠️ Đã dừng change info theo yêu cầu"
                    status_item = self.ui.tableWidget.item(row, 5)
                    if status_item:
                        status_item.setText(status)
                    self.log_message(f"{status} - Thiết bị {serial} (dòng {row + 1})")
                    return status

                # Lấy Device ID từ cột Devices (cột 0, index 0)
                device_item = self.ui.tableWidget.item(row, 0)
                device_id = device_item.text().strip() if device_item and device_item.text().strip() else ""

                # In thông tin debug
                self.log_message(f"🔍 Device ID từ cột Devices: '{device_id}'")

                if not device_id:
                    # Nếu không có ID, sử dụng serial làm ID
                    device_id = serial
                    self.log_message(f"🔍 Sử dụng serial làm ID vì không có ID trong cột Pass")

                # Cập nhật trạng thái
                status_item = self.ui.tableWidget.item(row, 5)
                if status_item:
                    status_item.setText(f"Đang change info cho thiết bị: {device_id}")
                    QApplication.processEvents()

                # Hiển thị thông tin trong log
                self.log_message(f"🔄 Bắt đầu change info cho thiết bị: {device_id}")

                # Tạo URL API với chỉ mã máy (không cần email)
                api_url = f"http://127.0.0.1:{port_doma}/?changeinfo={urllib.parse.quote(device_id)}"

                # In thông tin debug về URL API
                self.log_message(f"🔗 Gửi yêu cầu đến URL: {api_url}")

                # Gửi yêu cầu đến API
                try:
                    # Cập nhật trạng thái
                    if status_item:
                        status_item.setText(f"Đang gửi yêu cầu change info cho thiết bị: {device_id}")
                        QApplication.processEvents()

                    # Hiển thị thông tin trong log
                    self.log_message(f"🔄 Đang gửi yêu cầu change info...")

                    # Gửi yêu cầu đến API
                    response = requests.get(api_url, timeout=30)

                    # Kiểm tra kết quả
                    if response.status_code == 200:
                        status = f"✅ Đã change info cho thiết bị: {device_id}"
                    else:
                        status = f"❌ Không thể change info cho thiết bị: {device_id} (HTTP {response.status_code})"
                        # Ghi log lỗi HTTP
                        self.log_message(f"❌ Lỗi HTTP {response.status_code} khi gửi yêu cầu đến API")
                        # Dừng quá trình và không tiếp tục với thiết bị tiếp theo
                        return status
                except requests.RequestException as e:
                    error_msg = f"❌ LỖI KẾT NỐI API: {str(e)}"
                    status = error_msg

                    # Ghi log lỗi kết nối API với định dạng nổi bật
                    self.log_message("=" * 50)
                    self.log_message(error_msg)
                    self.log_message("=" * 50)

                    # Cập nhật trạng thái trong bảng với thông báo lỗi
                    if status_item:
                        status_item.setText(error_msg)
                        QApplication.processEvents()

                    # Dừng quá trình và không tiếp tục với thiết bị tiếp theo
                    return status

                # Cập nhật trạng thái trong bảng
                if status_item:
                    status_item.setText(status)
                    QApplication.processEvents()

                # Hiển thị thông tin trong log
                self.log_message(f"{status}")
                return status

            except Exception as e:
                error_msg = f"Lỗi khi change info: {str(e)}"
                status_item = self.ui.tableWidget.item(row, 5)
                if status_item:
                    status_item.setText(f"❌ {error_msg}")
                    QApplication.processEvents()
                self.log_message(f"❌ {error_msg}")
                return error_msg

        # Xử lý từng thiết bị tuần tự
        self.log_message(f"Bắt đầu change info cho {len(selected_devices)} thiết bị. Quá trình này có thể mất vài phút...")

        # Tạo danh sách để lưu trữ kết quả
        results = []

        # Biến để kiểm tra xem có lỗi kết nối API không
        api_connection_error = False

        # Xử lý từng thiết bị tuần tự
        for i, device in enumerate(selected_devices):
            result = process_device(device)
            results.append(result)

            # Kiểm tra xem có lỗi kết nối API không
            if result and isinstance(result, str) and ("LỖI KẾT NỐI API" in result or "HTTP" in result):
                # Hiển thị thông báo lỗi nổi bật trong log
                self.log_message("=" * 50)
                self.log_message("❌ PHÁT HIỆN LỖI KẾT NỐI API HOẶC LỖI HTTP")
                self.log_message(f"❌ Chi tiết lỗi: {result}")
                self.log_message("❌ ĐANG DỪNG TẤT CẢ CÁC THIẾT BỊ")
                self.log_message("=" * 50)

                # Cập nhật trạng thái của tất cả các thiết bị còn lại
                for remaining_row, _ in selected_devices[i+1:]:
                    remaining_status_item = self.ui.tableWidget.item(remaining_row, 5)
                    if remaining_status_item:
                        remaining_status_item.setText("❌ Đã dừng do lỗi kết nối API")
                        QApplication.processEvents()

                api_connection_error = True
                break

            # Đợi một chút giữa các thiết bị để tránh quá tải API
            if i < len(selected_devices) - 1:  # Không đợi sau thiết bị cuối cùng
                self.log_message(f"Đợi 3 giây trước khi xử lý thiết bị tiếp theo...")
                time.sleep(3)

        # Nếu có lỗi kết nối API, dừng quá trình và không đợi 50 giây
        if api_connection_error:
            self.log_message("=" * 50)
            self.log_message("❌ ĐÃ DỪNG QUÁ TRÌNH DO LỖI KẾT NỐI API")
            self.log_message("❌ KIỂM TRA XEM API CÓ ĐANG CHẠY KHÔNG")
            self.log_message("❌ KHÔNG ĐỢI 50 GIÂY")
            self.log_message("❌ KHÔNG HOÀN THÀNH QUÁ TRÌNH CHANGE INFO")
            self.log_message("=" * 50)

            # Cập nhật trạng thái của tất cả các thiết bị đã chọn
            for row, _ in selected_devices:
                status_item = self.ui.tableWidget.item(row, 5)
                if status_item and "❌" not in status_item.text():
                    status_item.setText("❌ Đã dừng do lỗi kết nối API")
                    QApplication.processEvents()

            return

        # Đợi thêm 50 giây sau khi hoàn thành tất cả các thiết bị để đảm bảo API đã xử lý xong
        self.log_message("Đợi thêm 50 giây để đảm bảo API đã xử lý xong...")

        # Đếm ngược 50 giây để hoàn thành bước change info
        for remaining in range(50, 0, -1):
            # Kiểm tra cờ dừng
            if hasattr(self, 'stop_changing_info') and self.stop_changing_info:
                self.log_message("⚠️ Đã dừng change info theo yêu cầu")
                return

            self.log_message(f"Còn {remaining} giây để hoàn thành bước change info...")

            # Cập nhật trạng thái của tất cả các thiết bị được chọn
            for row, _ in selected_devices:
                status_item = self.ui.tableWidget.item(row, 5)
                if status_item:
                    status_item.setText(f"Đang change info, đợi {remaining}s để hoàn thành...")
                    QApplication.processEvents()

            time.sleep(1)
            QApplication.processEvents()

        # Hoàn thành bước change info sau khi đợi 50 giây
        self.log_message("✅ Đã hoàn thành thời gian chờ 50 giây cho bước change info")

        # Tự động cập nhật trạng thái sau khi change info
        self.log_message("🔄 Đang cập nhật trạng thái thiết bị sau change info...")
        QTimer.singleShot(2000, lambda: self.get_device_status(selected_only=True))  # Chỉ thiết bị đã chọn

    def changer_button_clicked(self):
        """Xử lý khi nút changer được click"""
        try:
            if self.ui.changer.isChecked():
                # Nút được bật - bắt đầu changer
                self.log_message("🔄 Bắt đầu chức năng Change Info...")

                # Kiểm tra xem có thiết bị nào được chọn không
                selected_devices = []
                for row in range(self.ui.tableWidget.rowCount()):
                    widget = self.ui.tableWidget.cellWidget(row, 0)
                    if widget:
                        checkbox = widget.findChild(QCheckBox)
                        if checkbox and checkbox.isChecked():
                            serial_item = self.ui.tableWidget.item(row, 1)
                            if serial_item:
                                selected_devices.append((row, serial_item.text().strip()))

                if not selected_devices:
                    QMessageBox.warning(self, "Cảnh báo", "Vui lòng chọn ít nhất một thiết bị!")
                    self.ui.changer.setChecked(False)
                    return

                # Thực hiện changer
                try:
                    self.changer()
                    self.log_message("✅ Đã hoàn thành chức năng Change Info")
                except Exception as changer_error:
                    self.log_message(f"❌ Lỗi khi thực hiện changer: {str(changer_error)}")
                finally:
                    # Luôn reset nút về trạng thái unchecked sau khi hoàn thành
                    self.ui.changer.setChecked(False)
                    self.log_message("🔄 Đã reset nút changer về trạng thái ban đầu")

                    # Tự động get status sau changer (backup plan)
                    QTimer.singleShot(2000, lambda: self.get_device_status(selected_only=True))

            else:
                # Nút được tắt - dừng changer
                self.log_message("⚠️ Dừng chức năng Change Info...")
                self.ui.changer.setChecked(False)

        except Exception as e:
            self.log_message(f"❌ Lỗi khi xử lý nút changer: {str(e)}")
            self.ui.changer.setChecked(False)

    def restore_button_clicked(self):
        """Xử lý khi nút restore được click"""
        try:
            if self.ui.restore.isChecked():
                # Nút được bật - bắt đầu restore
                self.log_message("🔄 Bắt đầu chức năng Restore Email...")

                # Kiểm tra xem có thiết bị nào được chọn không
                selected_devices = []
                for row in range(self.ui.tableWidget.rowCount()):
                    widget = self.ui.tableWidget.cellWidget(row, 0)
                    if widget:
                        checkbox = widget.findChild(QCheckBox)
                        if checkbox and checkbox.isChecked():
                            serial_item = self.ui.tableWidget.item(row, 1)
                            if serial_item:
                                selected_devices.append((row, serial_item.text().strip()))

                if not selected_devices:
                    QMessageBox.warning(self, "Cảnh báo", "Vui lòng chọn ít nhất một thiết bị!")
                    self.ui.restore.setChecked(False)
                    return

                # Thực hiện restore thủ công (không cần đợi 65 giây)
                try:
                    self.restore_email(is_manual=True)
                    self.log_message("✅ Đã hoàn thành chức năng Restore Email thủ công")
                except Exception as restore_error:
                    self.log_message(f"❌ Lỗi khi thực hiện restore: {str(restore_error)}")
                finally:
                    # Luôn reset nút về trạng thái unchecked sau khi hoàn thành
                    self.ui.restore.setChecked(False)
                    self.log_message("🔄 Đã reset nút restore về trạng thái ban đầu")

                    # Tự động get status sau restore (backup plan)
                    QTimer.singleShot(2000, lambda: self.get_device_status(selected_only=True))

            else:
                # Nút được tắt - dừng restore
                self.log_message("⚠️ Dừng chức năng Restore Email...")
                self.ui.restore.setChecked(False)

        except Exception as e:
            self.log_message(f"❌ Lỗi khi xử lý nút restore: {str(e)}")
            self.ui.restore.setChecked(False)

    def backup_button_clicked(self):
        """Xử lý khi nút backup được click"""
        try:
            if self.ui.backup.isChecked():
                # Nút được bật - bắt đầu backup
                self.log_message("🔄 Bắt đầu chức năng Backup...")

                # Kiểm tra xem có thiết bị nào được chọn không
                selected_devices = []
                for row in range(self.ui.tableWidget.rowCount()):
                    widget = self.ui.tableWidget.cellWidget(row, 0)
                    if widget:
                        checkbox = widget.findChild(QCheckBox)
                        if checkbox and checkbox.isChecked():
                            serial_item = self.ui.tableWidget.item(row, 1)
                            if serial_item:
                                selected_devices.append((row, serial_item.text().strip()))

                if not selected_devices:
                    QMessageBox.warning(self, "Cảnh báo", "Vui lòng chọn ít nhất một thiết bị!")
                    self.ui.backup.setChecked(False)
                    return

                # Thực hiện backup
                try:
                    self.backup()
                    self.log_message("✅ Đã hoàn thành chức năng Backup")
                except Exception as backup_error:
                    self.log_message(f"❌ Lỗi khi thực hiện backup: {str(backup_error)}")
                finally:
                    # Luôn reset nút về trạng thái unchecked sau khi hoàn thành
                    self.ui.backup.setChecked(False)
                    self.log_message("🔄 Đã reset nút backup về trạng thái ban đầu")

            else:
                # Nút được tắt - dừng backup
                self.log_message("⚠️ Dừng chức năng Backup...")
                self.ui.backup.setChecked(False)

        except Exception as e:
            self.log_message(f"❌ Lỗi khi xử lý nút backup: {str(e)}")
            self.ui.backup.setChecked(False)

    def backup(self):
        """Thực hiện chức năng backup thông qua API"""
        # Kiểm tra xem có thiết bị nào được chọn không
        selected_devices = []
        for row in range(self.ui.tableWidget.rowCount()):
            widget = self.ui.tableWidget.cellWidget(row, 0)
            if widget:
                checkbox = widget.findChild(QCheckBox)
                if checkbox and checkbox.isChecked():
                    serial_item = self.ui.tableWidget.item(row, 1)
                    if serial_item:
                        selected_devices.append((row, serial_item.text().strip()))

        if not selected_devices:
            QMessageBox.warning(self, "Cảnh báo", "Vui lòng chọn ít nhất một thiết bị!")
            return

        # Lấy giá trị port doma
        port_doma = "60708"  # Giá trị mặc định
        if hasattr(self.ui, 'lineEdit_port_doma'):
            port_doma = self.ui.lineEdit_port_doma.text().strip()
            if not port_doma:
                port_doma = "60708"

        # Cập nhật trạng thái của tất cả các thiết bị được xử lý
        for row, device_serial in selected_devices:
            status_item = self.ui.tableWidget.item(row, 5)
            if status_item:
                status_item.setText(f"Đang chuẩn bị backup: {device_serial}")
                QApplication.processEvents()
                self.log_message(f"🔄 Chuẩn bị backup cho thiết bị: {device_serial}")

        # Tạo một hàm để xử lý từng thiết bị
        def process_device(row_serial_tuple):
            row, serial = row_serial_tuple
            try:
                # Cập nhật trạng thái
                status_item = self.ui.tableWidget.item(row, 5)
                if status_item:
                    status_item.setText(f"Đang backup cho thiết bị: {serial}")
                    QApplication.processEvents()

                # Hiển thị thông tin trong log
                self.log_message(f"🔄 Bắt đầu backup cho thiết bị: {serial}")

                # Tạo URL API với serial thiết bị
                api_url = f"http://127.0.0.1:{port_doma}/?backup={urllib.parse.quote(serial)}"

                # In thông tin debug về URL API
                self.log_message(f"🔗 Gửi yêu cầu backup đến URL: {api_url}")

                # Gửi yêu cầu đến API
                try:
                    # Cập nhật trạng thái
                    if status_item:
                        status_item.setText(f"Đang gửi yêu cầu backup cho thiết bị: {serial}")
                        QApplication.processEvents()

                    # Hiển thị thông tin trong log
                    self.log_message(f"🔄 Đang gửi yêu cầu backup...")

                    # Gửi yêu cầu đến API
                    response = requests.get(api_url, timeout=30)

                    # Kiểm tra kết quả
                    if response.status_code == 200:
                        status = f"✅ Đã backup cho thiết bị: {serial}"
                    else:
                        status = f"❌ Không thể backup cho thiết bị: {serial} (HTTP {response.status_code})"
                        # Ghi log lỗi HTTP
                        self.log_message(f"❌ Lỗi HTTP {response.status_code} khi gửi yêu cầu backup đến API")
                        # Dừng quá trình và không tiếp tục với thiết bị tiếp theo
                        return status
                except requests.RequestException as e:
                    error_msg = f"❌ LỖI KẾT NỐI API BACKUP: {str(e)}"
                    status = error_msg

                    # Ghi log lỗi kết nối API với định dạng nổi bật
                    self.log_message("=" * 50)
                    self.log_message(error_msg)
                    self.log_message("=" * 50)

                    # Cập nhật trạng thái trong bảng với thông báo lỗi
                    if status_item:
                        status_item.setText(error_msg)
                        QApplication.processEvents()

                    # Dừng quá trình và không tiếp tục với thiết bị tiếp theo
                    return status

                # Cập nhật trạng thái trong bảng
                if status_item:
                    status_item.setText(status)
                    QApplication.processEvents()

                # Hiển thị thông tin trong log
                self.log_message(f"{status}")
                return status

            except Exception as e:
                error_msg = f"Lỗi khi backup: {str(e)}"
                status_item = self.ui.tableWidget.item(row, 5)
                if status_item:
                    status_item.setText(f"❌ {error_msg}")
                    QApplication.processEvents()
                self.log_message(f"❌ {error_msg}")
                return error_msg

        # Xử lý từng thiết bị tuần tự
        self.log_message(f"Bắt đầu backup cho {len(selected_devices)} thiết bị. Quá trình này có thể mất vài phút...")

        # Tạo danh sách để lưu trữ kết quả
        results = []

        # Biến để kiểm tra xem có lỗi kết nối API không
        api_connection_error = False

        # Xử lý từng thiết bị tuần tự
        for i, device in enumerate(selected_devices):
            result = process_device(device)
            results.append(result)

            # Kiểm tra xem có lỗi kết nối API không
            if result and isinstance(result, str) and ("LỖI KẾT NỐI API" in result or "HTTP" in result):
                # Hiển thị thông báo lỗi nổi bật trong log
                self.log_message("=" * 50)
                self.log_message("❌ PHÁT HIỆN LỖI KẾT NỐI API HOẶC LỖI HTTP")
                self.log_message(f"❌ Chi tiết lỗi: {result}")
                self.log_message("❌ ĐANG DỪNG TẤT CẢ CÁC THIẾT BỊ")
                self.log_message("=" * 50)

                # Cập nhật trạng thái của tất cả các thiết bị còn lại
                for remaining_row, _ in selected_devices[i+1:]:
                    remaining_status_item = self.ui.tableWidget.item(remaining_row, 5)
                    if remaining_status_item:
                        remaining_status_item.setText("❌ Đã dừng do lỗi kết nối API")
                        QApplication.processEvents()

                api_connection_error = True
                break

            # Đợi một chút giữa các thiết bị để tránh quá tải API
            if i < len(selected_devices) - 1:  # Không đợi sau thiết bị cuối cùng
                self.log_message(f"Đợi 1 giây trước khi xử lý thiết bị tiếp theo...")
                time.sleep(1)

        # Nếu có lỗi kết nối API, dừng quá trình
        if api_connection_error:
            self.log_message("=" * 50)
            self.log_message("❌ ĐÃ DỪNG QUÁ TRÌNH DO LỖI KẾT NỐI API")
            self.log_message("❌ KIỂM TRA XEM API CÓ ĐANG CHẠY KHÔNG")
            self.log_message("❌ KHÔNG HOÀN THÀNH QUÁ TRÌNH BACKUP")
            self.log_message("=" * 50)

            # Cập nhật trạng thái của tất cả các thiết bị đã chọn
            for row, _ in selected_devices:
                status_item = self.ui.tableWidget.item(row, 5)
                if status_item and "❌" not in status_item.text():
                    status_item.setText("❌ Đã dừng do lỗi kết nối API")
                    QApplication.processEvents()

            return

        # Thông báo hoàn thành
        self.log_message("✅ Đã hoàn thành backup cho tất cả thiết bị")

        # Tự động cập nhật trạng thái sau khi backup
        self.log_message("🔄 Đang cập nhật trạng thái thiết bị sau backup...")
        QTimer.singleShot(2000, lambda: self.get_device_status(selected_only=True))  # Chỉ thiết bị đã chọn

    def get_device_status(self, selected_only=False):
        """Lấy trạng thái của thiết bị từ API getstatus

        Args:
            selected_only (bool): True để chỉ kiểm tra thiết bị đã chọn, False để kiểm tra tất cả
        """
        # Lấy giá trị port doma
        port_doma = "60708"  # Giá trị mặc định
        if hasattr(self.ui, 'lineEdit_port_doma'):
            port_doma = self.ui.lineEdit_port_doma.text().strip()
            if not port_doma:
                port_doma = "60708"

        # Lấy danh sách thiết bị cần kiểm tra
        devices_to_check = []

        if selected_only:
            # Chỉ lấy thiết bị đã chọn
            for row in range(self.ui.tableWidget.rowCount()):
                widget = self.ui.tableWidget.cellWidget(row, 0)
                if widget:
                    checkbox = widget.findChild(QCheckBox)
                    if checkbox and checkbox.isChecked():
                        serial_item = self.ui.tableWidget.item(row, 1)
                        if serial_item and serial_item.text().strip():
                            devices_to_check.append((row, serial_item.text().strip()))
        else:
            # Lấy tất cả thiết bị trong bảng
            for row in range(self.ui.tableWidget.rowCount()):
                serial_item = self.ui.tableWidget.item(row, 1)
                if serial_item and serial_item.text().strip():
                    devices_to_check.append((row, serial_item.text().strip()))

        if not devices_to_check:
            if selected_only:
                self.log_message("⚠️ Không có thiết bị nào được chọn để kiểm tra trạng thái")
            else:
                self.log_message("⚠️ Không có thiết bị nào trong bảng để kiểm tra trạng thái")
            return

        device_type = "đã chọn" if selected_only else "tất cả"
        self.log_message(f"🔄 Đang kiểm tra trạng thái {len(devices_to_check)} thiết bị {device_type}...")

        # Tạo một hàm để xử lý từng thiết bị
        def check_device_status(row_serial_tuple):
            row, serial = row_serial_tuple
            try:
                # Tạo URL API với serial thiết bị
                api_url = f"http://127.0.0.1:{port_doma}/?getstatus={urllib.parse.quote(serial)}"

                # Gửi yêu cầu đến API
                response = requests.get(api_url, timeout=10)

                # Kiểm tra kết quả
                if response.status_code == 200:
                    try:
                        # Thử parse JSON response
                        response_data = response.json()
                        if isinstance(response_data, dict) and 'status' in response_data:
                            status_text = response_data['status']
                        else:
                            status_text = str(response_data)
                    except:
                        # Nếu không parse được JSON, dùng text
                        status_text = response.text.strip()

                    # Cập nhật trạng thái trong bảng (cột 5 = cột Log)
                    status_item = self.ui.tableWidget.item(row, 5)
                    if status_item:
                        # Thêm prefix để phân biệt với status khác
                        new_text = f"📊 {status_text}"
                        status_item.setText(new_text)
                        QApplication.processEvents()
                    else:
                        # Tạo QTableWidgetItem mới nếu chưa có
                        from PyQt5.QtWidgets import QTableWidgetItem
                        new_text = f"📊 {status_text}"
                        new_item = QTableWidgetItem(new_text)
                        self.ui.tableWidget.setItem(row, 5, new_item)
                        QApplication.processEvents()

                    return f"✅ {serial}: {status_text}"
                else:
                    error_msg = f"❌ HTTP {response.status_code}"

                    # Cập nhật trạng thái lỗi trong bảng (cột 5 = cột Log)
                    status_item = self.ui.tableWidget.item(row, 5)
                    if status_item:
                        status_item.setText(error_msg)
                    else:
                        from PyQt5.QtWidgets import QTableWidgetItem
                        new_item = QTableWidgetItem(error_msg)
                        self.ui.tableWidget.setItem(row, 5, new_item)
                    QApplication.processEvents()

                    return f"❌ {serial}: {error_msg}"

            except requests.RequestException as e:
                error_msg = f"❌ Lỗi kết nối: {str(e)}"
                display_msg = "❌ Lỗi kết nối API"

                # Cập nhật trạng thái lỗi trong bảng (cột 5 = cột Log)
                status_item = self.ui.tableWidget.item(row, 5)
                if status_item:
                    status_item.setText(display_msg)
                else:
                    from PyQt5.QtWidgets import QTableWidgetItem
                    new_item = QTableWidgetItem(display_msg)
                    self.ui.tableWidget.setItem(row, 5, new_item)
                QApplication.processEvents()

                return f"❌ {serial}: {error_msg}"

            except Exception as e:
                error_msg = f"❌ Lỗi: {str(e)}"
                display_msg = "❌ Lỗi không xác định"

                # Cập nhật trạng thái lỗi trong bảng (cột 5 = cột Log)
                status_item = self.ui.tableWidget.item(row, 5)
                if status_item:
                    status_item.setText(display_msg)
                else:
                    from PyQt5.QtWidgets import QTableWidgetItem
                    new_item = QTableWidgetItem(display_msg)
                    self.ui.tableWidget.setItem(row, 5, new_item)
                QApplication.processEvents()

                return f"❌ {serial}: {error_msg}"

        # Xử lý từng thiết bị tuần tự để tránh quá tải API
        success_count = 0
        error_count = 0

        for i, device in enumerate(devices_to_check):
            result = check_device_status(device)

            if result.startswith("✅"):
                success_count += 1
            else:
                error_count += 1

            # Log kết quả
            self.log_message(result)

            # Đợi một chút giữa các thiết bị để tránh quá tải API
            if i < len(devices_to_check) - 1:  # Không đợi sau thiết bị cuối cùng
                time.sleep(0.5)  # Đợi ngắn hơn vì chỉ là get status

        # Thông báo tổng kết
        self.log_message(f"✅ Đã kiểm tra trạng thái: {success_count} thành công, {error_count} lỗi")

    def closeEvent(self, _event):
        """Cleanup khi đóng ứng dụng"""
        try:
            self.log_message("🛑 Đang đóng ứng dụng và dọn dẹp tài nguyên...")

            # Dừng tất cả sub processes trước
            self.stop_all_sub_processes()

            # Force kill tất cả scrcpy processes
            self.force_kill_scrcpy()

            # Dừng tất cả proxy servers
            self.stop_all_proxies()

            # Timer đã được loại bỏ để tránh overhead

            # Dừng và cleanup chplay threads
            if hasattr(self, 'chplay_multi_thread') and self.chplay_multi_thread:
                try:
                    self.log_message("🛑 Đang dừng ChPlay threads...")
                    stopped_count = self.chplay_multi_thread.stop_all()
                    self.log_message(f"⚠️ Đã gửi tín hiệu dừng đến {stopped_count} ChPlay threads")

                    # Đợi threads kết thúc (tối đa 10 giây cho ChPlay)
                    import time
                    wait_time = 0
                    max_wait = 10  # Tăng từ 3s lên 10s cho ChPlay

                    while self.chplay_multi_thread.is_running() and wait_time < max_wait:
                        time.sleep(0.2)  # Tăng interval để giảm CPU usage
                        wait_time += 0.2

                        # Log tiến trình mỗi 2 giây
                        if int(wait_time * 10) % 20 == 0:  # Mỗi 2 giây
                            self.log_message(f"⏰ Đang đợi ChPlay threads kết thúc... ({wait_time:.1f}s/{max_wait}s)")

                        QApplication.processEvents()

                    if self.chplay_multi_thread.is_running():
                        self.log_message(f"⚠️ Một số ChPlay threads vẫn đang chạy sau {max_wait} giây")
                        # Force cleanup
                        try:
                            self.chplay_multi_thread = None
                            self.log_message("🗑️ Đã force cleanup ChPlay multi-thread")
                        except:
                            pass
                    else:
                        self.log_message("✅ Tất cả ChPlay threads đã dừng hoàn toàn")

                except Exception as e:
                    self.log_message(f"❌ Lỗi khi dừng ChPlay threads: {e}")
                    # Vẫn cố gắng force cleanup nếu có lỗi
                    try:
                        if hasattr(self, 'chplay_multi_thread') and self.chplay_multi_thread:
                            self.chplay_multi_thread = None
                            self.log_message("🗑️ Đã force cleanup ChPlay multi-thread do lỗi")
                    except:
                        pass

            # Cleanup temp files
            for file in os.listdir('.'):
                if file.startswith('screenshot_') and file.endswith('.png'):
                    os.remove(file)
                if file == "screen_temp.png":
                    os.remove(file)

            # Disconnect ADB
            self.log_message("Đang ngắt kết nối ADB...")
            subprocess.run([ADB_PATH, "kill-server"], check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

            self.log_message("Đã dọn dẹp tất cả tài nguyên")
        except Exception as e:
            self.log_message(f"Lỗi cleanup: {e}")

        # Đảm bảo rằng tất cả các tiến trình con đã được đóng
        try:
            # Tắt tất cả các tiến trình scrcpy (thử nhiều cách)
            self.log_message("Đang tắt tất cả các tiến trình scrcpy...")

            # Cách 1: taskkill với /F /IM
            subprocess.run(["taskkill", "/F", "/IM", "scrcpy.exe"],
                          stdout=subprocess.DEVNULL,
                          stderr=subprocess.DEVNULL,
                          creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

            # Cách 2: taskkill với /F /T (kill cả process tree)
            subprocess.run(["taskkill", "/F", "/T", "/IM", "scrcpy.exe"],
                          stdout=subprocess.DEVNULL,
                          stderr=subprocess.DEVNULL,
                          creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

            # Cách 3: Tìm và kill theo PID
            try:
                result = subprocess.run(["wmic", "process", "where", "name='scrcpy.exe'", "get", "processid", "/value"],
                                      capture_output=True, text=True,
                                      creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
                for line in result.stdout.split('\n'):
                    if 'ProcessId=' in line:
                        pid = line.split('=')[1].strip()
                        if pid and pid.isdigit():
                            subprocess.run(["taskkill", "/F", "/PID", pid],
                                          stdout=subprocess.DEVNULL,
                                          stderr=subprocess.DEVNULL,
                                          creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
            except:
                pass

            # Tắt tất cả các tiến trình adb
            subprocess.run(["taskkill", "/F", "/IM", "adb.exe"],
                          stdout=subprocess.DEVNULL,
                          stderr=subprocess.DEVNULL,
                          creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

            subprocess.run(["taskkill", "/F", "/T", "/IM", "adb.exe"],
                          stdout=subprocess.DEVNULL,
                          stderr=subprocess.DEVNULL,
                          creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

            # Tắt tất cả các tiến trình python con (cho trường hợp đóng gói)
            subprocess.run(["taskkill", "/F", "/IM", "python.exe"],
                          stdout=subprocess.DEVNULL,
                          stderr=subprocess.DEVNULL,
                          creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

        except Exception as e:
            print(f"Lỗi khi tắt các tiến trình con: {e}")

        # Force exit để đảm bảo app thực sự tắt
        import sys
        sys.exit(0)

    # ===== PROXY METHODS =====

    def load_proxies(self):
        """Tải danh sách proxy từ file"""
        try:
            proxy_file = self.proxy_config['proxy_file']
            self.proxy_list = []

            if not os.path.exists(proxy_file):
                # Tạo file proxy mẫu nếu chưa có
                os.makedirs(os.path.dirname(proxy_file), exist_ok=True)
                with open(proxy_file, 'w', encoding='utf-8') as f:
                    f.write("# File proxy - Thêm proxy theo format:\n")
                    f.write("# ip:port:username:password\n")
                    f.write("# hoặc ip:port (không có auth)\n")
                    f.write("# Dòng bắt đầu bằng # sẽ bị bỏ qua\n")
                self.log_message(f"📁 Đã tạo file proxy mẫu: {proxy_file}")
                return

            with open(proxy_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            for line in lines:
                line = line.strip()
                if not line or line.startswith('#'):
                    continue

                try:
                    # Parse proxy format: ip:port:user:pass hoặc ip:port
                    if line.startswith('socks5://'):
                        # Format: socks5://user:pass@ip:port
                        parts = line.replace('socks5://', '').split('@')
                        if len(parts) == 2:
                            auth_part, server_part = parts
                            if ':' in auth_part:
                                user, pwd = auth_part.split(':', 1)
                            else:
                                user, pwd = auth_part, ''
                            if ':' in server_part:
                                ip, port = server_part.split(':', 1)
                                self.proxy_list.append((ip, int(port), user, pwd))
                    else:
                        # Format: ip:port:user:pass hoặc ip:port
                        parts = line.split(':')
                        if len(parts) >= 2:
                            ip = parts[0]
                            port = int(parts[1])
                            user = parts[2] if len(parts) > 2 else ''
                            pwd = parts[3] if len(parts) > 3 else ''
                            self.proxy_list.append((ip, port, user, pwd))

                except (ValueError, IndexError) as e:
                    logger.warning(f"Invalid proxy format: {line} - {e}")
                    continue

            self.log_message(f"📋 Đã tải {len(self.proxy_list)} proxy từ file")

        except Exception as e:
            logger.error(f"Error loading proxies: {e}")
            self.log_message(f"❌ Lỗi tải proxy: {e}")

    def get_lan_ip(self):
        """Lấy IP LAN của máy tính (cache để tránh gọi lại nhiều lần)"""
        if self.lan_ip is None:
            try:
                import socket
                hostname = socket.gethostname()
                self.lan_ip = socket.gethostbyname(hostname)
                self.log_message(f"🌐 Detected LAN IP: {self.lan_ip}")
            except Exception as e:
                self.log_message(f"⚠️ Không thể lấy IP LAN, sử dụng 127.0.0.1: {e}")
                self.lan_ip = "127.0.0.1"
        return self.lan_ip

    def load_email_proxy_mapping(self):
        """Đọc file mail.txt và tạo mapping email -> proxy"""
        try:
            # Đọc email từ file mail.txt sử dụng path_manager
            try:
                mail_file_path = path_manager.get_txt_path("mail.txt")
            except Exception as e:
                mail_file_path = path_manager.get_data_path("mail.txt")

            if not os.path.exists(mail_file_path):
                self.log_message(f"⚠️ File {mail_file_path} không tồn tại")
                return {}

            email_proxy_mapping = {}

            with open(mail_file_path, "r", encoding='utf-8') as file:
                lines = file.readlines()

            for line in lines:
                line = line.strip()
                if not line or line.startswith('#'):
                    continue

                # Parse line: email|password|auth|proxy (proxy là optional)
                parts = line.split('|')
                if len(parts) >= 3:  # Ít nhất phải có email|password|auth
                    email = parts[0].strip()

                    # Kiểm tra có trường proxy (trường thứ 4) không
                    if len(parts) >= 4 and parts[3].strip():
                        proxy_str = parts[3].strip()

                        # Parse proxy string để lấy thông tin
                        proxy_info = self.parse_proxy_string(proxy_str)
                        if proxy_info:
                            email_proxy_mapping[email] = proxy_info
                            self.log_message(f"📧 Email {email} có proxy cố định: {proxy_info[0]}:{proxy_info[1]}")
                        else:
                            self.log_message(f"⚠️ Proxy không hợp lệ cho email {email}: {proxy_str}")
                    # Nếu không có proxy, email sẽ được gán random

            self.email_proxy_mapping = email_proxy_mapping
            self.log_message(f"📊 Đã load {len(email_proxy_mapping)} email có proxy cố định")
            return email_proxy_mapping

        except Exception as e:
            self.log_message(f"❌ Lỗi khi đọc email-proxy mapping: {e}")
            return {}

    def parse_proxy_string(self, proxy_str):
        """Parse proxy string thành tuple (ip, port, user, pwd)"""
        try:
            # Hỗ trợ nhiều format:
            # 1. socks5://user:pass@ip:port
            # 2. socks5://user:pass:ip:port
            # 3. user:pass:ip:port
            # 4. ip:port:user:pass
            # 5. ip:port

            # Loại bỏ prefix socks5:// nếu có
            if proxy_str.startswith('socks5://'):
                proxy_str = proxy_str[9:]  # Bỏ 'socks5://'

            # Kiểm tra format user:pass@ip:port
            if '@' in proxy_str:
                auth_part, addr_part = proxy_str.split('@', 1)
                if ':' in auth_part and ':' in addr_part:
                    user, pwd = auth_part.split(':', 1)
                    ip, port = addr_part.split(':', 1)
                    return (ip.strip(), int(port.strip()), user.strip(), pwd.strip())

            # Kiểm tra format với dấu :
            parts = proxy_str.split(':')
            if len(parts) == 4:
                # Format: user:pass:ip:port hoặc ip:port:user:pass
                # Thử detect bằng cách kiểm tra part nào là số (port)
                try:
                    # Thử format user:pass:ip:port
                    port = int(parts[3])
                    return (parts[2].strip(), port, parts[0].strip(), parts[1].strip())
                except ValueError:
                    try:
                        # Thử format ip:port:user:pass
                        port = int(parts[1])
                        return (parts[0].strip(), port, parts[2].strip(), parts[3].strip())
                    except ValueError:
                        pass
            elif len(parts) == 2:
                # Format: ip:port (không có auth)
                try:
                    port = int(parts[1])
                    return (parts[0].strip(), port, "", "")
                except ValueError:
                    pass

            return None

        except Exception as e:
            self.log_message(f"❌ Lỗi parse proxy string '{proxy_str}': {e}")
            return None

    def smart_assign_proxies_preserving_fixed(self):
        """
        Gán proxy thông minh nhưng bảo vệ proxy cố định đã được gán theo email
        Chỉ thay đổi proxy random, không động vào proxy cố định
        """
        try:
            if not self.proxy_list:
                self.log_message("⚠️ Không có proxy trong danh sách để gán")
                return

            # Lấy danh sách thiết bị mobile
            mobile_devices = self.get_mobile_devices()
            if not mobile_devices:
                self.log_message("⚠️ Không có thiết bị mobile nào để gán proxy")
                return

            self.log_message(f"🔄 Bắt đầu gán proxy thông minh cho {len(mobile_devices)} thiết bị (bảo vệ proxy cố định)")

            # Load email-proxy mapping từ mail.txt
            self.load_email_proxy_mapping()

            # Phân loại thiết bị: cố định vs random
            fixed_devices = []  # Thiết bị có email với proxy cố định
            random_devices = []  # Thiết bị cần proxy random

            for device in mobile_devices:
                device_id = device.get('id', '')
                device_name = device.get('name', f'Device {len(fixed_devices) + len(random_devices) + 1}')

                # Kiểm tra xem thiết bị này có email với proxy cố định không
                email_for_device = self.device_email_mapping.get(device_id)

                if email_for_device and email_for_device in self.email_proxy_mapping:
                    # Thiết bị có proxy cố định - KHÔNG thay đổi
                    fixed_proxy = self.email_proxy_mapping[email_for_device]
                    fixed_devices.append((device, fixed_proxy, email_for_device))
                    self.log_message(f"🔒 Giữ nguyên proxy cố định cho {device_name} ({email_for_device})")
                else:
                    # Thiết bị cần proxy random - CÓ THỂ thay đổi
                    random_devices.append(device)

            # Lấy danh sách proxy đã được sử dụng bởi thiết bị cố định
            used_fixed_proxies = []
            for device, proxy_info, email in fixed_devices:
                used_fixed_proxies.append(proxy_info)

            # Lọc proxy khả dụng cho random (loại trừ proxy cố định)
            used_proxy_addresses = {(proxy[0], proxy[1]) for proxy in used_fixed_proxies}
            available_random_proxies = [p for p in self.proxy_list if (p[0], p[1]) not in used_proxy_addresses]

            if len(random_devices) > len(available_random_proxies):
                self.log_message(f"⚠️ Không đủ proxy random: cần {len(random_devices)}, có {len(available_random_proxies)}")
                # Cắt bớt thiết bị random nếu không đủ proxy
                random_devices = random_devices[:len(available_random_proxies)]

            # Shuffle proxy random để tránh gán lại proxy cũ
            import random
            random.shuffle(available_random_proxies)

            # Tạo plan gán proxy mới
            device_proxy_plan = []

            # Thêm thiết bị cố định (giữ nguyên)
            for device, proxy_info, email in fixed_devices:
                device_proxy_plan.append((device, 'fixed', proxy_info, email))

            # Thêm thiết bị random (gán proxy mới)
            for i, device in enumerate(random_devices):
                if i < len(available_random_proxies):
                    proxy_info = available_random_proxies[i]
                    device_proxy_plan.append((device, 'random', proxy_info, None))

            # Thực hiện gán proxy theo plan (sử dụng logic từ smart_assign_proxies_after_restore)
            self.execute_smart_proxy_assignment(device_proxy_plan)

            # Thống kê
            fixed_count = len(fixed_devices)
            random_count = len(random_devices)
            self.log_message(f"✅ Hoàn thành gán proxy: {fixed_count} cố định (giữ nguyên) + {random_count} random (thay đổi)")

        except Exception as e:
            self.log_message(f"❌ Lỗi trong smart_assign_proxies_preserving_fixed: {str(e)}")

    def execute_smart_proxy_assignment(self, device_proxy_plan):
        """Thực hiện gán proxy theo plan đã được tạo (logic từ smart_assign_proxies_after_restore)"""
        try:
            if not device_proxy_plan:
                self.log_message("⚠️ Không có plan gán proxy để thực hiện")
                return

            # Dừng tất cả proxy servers hiện tại
            self.stop_all_proxy_servers()

            # Lấy IP LAN
            if not self.lan_ip:
                self.lan_ip = self.get_lan_ip()

            # Gán proxy theo đúng thứ tự thiết bị
            local_port = self.proxy_config['start_port']
            assigned_proxies = []  # Để cập nhật current_proxies
            assigned_ports = []   # Để cập nhật local_ports

            for device, proxy_type, proxy_info, _ in device_proxy_plan:  # _ = email (không sử dụng)
                if proxy_type == 'none' or proxy_info is None:
                    continue

                device_id = device.get('id', '')
                device_name = device.get('name', f'Device {len(assigned_proxies) + 1}')
                ip, port, user, pwd = proxy_info

                # Tạo proxy string cho thiết bị
                proxy_str = f"socks5://{self.lan_ip}:{local_port}"
                self.device_proxy_mapping[device_id] = proxy_str

                # Khởi động proxy server
                try:
                    server_thread = threading.Thread(
                        target=self.start_proxy_server,
                        args=(local_port, ip, port, user, pwd),
                        daemon=True,
                        name=f"ProxyServer-{local_port}"
                    )
                    server_thread.start()
                    self.proxy_servers.append(server_thread)
                except Exception as e:
                    self.log_message(f"❌ Lỗi khởi động proxy server {local_port}: {e}")

                # Lưu proxy info và port để cập nhật current_proxies và local_ports
                assigned_proxies.append(proxy_info)
                assigned_ports.append(local_port)

                auth_info = f" (Auth: {user})" if user else ""
                proxy_label = "Cố định" if proxy_type == 'fixed' else "Random"
                icon = "🔒" if proxy_type == 'fixed' else "🔄"
                self.log_message(f"{icon} {device_name}: {ip}:{port}{auth_info} → :{local_port} ({proxy_label})")

                local_port += 1

            # Cập nhật current_proxies và local_ports để hiển thị đúng thông tin
            self.current_proxies = assigned_proxies
            self.local_ports = assigned_ports

            # Cập nhật hiển thị
            self.update_device_list()
            self.update_ports_label()

        except Exception as e:
            self.log_message(f"❌ Lỗi trong execute_smart_proxy_assignment: {str(e)}")

    def update_auto_assign_button(self):
        """Cập nhật text của button auto-assign"""
        if hasattr(self.ui, 'pushButton_7'):
            status = "ON" if self.auto_assign_enabled else "OFF"
            self.ui.pushButton_7.setText(f"Auto-Assign: {status}")

    def toggle_auto_assign(self):
        """Bật/tắt auto-assign proxy"""
        self.auto_assign_enabled = not self.auto_assign_enabled
        self.update_auto_assign_button()

        status = "bật" if self.auto_assign_enabled else "tắt"
        self.log_message(f"🎯 Đã {status} auto-assign proxy")

        # Nếu vừa bật auto-assign, thực hiện gán proxy thông minh (bảo vệ proxy cố định)
        if self.auto_assign_enabled:
            self.log_message("🔄 Đang gán proxy thông minh (bảo vệ proxy cố định từ email)...")
            self.smart_assign_proxies_preserving_fixed()

    def auto_rotate_proxy_after_restore(self):
        """Tự động đổi proxy sau khi restore (được gọi bởi QTimer)"""
        try:
            self.log_message("⏰ 20 giây đã trôi qua - Đang tự động đổi proxy...")
            # Sử dụng smart assign thay vì rotate random
            self.smart_assign_proxies_after_restore()
            self.log_message("✅ Đã tự động đổi proxy thành công sau restore")
        except Exception as e:
            self.log_message(f"⚠️ Lỗi khi tự động đổi proxy sau restore: {e}")

    def smart_assign_proxies_after_restore(self):
        """Gán proxy thông minh sau restore: email có proxy cố định thì gán cố định, còn lại random"""
        try:
            # Lấy danh sách thiết bị hiện tại
            mobile_devices = self.get_mobile_devices()
            if not mobile_devices:
                self.log_message("⚠️ Không có thiết bị nào để gán proxy")
                return

            # Dừng proxy servers cũ
            self.stop_all_proxies()

            # Tạo mapping thiết bị theo thứ tự và phân loại proxy
            device_proxy_plan = []  # [(device_info, proxy_type, proxy_info, email)]

            # Chuẩn bị proxy random trước
            available_proxies = []
            random_proxy_iter = None

            if self.proxy_list:
                # Lấy danh sách proxy từ email đã dùng
                used_email_proxies = []
                for device in mobile_devices:
                    device_id = device['id']
                    email_for_device = self.device_email_mapping.get(device_id)
                    if email_for_device and email_for_device in self.email_proxy_mapping:
                        used_email_proxies.append(self.email_proxy_mapping[email_for_device])

                # Loại bỏ proxy trùng lặp (so sánh theo IP:Port)
                used_proxy_addresses = {(proxy[0], proxy[1]) for proxy in used_email_proxies}
                available_proxies = [
                    p for p in self.proxy_list
                    if (p[0], p[1]) not in used_proxy_addresses
                ]

                if available_proxies:
                    # Shuffle để random
                    random.shuffle(available_proxies)
                    random_proxy_iter = iter(available_proxies)

                self.log_message(f"📊 Proxy từ email đã dùng: {len(used_email_proxies)}, Proxy khả dụng cho random: {len(available_proxies)}")

            # Phân loại thiết bị theo thứ tự và gán proxy
            for device in mobile_devices:
                device_id = device['id']
                device_name = device['name']

                # Kiểm tra xem thiết bị này có email với proxy cố định không
                email_for_device = self.device_email_mapping.get(device_id)

                if email_for_device and email_for_device in self.email_proxy_mapping:
                    # Thiết bị có email với proxy cố định
                    fixed_proxy = self.email_proxy_mapping[email_for_device]
                    device_proxy_plan.append((device, 'fixed', fixed_proxy, email_for_device))
                    self.log_message(f"🎯 {device_name} ({email_for_device}) → Proxy cố định: {fixed_proxy[0]}:{fixed_proxy[1]}")
                else:
                    # Thiết bị cần proxy random
                    if random_proxy_iter:
                        try:
                            random_proxy = next(random_proxy_iter)
                            device_proxy_plan.append((device, 'random', random_proxy, None))
                            self.log_message(f"🎲 {device_name} → Proxy random: {random_proxy[0]}:{random_proxy[1]}")
                        except StopIteration:
                            self.log_message(f"⚠️ Hết proxy random cho {device_name}")
                            device_proxy_plan.append((device, 'none', None, None))
                    else:
                        self.log_message(f"⚠️ Không có proxy random cho {device_name}")
                        device_proxy_plan.append((device, 'none', None, None))

            # Gán proxy theo đúng thứ tự thiết bị
            local_port = self.proxy_config['start_port']
            assigned_proxies = []  # Để cập nhật current_proxies
            assigned_ports = []   # Để cập nhật local_ports

            for device, proxy_type, proxy_info, email in device_proxy_plan:
                if proxy_type == 'none' or proxy_info is None:
                    continue

                device_id = device['id']
                device_name = device['name']
                ip, port, user, pwd = proxy_info

                try:
                    # Khởi động proxy server
                    server_thread = threading.Thread(
                        target=self.start_proxy_server,
                        args=(local_port, ip, port, user, pwd),
                        daemon=True,
                        name=f"ProxyServer-{local_port}"
                    )
                    server_thread.start()
                    self.proxy_threads.append(server_thread)

                    # Tạo proxy string cho device mapping với IP LAN thực
                    lan_ip = self.get_lan_ip()
                    proxy_str = f"socks5://{lan_ip}:{local_port}"
                    self.device_proxy_mapping[device_id] = proxy_str

                    # Lưu proxy info và port để cập nhật current_proxies và local_ports
                    assigned_proxies.append(proxy_info)
                    assigned_ports.append(local_port)

                    auth_info = f" (Auth: {user})" if user else ""
                    proxy_label = "Cố định" if proxy_type == 'fixed' else "Random"
                    icon = "🎯" if proxy_type == 'fixed' else "🎲"
                    self.log_message(f"{icon} {device_name}: {ip}:{port}{auth_info} → :{local_port} ({proxy_label})")

                    local_port += 1

                except Exception as e:
                    self.log_message(f"❌ Lỗi khởi động proxy cho {device_name}: {e}")

            # Cập nhật current_proxies và local_ports để hiển thị đúng thông tin
            self.current_proxies = assigned_proxies
            self.local_ports = assigned_ports

            # Đếm số proxy đã gán
            fixed_count = sum(1 for _, proxy_type, _, _ in device_proxy_plan if proxy_type == 'fixed')
            random_count = sum(1 for _, proxy_type, _, _ in device_proxy_plan if proxy_type == 'random')

            self.log_message(f"✅ Đã gán proxy thông minh: {fixed_count} cố định + {random_count} random")

            # Cập nhật hiển thị
            self.update_proxy_display()

        except Exception as e:
            self.log_message(f"❌ Lỗi trong smart assign proxy: {e}")
            logger.error(f"Error in smart_assign_proxies_after_restore: {e}")

    def config_proxy(self):
        """Mở dialog để chỉnh sửa file proxy"""
        # Kiểm tra license cho tính năng PROXY
        app = QApplication.instance()
        allowed_features = app.property("allowed_features")

        # Kiểm tra quyền sử dụng proxy
        proxy_allowed = is_feature_allowed("PROXY", allowed_features)
        if allowed_features and allowed_features.upper() == "ALL_BLOCKED":
            proxy_allowed = False

        if not proxy_allowed:
            QMessageBox.warning(self, "Tính năng bị giới hạn",
                               "Tính năng Proxy không có trong gói license của bạn.")
            self.log_message("❌ Tính năng Proxy không được phép sử dụng với license hiện tại")
            return

        try:
            proxy_file = self.proxy_config['proxy_file']
            dialog = ProxyEditDialog(proxy_file, self)
            if dialog.exec_() == dialog.Accepted:
                # Tải lại proxy sau khi chỉnh sửa
                self.load_proxies()
                # Tải lại email-proxy mapping để cập nhật thay đổi
                self.load_email_proxy_mapping()
                self.log_message("✅ Đã cập nhật file proxy và email-proxy mapping")
        except Exception as e:
            QMessageBox.critical(self, "Lỗi", f"Không thể mở editor proxy:\n{e}")
            logger.error(f"Error opening proxy config: {e}")

    def check_proxy_health(self):
        """Kiểm tra sức khỏe của proxy đang được gán cho thiết bị"""
        # Kiểm tra license cho tính năng PROXY
        app = QApplication.instance()
        allowed_features = app.property("allowed_features")

        # Kiểm tra quyền sử dụng proxy
        proxy_allowed = is_feature_allowed("PROXY", allowed_features)
        if allowed_features and allowed_features.upper() == "ALL_BLOCKED":
            proxy_allowed = False

        if not proxy_allowed:
            QMessageBox.warning(self, "Tính năng bị giới hạn",
                               "Tính năng Proxy không có trong gói license của bạn.")
            self.log_message("❌ Tính năng Proxy không được phép sử dụng với license hiện tại")
            return

        # Kiểm tra xem có thiết bị nào được gán proxy không
        if not self.current_proxies or not self.device_proxy_mapping:
            QMessageBox.warning(self, "Cảnh báo", "Không có thiết bị nào được gán proxy để kiểm tra!")
            return

        # Kiểm tra xem có đang chạy health check không
        if hasattr(self, 'health_check_running') and self.health_check_running:
            # Log thông báo đang kiểm tra (không hiển thị message box)
            self.log_message("⏳ Đang kiểm tra proxy, vui lòng đợi...")
            return

        self.log_message("🏥 Bắt đầu kiểm tra sức khỏe proxy đang được gán...")

        # Disable button để tránh spam
        if hasattr(self.ui, 'pushButton_4'):
            self.ui.pushButton_4.setEnabled(False)

        # Đánh dấu đang chạy health check
        self.health_check_running = True

        # Bật chế độ hiển thị health check results
        self.showing_health_results = True

        # Khởi tạo health check results
        self.health_check_results = {}

        # Hiển thị trạng thái đang kiểm tra
        self.display_health_check_results()

        # Khởi tạo danh sách health checkers nếu chưa có
        if not hasattr(self, 'health_checkers'):
            self.health_checkers = []

        # Dừng và xóa các health checkers cũ
        self.cleanup_health_checkers()

        # Chỉ kiểm tra proxy đang được gán cho thiết bị
        proxy_count = 0

        for i, proxy_info in enumerate(self.current_proxies):
            if proxy_info:  # Proxy info không None
                try:
                    checker = ProxyHealthChecker(proxy_info, self.proxy_config['health_check_url'], 5)  # Timeout ngắn
                    checker.health_checked.connect(self.on_health_checked)
                    checker.finished.connect(lambda: self.on_health_checker_finished())

                    # Lưu reference để tránh garbage collection
                    self.health_checkers.append(checker)
                    checker.start()
                    proxy_count += 1
                    # logger.info(f"Started health check for assigned proxy {i+1}: {proxy_info[0]}:{proxy_info[1]}")
                    pass
                except Exception as e:
                    logger.error(f"Error starting health check for assigned proxy {i+1}: {e}")

        if proxy_count == 0:
            self.log_message("⚠️ Không có proxy được gán hợp lệ để kiểm tra")
            self.health_check_running = False
            self.showing_health_results = False
            if hasattr(self.ui, 'pushButton_4'):
                self.ui.pushButton_4.setEnabled(True)
        else:
            self.log_message(f"🔍 Đang kiểm tra {proxy_count} proxy được gán cho thiết bị...")
            # Re-enable button sau 15 giây
            QTimer.singleShot(15000, self.enable_health_check_button)

    def cleanup_health_checkers(self):
        """Dọn dẹp các health checkers cũ"""
        try:
            if hasattr(self, 'health_checkers'):
                for checker in self.health_checkers:
                    if checker.isRunning():
                        checker.terminate()
                        checker.wait(1000)  # Đợi tối đa 1 giây
                self.health_checkers.clear()
        except Exception as e:
            logger.error(f"Error cleaning up health checkers: {e}")

    def on_health_checker_finished(self):
        """Callback khi một health checker hoàn thành"""
        try:
            # Kiểm tra xem tất cả health checkers đã hoàn thành chưa
            if hasattr(self, 'health_checkers'):
                running_count = sum(1 for checker in self.health_checkers if checker.isRunning())
                if running_count == 0:
                    # Tất cả đã hoàn thành
                    self.health_check_running = False
                    self.log_message("✅ Đã hoàn thành kiểm tra sức khỏe proxy")

                    # Cập nhật hiển thị cuối cùng với thống kê
                    if hasattr(self, 'showing_health_results') and self.showing_health_results:
                        self.display_health_check_results()

                    # Tự động tắt chế độ hiển thị health check sau 10 giây
                    QTimer.singleShot(10000, self.restore_normal_display)

                    if hasattr(self.ui, 'pushButton_4'):
                        self.ui.pushButton_4.setEnabled(True)
        except Exception as e:
            logger.error(f"Error in health checker finished callback: {e}")

    def restore_normal_display(self):
        """Khôi phục hiển thị bình thường (device-proxy mapping)"""
        try:
            self.showing_health_results = False
            self.update_proxy_display()
            self.log_message("🔄 Đã chuyển về hiển thị proxy bình thường")
        except Exception as e:
            logger.error(f"Error restoring normal display: {e}")

    def enable_health_check_button(self):
        """Kích hoạt lại button Check Proxy"""
        try:
            self.health_check_running = False
            if hasattr(self.ui, 'pushButton_4'):
                self.ui.pushButton_4.setEnabled(True)
            self.log_message("⏰ Đã kích hoạt lại button Check Proxy")
        except Exception as e:
            logger.error(f"Error enabling health check button: {e}")

    def on_health_checked(self, proxy_str, is_healthy):
        """Callback khi kiểm tra sức khỏe proxy hoàn thành"""
        try:
            status = "✅ Khỏe mạnh" if is_healthy else "❌ Không hoạt động"
            self.log_message(f"🏥 {proxy_str} - {status}")

            # Cập nhật hiển thị health check trong listView
            self.update_health_check_display(proxy_str, is_healthy)

        except Exception as e:
            logger.error(f"Error in health check callback: {e}")

    def update_health_check_display(self, proxy_str, is_healthy):
        """Cập nhật hiển thị health check trong listView"""
        try:
            # Khởi tạo health check results nếu chưa có
            if not hasattr(self, 'health_check_results'):
                self.health_check_results = {}

            # Lưu kết quả health check
            self.health_check_results[proxy_str] = is_healthy

            # Cập nhật hiển thị nếu đang ở chế độ health check
            if hasattr(self, 'showing_health_results') and self.showing_health_results:
                self.display_health_check_results()

        except Exception as e:
            logger.error(f"Error updating health check display: {e}")

    def display_health_check_results(self):
        """Hiển thị kết quả health check trong listView"""
        try:
            if not hasattr(self.ui, 'listView'):
                return

            # Tạo danh sách hiển thị health check
            display_items = []

            # Header
            display_items.append("🏥 Kết quả kiểm tra sức khỏe proxy:")
            display_items.append("")

            # Hiển thị kết quả health check
            if hasattr(self, 'health_check_results') and self.health_check_results:
                healthy_count = 0
                total_count = len(self.health_check_results)

                # Tạo mapping proxy -> device để hiển thị tên thiết bị
                proxy_device_mapping = {}
                if hasattr(self, 'current_proxies') and hasattr(self, 'device_proxy_mapping'):
                    for i, proxy_info in enumerate(self.current_proxies):
                        if proxy_info and len(proxy_info) >= 2:
                            proxy_str = f"{proxy_info[0]}:{proxy_info[1]}"
                            # Tìm device_id tương ứng
                            for device_id, _ in self.device_proxy_mapping.items():
                                if i < len(list(self.device_proxy_mapping.keys())):
                                    device_keys = list(self.device_proxy_mapping.keys())
                                    if i < len(device_keys):
                                        device_name = self.get_device_name_by_id(device_keys[i])
                                        proxy_device_mapping[proxy_str] = device_name
                                        break

                for proxy_str, is_healthy in self.health_check_results.items():
                    status_icon = "✅" if is_healthy else "❌"
                    status_text = "Good" if is_healthy else "Không hoạt động"

                    # Tìm thông tin auth từ proxy_list
                    auth_info = self.get_proxy_auth_info(proxy_str)
                    auth_text = f" (Auth: {auth_info})" if auth_info else ""

                    # Tìm tên thiết bị
                    device_name = proxy_device_mapping.get(proxy_str, "Unknown Device")

                    display_text = f"{status_icon} {device_name}: {proxy_str}{auth_text} - {status_text}"
                    display_items.append(display_text)

                    if is_healthy:
                        healthy_count += 1

                # Thêm thống kê
                display_items.append("")
                display_items.append(f"📊 Tổng kết: {healthy_count}/{total_count} proxy khỏe mạnh")

            else:
                display_items.append("⏳ Đang kiểm tra proxy được gán cho thiết bị...")

            # Cập nhật model cho listView
            model = QStringListModel()
            model.setStringList(display_items)
            self.ui.listView.setModel(model)

        except Exception as e:
            logger.error(f"Error displaying health check results: {e}")

    def get_proxy_auth_info(self, proxy_str):
        """Lấy thông tin auth từ proxy string"""
        try:
            # proxy_str format: "ip:port"
            ip, port = proxy_str.split(':')

            # Tìm trong proxy_list
            for proxy_info in self.proxy_list:
                if proxy_info and len(proxy_info) >= 4:
                    if proxy_info[0] == ip and str(proxy_info[1]) == port:
                        return proxy_info[2]  # username

            return None
        except Exception as e:
            # logger.debug(f"Error getting proxy auth info: {e}")
            return None

    def rotate_proxies(self):
        """Đổi proxy cho các thiết bị và khởi động proxy servers mới"""
        # Kiểm tra license cho tính năng PROXY
        app = QApplication.instance()
        allowed_features = app.property("allowed_features")

        # Kiểm tra quyền sử dụng proxy
        proxy_allowed = is_feature_allowed("PROXY", allowed_features)
        if allowed_features and allowed_features.upper() == "ALL_BLOCKED":
            proxy_allowed = False

        if not proxy_allowed:
            QMessageBox.warning(self, "Tính năng bị giới hạn",
                               "Tính năng Proxy không có trong gói license của bạn.")
            self.log_message("❌ Tính năng Proxy không được phép sử dụng với license hiện tại")
            return

        if not self.proxy_list:
            QMessageBox.warning(self, "Cảnh báo", "Không có proxy nào để đổi!")
            return

        # Lấy danh sách thiết bị hiện tại
        mobile_devices = self.get_mobile_devices()
        if not mobile_devices:
            QMessageBox.warning(self, "Cảnh báo", "Không có thiết bị nào để gán proxy!")
            return

        try:
            # Dừng proxy servers cũ
            self.stop_all_proxies()

            # Chọn proxy ngẫu nhiên
            num_devices = len(mobile_devices)
            num_proxies = len(self.proxy_list)
            max_assign = min(num_devices, num_proxies)

            selected_proxies = random.sample(self.proxy_list, max_assign)

            # Tạo local ports mới
            self.local_ports = [self.proxy_config['start_port'] + i for i in range(max_assign)]

            # Gán proxy mới và khởi động servers
            self.device_proxy_mapping.clear()
            for i in range(max_assign):
                device = mobile_devices[i]
                proxy_info = selected_proxies[i]
                device_id = device.get('id', '')
                device_name = device.get('name', f'Device {i+1}')
                local_port = self.local_ports[i]

                ip, port, user, pwd = proxy_info

                # Khởi động proxy server
                try:
                    server_thread = threading.Thread(
                        target=self.start_proxy_server,
                        args=(local_port, ip, port, user, pwd),
                        daemon=True,
                        name=f"ProxyServer-{local_port}"
                    )
                    server_thread.start()
                    self.proxy_threads.append(server_thread)

                    # Tạo proxy string cho device mapping với IP LAN thực
                    lan_ip = self.get_lan_ip()
                    proxy_str = f"socks5://{lan_ip}:{local_port}"
                    self.device_proxy_mapping[device_id] = proxy_str

                    auth_info = f" (Auth: {user})" if user else ""
                    self.log_message(f"🔄 {device_name}: {ip}:{port}{auth_info} → :{local_port}")

                except Exception as e:
                    logger.error(f"❌ Failed to start proxy for {device_name}: {e}")
                    self.log_message(f"❌ Lỗi khởi động proxy cho {device_name}: {e}")

            self.current_proxies = selected_proxies
            self.log_message(f"✅ Đã đổi và khởi động {max_assign} proxy servers")

            # Cập nhật hiển thị
            self.update_proxy_display()

        except Exception as e:
            logger.error(f"Error rotating proxies: {e}")
            self.log_message(f"❌ Lỗi đổi proxy: {e}")

    def stop_all_proxies(self):
        """Dừng tất cả proxy servers"""
        try:
            # Dừng tất cả proxy servers
            for server in self.active_servers:
                try:
                    server.close()
                except:
                    pass

            # Clear tất cả
            self.active_servers.clear()
            self.proxy_threads.clear()
            self.device_proxy_mapping.clear()
            self.current_proxies.clear()
            self.proxy_servers.clear()
            self.local_ports.clear()

            self.log_message("⏹️ Đã dừng tất cả proxy servers")

            # Cập nhật hiển thị
            self.update_proxy_display()

        except Exception as e:
            logger.error(f"Error stopping proxies: {e}")
            self.log_message(f"❌ Lỗi dừng proxy: {e}")

    def open_chrome_with_proxy(self):
        """Mở Chrome với proxy configuration"""
        try:
            # Kiểm tra current_proxies có hợp lệ không
            if not self.current_proxies or all(p is None for p in self.current_proxies):
                # Nếu chưa có proxy đang chạy, sử dụng proxy_list
                if not self.proxy_list:
                    QMessageBox.warning(self, "Cảnh báo", "Không có proxy nào để sử dụng!")
                    return
                active_proxies = self.proxy_list[:5]  # Lấy 5 proxy đầu tiên
            else:
                # Lọc ra các proxy không None
                active_proxies = [p for p in self.current_proxies if p is not None]

            if not active_proxies:
                QMessageBox.warning(self, "Cảnh báo", "Không có proxy nào đang hoạt động!")
                return

            # Tạo dialog với active proxies
            dialog = ChromeProxyDialog(active_proxies, self.proxy_config, self)
            if dialog.exec_() == dialog.Accepted:
                proxy_info = dialog.get_selected_proxy()
                website = dialog.get_website()

                if proxy_info and website:
                    self.launch_chrome_with_proxy(proxy_info, website)

        except Exception as e:
            logger.error(f"Error opening Chrome with proxy: {e}")
            QMessageBox.critical(self, "Lỗi", f"Không thể mở Chrome với proxy:\n{e}")

    def launch_chrome_with_proxy(self, proxy_info, website):
        """Khởi động Chrome với proxy settings"""
        try:
            proxy_index, (ip, port, user, pwd) = proxy_info

            # Sử dụng local proxy server nếu có
            if proxy_index < len(self.local_ports):
                local_port = self.local_ports[proxy_index]
                lan_ip = self.get_lan_ip()
                proxy_server = f"socks5://{lan_ip}:{local_port}"
                profile_suffix = f"local_{local_port}"
            else:
                # Fallback to direct proxy
                if user and pwd:
                    proxy_server = f"socks5://{user}:{pwd}@{ip}:{port}"
                else:
                    proxy_server = f"socks5://{ip}:{port}"
                profile_suffix = f"direct_{ip}_{port}"

            # Tạo Chrome profile riêng cho proxy
            profile_dir = os.path.join(tempfile.gettempdir(), f"chrome_proxy_{profile_suffix}")

            # Chrome command line arguments
            chrome_args = [
                "--proxy-server=" + proxy_server,
                "--user-data-dir=" + profile_dir,
                "--no-first-run",
                "--disable-default-apps",
                "--disable-popup-blocking",
                "--disable-translate",
                "--new-window",
                website
            ]

            # Tìm Chrome executable
            chrome_paths = [
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                "chrome"  # Fallback to PATH
            ]

            chrome_exe = None
            for path in chrome_paths:
                if os.path.exists(path):
                    chrome_exe = path
                    break

            if not chrome_exe:
                chrome_exe = "chrome"  # Try PATH

            # Khởi động Chrome
            subprocess.Popen([chrome_exe] + chrome_args,
                           creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

            self.log_message(f"🚀 Đã mở Chrome với proxy {proxy_server} - {website}")

        except Exception as e:
            logger.error(f"Error launching Chrome: {e}")
            self.log_message(f"❌ Lỗi mở Chrome: {e}")

    def get_mobile_devices(self):
        """Lấy danh sách thiết bị mobile từ bảng"""
        devices = []
        try:
            for row in range(self.ui.tableWidget.rowCount()):
                serial_item = self.ui.tableWidget.item(row, 1)
                if serial_item:
                    serial = serial_item.text().strip()
                    if serial:  # Chỉ lấy thiết bị có serial
                        devices.append({
                            'id': serial,
                            'name': f'Device {row+1}',
                            'row': row
                        })
        except Exception as e:
            logger.error(f"Error getting mobile devices: {e}")

        return devices

    def check_and_auto_assign_proxies_on_startup(self):
        """Kiểm tra và tự động gán proxy khi khởi động"""
        try:
            if not self.auto_assign_enabled:
                # logger.info("🔒 Auto-assign disabled, skipping")
                return

            if not self.proxy_list:
                # logger.info("⚠️ No proxy list available for auto-assign")
                return

            # Lấy danh sách thiết bị
            mobile_devices = self.get_mobile_devices()
            current_mobile_count = len(mobile_devices)

            if current_mobile_count == 0:
                # logger.info("📱 No mobile devices detected, skipping auto-assign")
                return

            # Thực hiện auto-assign
            self.auto_assign_proxies_to_devices(mobile_devices)

        except Exception as e:
            logger.error(f"Error in auto-assign on startup: {e}")

    def auto_assign_proxies_to_devices(self, mobile_devices):
        """Tự động gán proxy cho các thiết bị và khởi động proxy servers"""
        try:
            num_devices = len(mobile_devices)
            num_proxies = len(self.proxy_list)

            if num_proxies == 0:
                self.log_message("⚠️ Không có proxy để gán")
                return

            # Dừng proxy servers cũ trước
            self.stop_all_proxies()

            # Tính số proxy cần gán
            max_assign = min(num_devices, num_proxies)

            # Chọn proxy ngẫu nhiên
            selected_proxies = random.sample(self.proxy_list, max_assign)

            # Tạo local ports
            self.local_ports = [self.proxy_config['start_port'] + i for i in range(max_assign)]

            # Gán proxy và khởi động servers
            self.device_proxy_mapping.clear()
            for i in range(max_assign):
                device = mobile_devices[i]
                proxy_info = selected_proxies[i]
                device_id = device.get('id', '')
                device_name = device.get('name', f'Device {i+1}')
                local_port = self.local_ports[i]

                ip, port, user, pwd = proxy_info

                # Khởi động proxy server
                try:
                    server_thread = threading.Thread(
                        target=self.start_proxy_server,
                        args=(local_port, ip, port, user, pwd),
                        daemon=True,
                        name=f"ProxyServer-{local_port}"
                    )
                    server_thread.start()
                    self.proxy_threads.append(server_thread)

                    # Tạo proxy string cho device mapping với IP LAN thực
                    lan_ip = self.get_lan_ip()
                    proxy_str = f"socks5://{lan_ip}:{local_port}"
                    self.device_proxy_mapping[device_id] = proxy_str

                    auth_info = f" (Auth: {user})" if user else ""
                    self.log_message(f"🤖 {device_name}: {ip}:{port}{auth_info} → :{local_port}")

                except Exception as e:
                    logger.error(f"❌ Failed to start proxy for {device_name}: {e}")
                    self.log_message(f"❌ Lỗi khởi động proxy cho {device_name}: {e}")

            # Cập nhật current_proxies
            self.current_proxies = selected_proxies
            self.log_message(f"🎯 Đã khởi động {max_assign} proxy servers")

            # Cập nhật hiển thị
            self.update_proxy_display()

        except Exception as e:
            logger.error(f"Error in auto-assign proxies: {e}")
            self.log_message(f"❌ Lỗi auto-assign proxy: {e}")

    def update_proxy_display(self):
        """Cập nhật hiển thị danh sách thiết bị được gán proxy"""
        try:
            # Không cập nhật nếu đang hiển thị health check results
            if hasattr(self, 'showing_health_results') and self.showing_health_results:
                return

            # Cập nhật IP Server label
            self.update_ip_server_label()

            # Cập nhật Ports label
            self.update_ports_label()

            # Cập nhật listView với danh sách thiết bị
            self.update_device_list()

        except Exception as e:
            logger.error(f"Error updating proxy display: {e}")

    def update_ip_server_label(self):
        """Cập nhật label IP Server"""
        try:
            if hasattr(self.ui, 'label_8'):
                try:
                    import socket
                    hostname = socket.gethostname()
                    local_ip = socket.gethostbyname(hostname)
                    self.ui.label_8.setText(f"🌐 IP LAN: {local_ip}")
                except:
                    self.ui.label_8.setText("🌐 IP LAN: Unknown")
        except Exception as e:
            logger.error(f"Error updating IP server label: {e}")

    def update_ports_label(self):
        """Cập nhật label Ports"""
        try:
            if hasattr(self.ui, 'label_9'):
                if self.local_ports:
                    ports_str = f"{self.local_ports[0]}-{self.local_ports[-1]}"
                    self.ui.label_9.setText(f"🔗 Ports: {ports_str} | Type: SOCKS5")
                else:
                    self.ui.label_9.setText("🔗 Ports: None | Type: SOCKS5")
        except Exception as e:
            logger.error(f"Error updating ports label: {e}")

    def update_device_list(self):
        """Cập nhật danh sách thiết bị trong listView"""
        try:
            if not hasattr(self.ui, 'listView'):
                return

            # Tạo danh sách hiển thị thiết bị
            display_items = []

            # Header        
            display_items.append("")

            # Thêm danh sách thiết bị và proxy
            if self.device_proxy_mapping:
                for i, (device_id, proxy_str) in enumerate(self.device_proxy_mapping.items()):
                    # Lấy thông tin thiết bị
                    device_name = self.get_device_name_by_id(device_id)

                    # Lấy thông tin proxy từ current_proxies
                    if i < len(self.current_proxies):
                        proxy_info = self.current_proxies[i]
                        ip, port, user, _ = proxy_info  # Bỏ pwd vì không dùng
                        local_port = self.local_ports[i] if i < len(self.local_ports) else "Unknown"

                        # Format hiển thị giống proxydemo
                        auth_info = f" (Auth: {user})" if user else ""
                        display_text = f"📱 {device_name}: {ip}:{port}{auth_info} → :{local_port}"
                        display_items.append(display_text)
                    else:
                        display_items.append(f"📱 {device_name}: {proxy_str}")
            else:
                display_items.append("⚠️ Chưa có thiết bị nào được gán proxy")

            # Cập nhật model cho listView
            model = QStringListModel()
            model.setStringList(display_items)
            self.ui.listView.setModel(model)

        except Exception as e:
            logger.error(f"Error updating device list: {e}")

    def get_device_name_by_id(self, device_id):
        """Lấy tên thiết bị từ device_id"""
        try:
            # Thử lấy tên thiết bị thật từ ADB trước
            real_name = self.get_real_device_name(device_id)
            if real_name:
                return real_name

            # Fallback: Tìm trong bảng thiết bị
            for row in range(self.ui.tableWidget.rowCount()):
                serial_item = self.ui.tableWidget.item(row, 1)
                if serial_item and serial_item.text().strip() == device_id:
                    # Thử lấy tên từ cột đầu tiên
                    name_item = self.ui.tableWidget.item(row, 0)
                    if name_item and name_item.text().strip():
                        return name_item.text().strip()
                    else:
                        return f"Device {row+1}"

            # Nếu không tìm thấy, trả về device_id rút gọn
            return device_id[:8] + "..." if len(device_id) > 8 else device_id

        except Exception as e:
            logger.error(f"Error getting device name: {e}")
            return device_id[:8] + "..." if len(device_id) > 8 else device_id

    def get_real_device_name(self, device_id):
        """Lấy tên thiết bị thật từ ADB (tương tự proxydemo.py)"""
        try:
            if not ADB_PATH:
                return None

            # Lấy model name
            result = subprocess.run([
                ADB_PATH, '-s', device_id, 'shell', 'getprop', 'ro.product.model'
            ], capture_output=True, text=True, timeout=10,
               creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

            if result.returncode == 0:
                model = result.stdout.strip()
                if model and model != "unknown":
                    # Trả về tên model với device_id rút gọn
                    return f"{model} ({device_id[:8]}...)"

            # Fallback: lấy brand + model
            result = subprocess.run([
                ADB_PATH, '-s', device_id, 'shell', 'getprop', 'ro.product.brand'
            ], capture_output=True, text=True, timeout=10,
               creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

            if result.returncode == 0:
                brand = result.stdout.strip()
                if brand and brand != "unknown":
                    return f"{brand} Device ({device_id[:8]}...)"

            # Fallback cuối: lấy manufacturer
            result = subprocess.run([
                ADB_PATH, '-s', device_id, 'shell', 'getprop', 'ro.product.manufacturer'
            ], capture_output=True, text=True, timeout=10,
               creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)

            if result.returncode == 0:
                manufacturer = result.stdout.strip()
                if manufacturer and manufacturer != "unknown":
                    return f"{manufacturer} ({device_id[:8]}...)"

        except subprocess.TimeoutExpired:
            logger.warning(f"Timeout getting device name for {device_id}")
        except Exception as e:
            logger.error(f"Error getting real device name for {device_id}: {e}")

        return None

    def start_proxy_server(self, local_port, remote_ip, remote_port, user, pwd):
        """Khởi động proxy server trên local port"""
        if socks is None:
            self.log_message("❌ Cần cài đặt PySocks: pip install PySocks")
            return

        try:
            # Tạo socket
            server = socket.socket(socket.AF_INET, socket.SOCK_STREAM)

            # Cải thiện socket options
            server.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            server.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)

            # Timeout settings
            server.settimeout(30)

            # Bind
            bind_address = self.proxy_config['local_bind']
            try:
                server.bind((bind_address, local_port))
                # logger.info(f"Successfully bound to {bind_address}:{local_port}")
            except OSError as e:
                if "Address already in use" in str(e):
                    logger.warning(f"Port {local_port} already in use")
                    return
                else:
                    raise

            # Listen
            server.listen(5)

            # Lưu server để có thể đóng sau này
            self.active_servers.append(server)

            # logger.info(f"🚀 Proxy server started on {bind_address}:{local_port} -> {remote_ip}:{remote_port}")

            while True:
                try:
                    # Accept với timeout
                    server.settimeout(1.0)
                    client_sock, client_addr = server.accept()

                    # logger.info(f"📱 New connection from {client_addr[0]}:{client_addr[1]} -> {remote_ip}:{remote_port}")

                    # Xử lý client trong thread riêng
                    client_thread = threading.Thread(
                        target=self.handle_client,
                        args=(client_sock, remote_ip, remote_port, user, pwd),
                        daemon=True,
                        name=f"Client-{client_addr[0]}-{local_port}"
                    )
                    client_thread.start()

                except socket.timeout:
                    continue
                except OSError:
                    # Server đã bị đóng
                    break
                except Exception as e:
                    logger.error(f"Error in proxy server loop: {e}")
                    break

        except Exception as e:
            logger.error(f"Error starting proxy server on port {local_port}: {e}")
            self.log_message(f"❌ Lỗi khởi động proxy server port {local_port}: {e}")
        finally:
            try:
                server.close()
            except:
                pass

    def handle_client(self, client_sock, remote_ip, remote_port, user, pwd):
        """Xử lý kết nối từ client - Hỗ trợ cả HTTP và SOCKS5"""
        try:
            # Đọc dữ liệu đầu tiên để xác định protocol
            client_sock.settimeout(10)
            first_data = client_sock.recv(1024, socket.MSG_PEEK)

            if not first_data:
                client_sock.close()
                return

            # Kiểm tra protocol
            if first_data[0] == 0x05:  # SOCKS5
                self.handle_socks5_client(client_sock, remote_ip, remote_port, user, pwd)
            elif first_data.startswith(b'CONNECT') or first_data.startswith(b'GET') or first_data.startswith(b'POST'):
                self.handle_http_client(client_sock, remote_ip, remote_port, user, pwd)
            else:
                # Assume SOCKS5 by default
                self.handle_socks5_client(client_sock, remote_ip, remote_port, user, pwd)

        except Exception as e:
            logger.error(f"Error handling client: {e}")
            try:
                client_sock.close()
            except:
                pass

    def handle_socks5_client(self, client_sock, remote_ip, remote_port, user, pwd):
        """Xử lý SOCKS5 client"""
        try:
            # Đọc SOCKS5 handshake
            data = client_sock.recv(1024)
            if not data or len(data) < 3:
                client_sock.close()
                return

            # Phản hồi SOCKS5 handshake
            if data[0] == 0x05:  # SOCKS5
                # Gửi phản hồi không cần authentication
                client_sock.send(b'\x05\x00')

                # Đọc request
                data = client_sock.recv(1024)
                if not data or len(data) < 10:
                    client_sock.close()
                    return

                # Parse SOCKS5 request
                if data[0] == 0x05 and data[1] == 0x01:  # CONNECT command
                    # Tạo kết nối đến proxy server
                    try:
                        remote = socks.socksocket()
                        if user and pwd:
                            remote.set_proxy(socks.SOCKS5, remote_ip, remote_port, True, user, pwd)
                        else:
                            remote.set_proxy(socks.SOCKS5, remote_ip, remote_port)

                        # Parse target address từ SOCKS5 request
                        if data[3] == 0x01:  # IPv4
                            target_ip = socket.inet_ntoa(data[4:8])
                            target_port = int.from_bytes(data[8:10], 'big')
                        elif data[3] == 0x03:  # Domain name
                            domain_len = data[4]
                            target_ip = data[5:5+domain_len].decode('utf-8')
                            target_port = int.from_bytes(data[5+domain_len:7+domain_len], 'big')
                        else:
                            # Unsupported address type
                            client_sock.send(b'\x05\x08\x00\x01\x00\x00\x00\x00\x00\x00')
                            client_sock.close()
                            return

                        # logger.debug(f"SOCKS5 connecting to {target_ip}:{target_port} via {remote_ip}:{remote_port}")

                        # Kết nối đến target thông qua proxy
                        remote.connect((target_ip, target_port))

                        # Gửi success response
                        client_sock.send(b'\x05\x00\x00\x01\x00\x00\x00\x00\x00\x00')

                        # Bắt đầu forward data
                        threading.Thread(target=self.forward_data, args=(client_sock, remote, "client->remote"), daemon=True).start()
                        threading.Thread(target=self.forward_data, args=(remote, client_sock, "remote->client"), daemon=True).start()

                        # logger.info(f"SOCKS5 connection established: {target_ip}:{target_port}")
                        return  # Keep connection alive

                    except Exception as e:
                        logger.error(f"Failed to connect through SOCKS5 proxy {remote_ip}:{remote_port}: {e}")
                        # Gửi connection refused
                        client_sock.send(b'\x05\x05\x00\x01\x00\x00\x00\x00\x00\x00')
                        client_sock.close()
                else:
                    # Unsupported command
                    client_sock.send(b'\x05\x07\x00\x01\x00\x00\x00\x00\x00\x00')
                    client_sock.close()
            else:
                # Not SOCKS5
                client_sock.close()

        except Exception as e:
            logger.error(f"Error handling SOCKS5 client: {e}")
            try:
                client_sock.close()
            except:
                pass

    def handle_http_client(self, client_sock, remote_ip, remote_port, user, pwd):
        """Xử lý HTTP client"""
        try:
            # Đọc HTTP request
            data = client_sock.recv(4096)
            if not data:
                client_sock.close()
                return

            request = data.decode('utf-8', errors='ignore')
            lines = request.split('\r\n')

            if lines[0].startswith('CONNECT'):
                # HTTP CONNECT method
                parts = lines[0].split(' ')
                if len(parts) >= 2:
                    target = parts[1]
                    if ':' in target:
                        target_host, target_port = target.split(':', 1)
                        target_port = int(target_port)
                    else:
                        target_host = target
                        target_port = 443  # Default HTTPS port

                try:
                    # Kết nối qua SOCKS5 proxy
                    remote = socks.socksocket()
                    if user and pwd:
                        remote.set_proxy(socks.SOCKS5, remote_ip, remote_port, True, user, pwd)
                    else:
                        remote.set_proxy(socks.SOCKS5, remote_ip, remote_port)

                    remote.connect((target_host, target_port))

                    # Gửi 200 Connection established
                    client_sock.send(b'HTTP/1.1 200 Connection established\r\n\r\n')

                    # Forward data
                    threading.Thread(target=self.forward_data, args=(client_sock, remote, "client->remote"), daemon=True).start()
                    threading.Thread(target=self.forward_data, args=(remote, client_sock, "remote->client"), daemon=True).start()

                    # logger.info(f"HTTP CONNECT tunnel established: {target_host}:{target_port}")
                    return  # Keep connection alive

                except Exception as e:
                    logger.error(f"Failed to establish HTTP CONNECT tunnel: {e}")
                    client_sock.send(b'HTTP/1.1 502 Bad Gateway\r\n\r\n')
                    client_sock.close()
            else:
                # Regular HTTP request - not supported for now
                client_sock.send(b'HTTP/1.1 405 Method Not Allowed\r\n\r\n')
                client_sock.close()

        except Exception as e:
            logger.error(f"Error handling HTTP client: {e}")
            try:
                client_sock.close()
            except:
                pass

    def forward_data(self, src, dst, direction=None):
        """Forward data giữa hai socket"""
        try:
            while True:
                try:
                    data = src.recv(4096)
                    if not data:
                        break
                    dst.sendall(data)
                except socket.timeout:
                    continue
                except Exception as e:
                    # logger.debug(f"Forward error ({direction}): {e}")
                    break
        except Exception as e:
            # logger.debug(f"Forward exception ({direction}): {e}")
            pass
        finally:
            try:
                src.close()
            except:
                pass
            try:
                dst.close()
            except:
                pass

if __name__ == "__main__":
    # Bật hỗ trợ DPI scaling cho màn hình độ phân giải cao
    if hasattr(Qt, 'AA_EnableHighDpiScaling'):
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

    # Đặt thuộc tính font mặc định cho toàn bộ ứng dụng
    app = QApplication(sys.argv)

    # Đảm bảo font chữ hiển thị đúng trên tất cả các hệ thống
    # Sử dụng font mặc định của hệ thống để tránh vấn đề tương thích
    font = app.font()

    # Kiểm tra hệ điều hành để chọn font phù hợp
    # Chọn font phù hợp với hệ điều hành
    font.setFamily("Segoe UI" if sys.platform == 'win32' else "Arial")

    # Sử dụng kích thước font nhỏ hơn để đảm bảo hiển thị đầy đủ trên button
    font.setPointSize(8)  # Kích thước font nhỏ hơn để tránh bị cắt
    app.setFont(font)

    # Đặt thuộc tính để tự động điều chỉnh kích thước theo DPI
    if hasattr(Qt, 'AA_DisableWindowContextHelpButton'):
        QApplication.setAttribute(Qt.AA_DisableWindowContextHelpButton, True)

    # Kiểm tra license trước khi khởi động ứng dụng
    license_valid, features = check_license()
    if not license_valid:
        # Thay vì đóng app, cho phép mở với tính năng bị giới hạn
        print("⚠️ Không có license hợp lệ. Mở app với chế độ giới hạn.")
        features = "ALL_BLOCKED"  # Đặt tất cả tính năng bị khóa

        # Hiển thị thông báo và hướng dẫn
        QMessageBox.warning(None, "Cần License",
                           "Không tìm thấy license hợp lệ.\n\n"
                           "Ứng dụng sẽ mở với chế độ giới hạn.\n"
                           "Vui lòng vào Settings → Licenses Info để nhập license key.")

    # Lưu tính năng được phép vào thuộc tính của ứng dụng
    app.setProperty("allowed_features", features)
    if license_valid:
        print(f"✅ License hợp lệ. Các tính năng được phép: {features}")
    else:
        print(f"⚠️ Chế độ giới hạn. Tính năng: {features}")

    window = MainApp()
    window.show()
    sys.exit(app.exec_())
