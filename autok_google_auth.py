#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Module t<PERSON><PERSON> hợp Google Authentication cho AutoK
Sử dụng file google_creds đã được mã hóa
"""

import os
import json
import tempfile
from google.oauth2.service_account import Credentials
from google_creds_manager import GoogleCredsManager

class AutoKGoogleAuth:
    def __init__(self, password: str = None):
        """
        Khởi tạo Google Auth cho AutoK
        """
        self.manager = GoogleCredsManager(password)
        self.password = password
        self._cached_creds = None
        
    def set_password(self, password: str):
        """
        Đặt password cho session hiện tại
        """
        self.password = password
        self.manager.password = password
        self._cached_creds = None  # Clear cache
        
    def verify_access(self) -> bool:
        """
        Kiểm tra có thể truy cập credentials không
        """
        if not self.password:
            return False
        return self.manager.verify_password(self.password)
    
    def get_credentials_data(self) -> dict:
        """
        Lấy credentials data (cache để tr<PERSON>h gi<PERSON><PERSON> m<PERSON> n<PERSON> lầ<PERSON>)
        """
        if self._cached_creds is None:
            if not self.password:
                raise Exception("Chưa đặt password!")
            self._cached_creds = self.manager.decrypt_creds(self.password)
        return self._cached_creds
    
    def get_google_credentials(self, scopes: list = None):
        """
        Tạo Google Credentials object
        """
        try:
            creds_data = self.get_credentials_data()
            
            # Tạo credentials từ service account info
            credentials = Credentials.from_service_account_info(
                creds_data,
                scopes=scopes
            )
            
            return credentials
            
        except Exception as e:
            print(f"❌ Lỗi khi tạo Google credentials: {e}")
            return None
    
    def get_credentials_file_path(self) -> str:
        """
        Tạo file tạm chứa credentials (cho các library cần file path)
        Trả về đường dẫn file tạm
        """
        try:
            creds_data = self.get_credentials_data()
            
            # Tạo file tạm
            temp_file = tempfile.NamedTemporaryFile(
                mode='w', 
                suffix='.json', 
                delete=False,
                prefix='autok_creds_'
            )
            
            json.dump(creds_data, temp_file, indent=2)
            temp_file.close()
            
            return temp_file.name
            
        except Exception as e:
            print(f"❌ Lỗi khi tạo file tạm: {e}")
            return None
    
    def cleanup_temp_file(self, file_path: str):
        """
        Xóa file tạm sau khi sử dụng
        """
        try:
            if file_path and os.path.exists(file_path):
                os.unlink(file_path)
        except Exception as e:
            print(f"⚠️ Không thể xóa file tạm: {e}")
    
    def test_connection(self) -> bool:
        """
        Test kết nối Google API
        """
        try:
            # Test với Google Drive API (scope cơ bản)
            scopes = ['https://www.googleapis.com/auth/drive.metadata.readonly']
            credentials = self.get_google_credentials(scopes)
            
            if credentials is None:
                return False
            
            # Thử tạo service
            from googleapiclient.discovery import build
            service = build('drive', 'v3', credentials=credentials)
            
            # Test call đơn giản
            results = service.files().list(pageSize=1).execute()
            
            print("✅ Kết nối Google API thành công!")
            return True
            
        except Exception as e:
            print(f"❌ Lỗi kết nối Google API: {e}")
            return False

# Singleton instance cho AutoK
_autok_google_auth = None

def get_autok_google_auth(password: str = None) -> AutoKGoogleAuth:
    """
    Lấy singleton instance của AutoKGoogleAuth
    """
    global _autok_google_auth
    
    if _autok_google_auth is None:
        _autok_google_auth = AutoKGoogleAuth(password)
    elif password:
        _autok_google_auth.set_password(password)
    
    return _autok_google_auth

def init_google_auth_for_autok(password: str) -> bool:
    """
    Khởi tạo Google Auth cho AutoK với password
    Trả về True nếu thành công
    """
    try:
        auth = get_autok_google_auth(password)
        
        if not auth.verify_access():
            print("❌ Sai password Google credentials!")
            return False
        
        print("✅ Đã khởi tạo Google Auth thành công!")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi khởi tạo Google Auth: {e}")
        return False

# Code để thêm vào AutoK.py
"""
Thêm vào đầu file AutoK.py:

from autok_google_auth import get_autok_google_auth, init_google_auth_for_autok

Thêm vào __init__ của MainApp:

# Khởi tạo Google Auth
self.google_auth_password = None
self.google_auth_initialized = False

Thêm method vào MainApp:
"""

def init_google_credentials(self):
    """
    Khởi tạo Google credentials (thêm vào AutoK.py)
    """
    try:
        if not os.path.exists("google_creds"):
            self.log_message("⚠️ Không tìm thấy file google_creds")
            return False

        # Lấy password từ user hoặc config
        password = self.get_google_password()

        if not password:
            return False

        # Khởi tạo Google Auth
        if init_google_auth_for_autok(password):
            self.google_auth_password = password
            self.google_auth_initialized = True
            self.log_message("✅ Đã khởi tạo Google credentials thành công!")
            return True
        else:
            self.log_message("❌ Không thể khởi tạo Google credentials!")
            return False

    except Exception as e:
        self.log_message(f"❌ Lỗi khởi tạo Google credentials: {e}")
        return False

def get_google_password(self):
    """
    Lấy password Google credentials (thêm vào AutoK.py)
    """
    # Cách 1: Từ dialog
    from PyQt5.QtWidgets import QInputDialog, QLineEdit

    password, ok = QInputDialog.getText(
        self,
        'Google Credentials',
        'Nhập password Google credentials:',
        QLineEdit.Password
    )

    if ok and password:
        return password

    return None

def get_google_credentials_for_sheets(self):
    """
    Lấy credentials cho Google Sheets (thêm vào AutoK.py)
    """
    if not self.google_auth_initialized:
        if not self.init_google_credentials():
            return None

    auth = get_autok_google_auth()
    scopes = [
        'https://www.googleapis.com/auth/spreadsheets',
        'https://www.googleapis.com/auth/drive'
    ]

    return auth.get_google_credentials(scopes)

if __name__ == "__main__":
    print("🔐 AutoK Google Authentication")
    print("=" * 40)
    
    # Test script
    if os.path.exists("google_creds"):
        password = input("Nhập password để test: ")
        
        auth = AutoKGoogleAuth(password)
        
        if auth.verify_access():
            print("✅ Password đúng!")
            
            # Hiển thị thông tin
            try:
                creds_data = auth.get_credentials_data()
                print(f"Project ID: {creds_data.get('project_id', 'N/A')}")
                print(f"Client Email: {creds_data.get('client_email', 'N/A')}")
                
                # Test kết nối
                print("\nĐang test kết nối...")
                auth.test_connection()
                
            except Exception as e:
                print(f"❌ Lỗi: {e}")
        else:
            print("❌ Password sai!")
    else:
        print("❌ Không tìm thấy file google_creds")
