@echo off
echo ========================================
echo    AutoK Application Builder
echo ========================================
echo.

REM Check if AutoK.py exists
if not exist "AutoK.py" (
    echo ERROR: AutoK.py not found!
    pause
    exit /b 1
)

REM Clean old builds
echo Cleaning old builds...
if exist "build" rmdir /s /q "build"
if exist "AutoK.dist" rmdir /s /q "AutoK.dist"
if exist "dist" rmdir /s /q "dist"
if exist "__pycache__" rmdir /s /q "__pycache__"

REM Create backup
if not exist "backup" mkdir backup
copy "AutoK.py" "backup\AutoK_original.py" >nul

REM Build with Nuitka
echo Building with Nuitka...
echo.

nuitka AutoK.py --standalone --windows-disable-console --windows-icon-from-ico="icon/icon.ico" --enable-plugin=pyqt5 --output-dir=build --remove-output --python-flag=no_docstrings --python-flag=no_asserts --python-flag=-O --lto=yes

if errorlevel 1 (
    echo.
    echo BUILD FAILED!
    pause
    exit /b 1
)

echo.
echo ========================================
echo           BUILD COMPLETED!
echo ========================================
echo.
echo Output folder: build\AutoK.dist\
echo Executable: build\AutoK.dist\AutoK.exe
echo.
echo Remember to copy these folders to build\AutoK.dist\:
echo - template\
echo - data\
echo - icon\
echo - screen\
echo.

pause

