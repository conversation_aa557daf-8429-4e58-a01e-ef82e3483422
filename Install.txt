pip install opencv-python numpy pyqt5 cryptography gspread oauth2client


pip install pyotp

py -m pip install --upgrade pip

pyuic5 Form.ui -o Form.py

Pyinstaller AutoK.py --onedir --noconsole --clean --icon=icon.ico

pip install PyJWT

pip install nuitka

pip install Pyinstaller

pip install PySocks

nuitka AutoK.py --standalone --enable-plugin=tk-inter --output-dir=dist

nuitka AutoK.py --standalone --windows-disable-console --windows-icon-from-ico=icon.ico

pyinstaller AutoK.py --onedir --noconsole --clean --icon=icon.ico

nuitka AutoK.py --standalone --windows-disable-console --windows-icon-from-ico="icon/icon.ico" --enable-plugin=pyqt5 --output-dir=build --remove-output

pyarmor obfuscate --exact AutoK.py
pyarmor-7 obfuscate --exact AutoK.py

nuitka dist/AutoK.py ^
  --standalone ^
  --windows-disable-console ^
  --windows-icon-from-ico="D:\AutoKhuong\icon\icon.ico" ^
  --enable-plugin=pyqt5 ^
  --output-dir=build ^
  --remove-output

nuitka AutoK.py `
--standalone `
--windows-disable-console `
--windows-icon-from-ico="icon/icon.ico" `
--enable-plugin=pyqt5 `
--output-dir=build `
--remove-output


🎯 Tóm tắt: Quy trình đơn giản khi có thay đổi

# Thay đổi code → Test → Chạy lệnh này
python release_new_version.py 1.1.0 "- Sửa lỗi addads không dừng được\n- Thêm Google ẩn danh\n- Cải thiện auto-update"

# Sửa file release_new_version.py
SERVER_CONFIG = {
    'api_url': 'https://your-domain.com/api/admin/update',
    'admin_key': 'your_secret_key',
    'download_base_url': 'https://your-domain.com/downloads/',
    'upload_command': 'scp {file} <EMAIL>:/path/to/downloads/'
}

125,1985 1024,1985