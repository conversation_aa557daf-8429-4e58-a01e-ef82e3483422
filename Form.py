# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'Form.ui'
#
# Created by: PyQt5 UI code generator 5.15.11
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        MainWindow.setObjectName("MainWindow")
        MainWindow.resize(793, 461)
        MainWindow.setMaximumSize(QtCore.QSize(793, 461))
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap("icon/favicon.ico"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        MainWindow.setWindowIcon(icon)
        MainWindow.setWindowOpacity(2.0)
        MainWindow.setStyleSheet("<PERSON><PERSON><PERSON><PERSON>, QCheckBox {\n"
"    background-color: transparent;\n"
"}")
        MainWindow.setIconSize(QtCore.QSize(48, 48))
        self.centralwidget = QtWidgets.QWidget(MainWindow)
        self.centralwidget.setObjectName("centralwidget")
        self.tabWidget_Proxy = QtWidgets.QTabWidget(self.centralwidget)
        self.tabWidget_Proxy.setGeometry(QtCore.QRect(0, 0, 661, 461))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.tabWidget_Proxy.sizePolicy().hasHeightForWidth())
        self.tabWidget_Proxy.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(8)
        font.setBold(False)
        font.setWeight(50)
        self.tabWidget_Proxy.setFont(font)
        self.tabWidget_Proxy.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.tabWidget_Proxy.setStyleSheet("QTabBar::tab {\n"
"    background-color:#dfdfdf;\n"
"    color: black;\n"
"    padding: 2px 10px;\n"
"    height: 20px; /* thấp hơn mặc định */\n"
"    font-size: 11px;\n"
"    min-width: 80px;\n"
"    border-top-right-radius: 6px;\n"
"    margin-right: 2px;\n"
"}\n"
"\n"
"QTabBar::tab:selected {\n"
"    background: #006241;\n"
"    color: white;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"QTabWidget::pane {\n"
"    background-color: transparent;\n"
"    border: 1px solid #ccc;\n"
"    border-top: none;\n"
"    border-bottom-left-radius: 8px;\n"
"    border-bottom-right-radius: 8px;\n"
"}\n"
"\n"
"")
        self.tabWidget_Proxy.setObjectName("tabWidget_Proxy")
        self.tab = QtWidgets.QWidget()
        self.tab.setObjectName("tab")
        self.tableWidget = QtWidgets.QTableWidget(self.tab)
        self.tableWidget.setGeometry(QtCore.QRect(0, 0, 661, 371))
        self.tableWidget.setMaximumSize(QtCore.QSize(661, 371))
        self.tableWidget.setAutoFillBackground(False)
        self.tableWidget.setStyleSheet("\n"
"QTableWidget {\n"
"    background-color: white;\n"
"    gridline-color: #ccc;\n"
"    font-size: 14px;\n"
"}\n"
"\n"
"QTableWidget::item {\n"
"    padding: 4px;\n"
"    color: black;\n"
"}\n"
"\n"
"QTableWidget::item:selected {\n"
"    background-color: #a2d5f2;  /* Màu nền khi chọn */\n"
"    color: black;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    color: darkblue;\n"
"    background-color: #f0f0f0;\n"
"    font-weight: bold;\n"
"    padding: 4px;\n"
"    border: 1px solid #ccc;\n"
"}\n"
"\n"
"")
        self.tableWidget.setObjectName("tableWidget")
        self.tableWidget.setColumnCount(6)
        self.tableWidget.setRowCount(0)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget.setHorizontalHeaderItem(5, item)
        self.tableWidget.horizontalHeader().setCascadingSectionResizes(False)
        self.tableWidget.horizontalHeader().setDefaultSectionSize(80)
        self.tableWidget.horizontalHeader().setSortIndicatorShown(False)
        self.tableWidget.horizontalHeader().setStretchLastSection(True)
        self.tableWidget.verticalHeader().setCascadingSectionResizes(True)
        self.frame_9 = QtWidgets.QFrame(self.tab)
        self.frame_9.setGeometry(QtCore.QRect(0, 370, 659, 71))
        self.frame_9.setStyleSheet("background-color: #bfbfbf;  /* ví dụ: màu be nhạt */\n"
"")
        self.frame_9.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_9.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_9.setObjectName("frame_9")
        self.groupBox_5 = QtWidgets.QGroupBox(self.frame_9)
        self.groupBox_5.setGeometry(QtCore.QRect(318, 9, 341, 51))
        self.groupBox_5.setStyleSheet("QGroupBox {\n"
"    border: 1.2px solid #8d8d8d;\n"
"    border-radius: 5px;\n"
"}\n"
"")
        self.groupBox_5.setTitle("")
        self.groupBox_5.setObjectName("groupBox_5")
        self.toolButton_wallet = QtWidgets.QToolButton(self.groupBox_5)
        self.toolButton_wallet.setGeometry(QtCore.QRect(294, 10, 31, 31))
        self.toolButton_wallet.setStyleSheet("QToolButton {\n"
"    border: none;\n"
"    background-color: transparent;\n"
"}\n"
"")
        self.toolButton_wallet.setText("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap("icon/wallet.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        icon1.addPixmap(QtGui.QPixmap("icon/stop.png"), QtGui.QIcon.Normal, QtGui.QIcon.On)
        icon1.addPixmap(QtGui.QPixmap("icon/stop.png"), QtGui.QIcon.Disabled, QtGui.QIcon.Off)
        self.toolButton_wallet.setIcon(icon1)
        self.toolButton_wallet.setIconSize(QtCore.QSize(25, 25))
        self.toolButton_wallet.setCheckable(True)
        self.toolButton_wallet.setObjectName("toolButton_wallet")
        self.add_card_Button = QtWidgets.QToolButton(self.groupBox_5)
        self.add_card_Button.setGeometry(QtCore.QRect(238, 10, 31, 31))
        self.add_card_Button.setStyleSheet("QToolButton {\n"
"    border: none;\n"
"    background-color: transparent;\n"
"}\n"
"")
        self.add_card_Button.setText("")
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap("icon/ads.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        icon2.addPixmap(QtGui.QPixmap("icon/stop1.png"), QtGui.QIcon.Normal, QtGui.QIcon.On)
        icon2.addPixmap(QtGui.QPixmap("icon/stop1.png"), QtGui.QIcon.Disabled, QtGui.QIcon.Off)
        self.add_card_Button.setIcon(icon2)
        self.add_card_Button.setIconSize(QtCore.QSize(25, 25))
        self.add_card_Button.setCheckable(True)
        self.add_card_Button.setObjectName("add_card_Button")
        self.pushButton_Mua = QtWidgets.QToolButton(self.groupBox_5)
        self.pushButton_Mua.setGeometry(QtCore.QRect(72, 9, 31, 31))
        self.pushButton_Mua.setStyleSheet("QToolButton {\n"
"    border: none;\n"
"    background-color: transparent;\n"
"}\n"
"")
        self.pushButton_Mua.setText("")
        icon3 = QtGui.QIcon()
        icon3.addPixmap(QtGui.QPixmap("icon/mua.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        icon3.addPixmap(QtGui.QPixmap("icon/stop1.png"), QtGui.QIcon.Normal, QtGui.QIcon.On)
        icon3.addPixmap(QtGui.QPixmap("icon/stop1.png"), QtGui.QIcon.Disabled, QtGui.QIcon.Off)
        icon3.addPixmap(QtGui.QPixmap("icon/mua.png"), QtGui.QIcon.Active, QtGui.QIcon.Off)
        self.pushButton_Mua.setIcon(icon3)
        self.pushButton_Mua.setIconSize(QtCore.QSize(25, 25))
        self.pushButton_Mua.setCheckable(True)
        self.pushButton_Mua.setObjectName("pushButton_Mua")
        self.xoa_the_close_Button = QtWidgets.QToolButton(self.groupBox_5)
        self.xoa_the_close_Button.setGeometry(QtCore.QRect(184, 9, 31, 31))
        self.xoa_the_close_Button.setStyleSheet("QToolButton {\n"
"    border: none;\n"
"    background-color: transparent;\n"
"}\n"
"")
        icon4 = QtGui.QIcon()
        icon4.addPixmap(QtGui.QPixmap("icon/delete.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        icon4.addPixmap(QtGui.QPixmap("icon/stop1.png"), QtGui.QIcon.Normal, QtGui.QIcon.On)
        icon4.addPixmap(QtGui.QPixmap("icon/stop1.png"), QtGui.QIcon.Disabled, QtGui.QIcon.Off)
        self.xoa_the_close_Button.setIcon(icon4)
        self.xoa_the_close_Button.setIconSize(QtCore.QSize(25, 25))
        self.xoa_the_close_Button.setCheckable(True)
        self.xoa_the_close_Button.setObjectName("xoa_the_close_Button")
        self.close_payment_Button = QtWidgets.QToolButton(self.groupBox_5)
        self.close_payment_Button.setGeometry(QtCore.QRect(126, 10, 31, 31))
        self.close_payment_Button.setStyleSheet("QToolButton {\n"
"    border: none;\n"
"    background-color: transparent;\n"
"}\n"
"")
        self.close_payment_Button.setText("")
        icon5 = QtGui.QIcon()
        icon5.addPixmap(QtGui.QPixmap("icon/close.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        icon5.addPixmap(QtGui.QPixmap("icon/stop1.png"), QtGui.QIcon.Normal, QtGui.QIcon.On)
        icon5.addPixmap(QtGui.QPixmap("icon/stop1.png"), QtGui.QIcon.Disabled, QtGui.QIcon.Off)
        self.close_payment_Button.setIcon(icon5)
        self.close_payment_Button.setIconSize(QtCore.QSize(25, 25))
        self.close_payment_Button.setCheckable(True)
        self.close_payment_Button.setObjectName("close_payment_Button")
        self.chplay = QtWidgets.QToolButton(self.groupBox_5)
        self.chplay.setGeometry(QtCore.QRect(15, 9, 31, 31))
        self.chplay.setStyleSheet("QToolButton {\n"
"    border: none;\n"
"    background-color: transparent;\n"
"}\n"
"")
        self.chplay.setText("")
        icon6 = QtGui.QIcon()
        icon6.addPixmap(QtGui.QPixmap("icon/chplay.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        icon6.addPixmap(QtGui.QPixmap("icon/stop1.png"), QtGui.QIcon.Normal, QtGui.QIcon.On)
        icon6.addPixmap(QtGui.QPixmap("icon/stop1.png"), QtGui.QIcon.Disabled, QtGui.QIcon.Off)
        self.chplay.setIcon(icon6)
        self.chplay.setIconSize(QtCore.QSize(25, 25))
        self.chplay.setCheckable(True)
        self.chplay.setChecked(False)
        self.chplay.setAutoRepeat(False)
        self.chplay.setAutoExclusive(False)
        self.chplay.setAutoRaise(False)
        self.chplay.setObjectName("chplay")
        self.groupBox_7 = QtWidgets.QGroupBox(self.frame_9)
        self.groupBox_7.setGeometry(QtCore.QRect(10, 9, 51, 51))
        self.groupBox_7.setStyleSheet("QGroupBox {\n"
"    border: 1.2px solid #8d8d8d;\n"
"    border-radius: 5px;\n"
"}\n"
"")
        self.groupBox_7.setTitle("")
        self.groupBox_7.setObjectName("groupBox_7")
        self.button_wifi = QtWidgets.QToolButton(self.groupBox_7)
        self.button_wifi.setGeometry(QtCore.QRect(10, 10, 31, 31))
        self.button_wifi.setFocusPolicy(QtCore.Qt.WheelFocus)
        self.button_wifi.setStyleSheet("QToolButton {\n"
"    border: none;\n"
"    background-color: transparent;\n"
"}\n"
"QToolButton:checked {\n"
"    background-color: rgba(0, 255, 0, 15); /* nền rất nhẹ */\n"
"     border-radius: 20px; /* tùy chọn bo góc */\n"
"}\n"
"")
        self.button_wifi.setText("")
        icon7 = QtGui.QIcon()
        icon7.addPixmap(QtGui.QPixmap("icon/wifi-off.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        icon7.addPixmap(QtGui.QPixmap("icon/wifi.png"), QtGui.QIcon.Normal, QtGui.QIcon.On)
        icon7.addPixmap(QtGui.QPixmap("icon/wifi-off.png"), QtGui.QIcon.Disabled, QtGui.QIcon.Off)
        icon7.addPixmap(QtGui.QPixmap("icon/wifi-off.png"), QtGui.QIcon.Disabled, QtGui.QIcon.On)
        icon7.addPixmap(QtGui.QPixmap("icon/wifi-off.png"), QtGui.QIcon.Active, QtGui.QIcon.Off)
        icon7.addPixmap(QtGui.QPixmap("icon/wifi.png"), QtGui.QIcon.Active, QtGui.QIcon.On)
        icon7.addPixmap(QtGui.QPixmap("icon/wifi-off.png"), QtGui.QIcon.Selected, QtGui.QIcon.Off)
        icon7.addPixmap(QtGui.QPixmap("icon/wifi.png"), QtGui.QIcon.Selected, QtGui.QIcon.On)
        self.button_wifi.setIcon(icon7)
        self.button_wifi.setIconSize(QtCore.QSize(22, 22))
        self.button_wifi.setCheckable(True)
        self.button_wifi.setObjectName("button_wifi")
        self.groupBox_6 = QtWidgets.QGroupBox(self.frame_9)
        self.groupBox_6.setGeometry(QtCore.QRect(72, 9, 171, 51))
        self.groupBox_6.setStyleSheet("QGroupBox {\n"
"    border: 1.2px solid #8d8d8d;\n"
"    border-radius: 5px;\n"
"}\n"
"")
        self.groupBox_6.setTitle("")
        self.groupBox_6.setObjectName("groupBox_6")
        self.restore = QtWidgets.QToolButton(self.groupBox_6)
        self.restore.setGeometry(QtCore.QRect(122, 10, 31, 31))
        self.restore.setStyleSheet("QToolButton {\n"
"    border: none;\n"
"    background-color: transparent;\n"
"}\n"
"")
        self.restore.setText("")
        icon8 = QtGui.QIcon()
        icon8.addPixmap(QtGui.QPixmap("icon/restore.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        icon8.addPixmap(QtGui.QPixmap("icon/stop.png"), QtGui.QIcon.Normal, QtGui.QIcon.On)
        self.restore.setIcon(icon8)
        self.restore.setIconSize(QtCore.QSize(25, 25))
        self.restore.setCheckable(True)
        self.restore.setObjectName("restore")
        self.changer = QtWidgets.QToolButton(self.groupBox_6)
        self.changer.setGeometry(QtCore.QRect(67, 10, 31, 31))
        self.changer.setStyleSheet("QToolButton {\n"
"    border: none;\n"
"    background-color: transparent;\n"
"}\n"
"")
        self.changer.setText("")
        icon9 = QtGui.QIcon()
        icon9.addPixmap(QtGui.QPixmap("icon/changer.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        icon9.addPixmap(QtGui.QPixmap("icon/stop.png"), QtGui.QIcon.Normal, QtGui.QIcon.On)
        self.changer.setIcon(icon9)
        self.changer.setIconSize(QtCore.QSize(25, 25))
        self.changer.setCheckable(True)
        self.changer.setObjectName("changer")
        self.backup = QtWidgets.QToolButton(self.groupBox_6)
        self.backup.setGeometry(QtCore.QRect(13, 10, 31, 31))
        self.backup.setStyleSheet("QToolButton {\n"
"    border: none;\n"
"    background-color: transparent;\n"
"}\n"
"")
        self.backup.setText("")
        icon10 = QtGui.QIcon()
        icon10.addPixmap(QtGui.QPixmap("icon/backup.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        icon10.addPixmap(QtGui.QPixmap("icon/stop.png"), QtGui.QIcon.Normal, QtGui.QIcon.On)
        self.backup.setIcon(icon10)
        self.backup.setIconSize(QtCore.QSize(25, 25))
        self.backup.setCheckable(True)
        self.backup.setObjectName("backup")
        self.checkBoxALL = QtWidgets.QCheckBox(self.tab)
        self.checkBoxALL.setGeometry(QtCore.QRect(27, 1, 31, 31))
        self.checkBoxALL.setStyleSheet("QCheckBox::indicator {\n"
"    width: 0px;\n"
"    height: 0px;\n"
"}")
        icon11 = QtGui.QIcon()
        icon11.addPixmap(QtGui.QPixmap("icon/checkbox_off.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        icon11.addPixmap(QtGui.QPixmap("icon/checkbox_on.png"), QtGui.QIcon.Normal, QtGui.QIcon.On)
        self.checkBoxALL.setIcon(icon11)
        self.checkBoxALL.setIconSize(QtCore.QSize(21, 21))
        self.checkBoxALL.setAutoRepeatDelay(300)
        self.checkBoxALL.setObjectName("checkBoxALL")
        self.tabWidget_Proxy.addTab(self.tab, "")
        self.tab_2 = QtWidgets.QWidget()
        self.tab_2.setObjectName("tab_2")
        self.tableView = QtWidgets.QTableView(self.tab_2)
        self.tableView.setGeometry(QtCore.QRect(0, 0, 661, 431))
        self.tableView.setObjectName("tableView")
        self.frame_5 = QtWidgets.QFrame(self.tab_2)
        self.frame_5.setGeometry(QtCore.QRect(0, 0, 661, 441))
        self.frame_5.setStyleSheet("background-color: #ececec; ")
        self.frame_5.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_5.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.frame_5.setObjectName("frame_5")
        self.wide = QtWidgets.QLabel(self.frame_5)
        self.wide.setGeometry(QtCore.QRect(260, 44, 21, 16))
        self.wide.setText("")
        self.wide.setObjectName("wide")
        self.high = QtWidgets.QLabel(self.frame_5)
        self.high.setGeometry(QtCore.QRect(260, 43, 21, 16))
        self.high.setText("")
        self.high.setObjectName("high")
        self.groupBox = QtWidgets.QGroupBox(self.frame_5)
        self.groupBox.setGeometry(QtCore.QRect(9, 179, 641, 241))
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(True)
        font.setWeight(75)
        self.groupBox.setFont(font)
        self.groupBox.setStyleSheet("QGroupBox {\n"
"    border: 1.2px solid #c0c0c0;\n"
"    border-radius: 8px;\n"
"    padding: 10px;\n"
"    margin-top: 10px; /* khoảng cách cho tiêu đề */\n"
"    background-color: #f7f7f7;\n"
"}\n"
"QGroupBox::title {\n"
"    subcontrol-origin: margin;\n"
"    subcontrol-position: top left;\n"
"    padding: 0 5px;\n"
"}")
        self.groupBox.setObjectName("groupBox")
        self.comboBox_4 = QtWidgets.QComboBox(self.groupBox)
        self.comboBox_4.setGeometry(QtCore.QRect(510, 196, 101, 22))
        self.comboBox_4.setObjectName("comboBox_4")
        self.comboBox_4.addItem("")
        self.checkBox_mua = QtWidgets.QCheckBox(self.groupBox)
        self.checkBox_mua.setGeometry(QtCore.QRect(479, 198, 16, 20))
        self.checkBox_mua.setText("")
        self.checkBox_mua.setObjectName("checkBox_mua")
        self.checkBox_xoa_close_payment = QtWidgets.QCheckBox(self.groupBox)
        self.checkBox_xoa_close_payment.setGeometry(QtCore.QRect(480, 158, 16, 17))
        self.checkBox_xoa_close_payment.setText("")
        self.checkBox_xoa_close_payment.setObjectName("checkBox_xoa_close_payment")
        self.comboBox_wallet_ads = QtWidgets.QComboBox(self.groupBox)
        self.comboBox_wallet_ads.setGeometry(QtCore.QRect(510, 115, 101, 22))
        self.comboBox_wallet_ads.setObjectName("comboBox_wallet_ads")
        self.comboBox_wallet_ads.addItem("")
        self.comboBox_wallet_ads.addItem("")
        self.checkBox_restore_email = QtWidgets.QCheckBox(self.groupBox)
        self.checkBox_restore_email.setGeometry(QtCore.QRect(480, 40, 16, 17))
        self.checkBox_restore_email.setStyleSheet("QLabel {\n"
"    background: transparent;\n"
"}")
        self.checkBox_restore_email.setText("")
        self.checkBox_restore_email.setObjectName("checkBox_restore_email")
        self.comboBox_restore = QtWidgets.QComboBox(self.groupBox)
        self.comboBox_restore.setGeometry(QtCore.QRect(510, 38, 101, 22))
        self.comboBox_restore.setObjectName("comboBox_restore")
        self.comboBox_restore.addItem("")
        self.comboBox_restore.addItem("")
        self.checkBox_add_wallet_ads = QtWidgets.QCheckBox(self.groupBox)
        self.checkBox_add_wallet_ads.setGeometry(QtCore.QRect(480, 118, 16, 17))
        self.checkBox_add_wallet_ads.setStyleSheet("QLabel {\n"
"    background: transparent;\n"
"}")
        self.checkBox_add_wallet_ads.setText("")
        self.checkBox_add_wallet_ads.setObjectName("checkBox_add_wallet_ads")
        self.comboBox_del_close = QtWidgets.QComboBox(self.groupBox)
        self.comboBox_del_close.setGeometry(QtCore.QRect(510, 155, 101, 22))
        self.comboBox_del_close.setObjectName("comboBox_del_close")
        self.comboBox_del_close.addItem("")
        self.comboBox_del_close.addItem("")
        self.label_5 = QtWidgets.QLabel(self.groupBox)
        self.label_5.setGeometry(QtCore.QRect(190, 168, 101, 16))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.label_5.setFont(font)
        self.label_5.setStyleSheet("QLabel {\n"
"    background: transparent;\n"
"}")
        self.label_5.setObjectName("label_5")
        self.goi1 = QtWidgets.QSpinBox(self.groupBox)
        self.goi1.setGeometry(QtCore.QRect(347, 39, 42, 22))
        self.goi1.setMaximum(15)
        self.goi1.setSingleStep(1)
        self.goi1.setObjectName("goi1")
        self.goi2 = QtWidgets.QSpinBox(self.groupBox)
        self.goi2.setGeometry(QtCore.QRect(347, 74, 42, 22))
        self.goi2.setMaximum(15)
        self.goi2.setSingleStep(1)
        self.goi2.setObjectName("goi2")
        self.label_4 = QtWidgets.QLabel(self.groupBox)
        self.label_4.setGeometry(QtCore.QRect(190, 41, 91, 16))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.label_4.setFont(font)
        self.label_4.setStyleSheet("QLabel {\n"
"    background: transparent;\n"
"}")
        self.label_4.setObjectName("label_4")
        self.goi3 = QtWidgets.QSpinBox(self.groupBox)
        self.goi3.setGeometry(QtCore.QRect(347, 108, 42, 22))
        self.goi3.setMaximum(50)
        self.goi3.setObjectName("goi3")
        self.spinBox_so_lan_add_card = QtWidgets.QSpinBox(self.groupBox)
        self.spinBox_so_lan_add_card.setGeometry(QtCore.QRect(347, 162, 41, 22))
        self.spinBox_so_lan_add_card.setStyleSheet("color: #000000;    ")
        self.spinBox_so_lan_add_card.setMinimum(1)
        self.spinBox_so_lan_add_card.setMaximum(50)
        self.spinBox_so_lan_add_card.setSingleStep(1)
        self.spinBox_so_lan_add_card.setProperty("value", 3)
        self.spinBox_so_lan_add_card.setObjectName("spinBox_so_lan_add_card")
        self.checkBox_chplay = QtWidgets.QCheckBox(self.groupBox)
        self.checkBox_chplay.setGeometry(QtCore.QRect(480, 80, 16, 17))
        self.checkBox_chplay.setStyleSheet("QLabel {\n"
"    background: transparent;\n"
"}")
        self.checkBox_chplay.setText("")
        self.checkBox_chplay.setObjectName("checkBox_chplay")
        self.comboBox = QtWidgets.QComboBox(self.groupBox)
        self.comboBox.setGeometry(QtCore.QRect(510, 78, 101, 22))
        self.comboBox.setObjectName("comboBox")
        self.comboBox.addItem("")
        self.lineEdit_port_doma = QtWidgets.QLineEdit(self.groupBox)
        self.lineEdit_port_doma.setGeometry(QtCore.QRect(93, 40, 51, 20))
        self.lineEdit_port_doma.setStyleSheet("QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 4px;     /* 👈 Bo góc */\n"
"    padding: 2px;\n"
"}\n"
"")
        self.lineEdit_port_doma.setObjectName("lineEdit_port_doma")
        self.label = QtWidgets.QLabel(self.groupBox)
        self.label.setGeometry(QtCore.QRect(23, 41, 61, 16))
        self.label.setStyleSheet("QLabel {\n"
"    background: transparent;\n"
"}")
        self.label.setObjectName("label")
        self.label_3 = QtWidgets.QLabel(self.groupBox)
        self.label_3.setGeometry(QtCore.QRect(190, 200, 151, 16))
        self.label_3.setStyleSheet("QLabel {\n"
"    background: transparent;\n"
"}")
        self.label_3.setObjectName("label_3")
        self.spinBox_error = QtWidgets.QSpinBox(self.groupBox)
        self.spinBox_error.setGeometry(QtCore.QRect(347, 197, 42, 22))
        self.spinBox_error.setObjectName("spinBox_error")
        self.groupBox_3 = QtWidgets.QGroupBox(self.frame_5)
        self.groupBox_3.setGeometry(QtCore.QRect(9, 4, 641, 171))
        self.groupBox_3.setStyleSheet("QGroupBox {\n"
"    border: 1.2px solid #c0c0c0;\n"
"    border-radius: 8px;\n"
"    padding: 10px;\n"
"    margin-top: 10px; /* khoảng cách cho tiêu đề */\n"
"    background-color: #f7f7f7;\n"
"}\n"
"QGroupBox::title {\n"
"    subcontrol-origin: margin;\n"
"    subcontrol-position: top left;\n"
"    padding: 0 5px;\n"
"}")
        self.groupBox_3.setTitle("")
        self.groupBox_3.setObjectName("groupBox_3")
        self.licenses = QtWidgets.QPushButton(self.groupBox_3)
        self.licenses.setGeometry(QtCore.QRect(14, 128, 91, 23))
        self.licenses.setStyleSheet("QPushButton {\n"
"    background-color: #939393;      /* Xám đậm */\n"
"    color: #F0F0F0;                 /* Chữ trắng xám */\n"
"    border: 1px solid #444;         /* Viền xám than */\n"
"    border-radius: 4px;             /* Bo góc nhẹ */\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color:#7c7c7c;      /* Hover sáng nhẹ */\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: #2e688d;      /* Nhấn: tối hơn */\n"
"}\n"
"")
        self.licenses.setObjectName("licenses")
        self.groupBox_8 = QtWidgets.QGroupBox(self.groupBox_3)
        self.groupBox_8.setGeometry(QtCore.QRect(12, 10, 351, 91))
        self.groupBox_8.setTitle("")
        self.groupBox_8.setObjectName("groupBox_8")
        self.spinBox_view = QtWidgets.QSpinBox(self.groupBox_8)
        self.spinBox_view.setGeometry(QtCore.QRect(198, 53, 42, 22))
        self.spinBox_view.setObjectName("spinBox_view")
        self.horizontalSlider = QtWidgets.QSlider(self.groupBox_8)
        self.horizontalSlider.setGeometry(QtCore.QRect(196, 20, 131, 22))
        self.horizontalSlider.setStyleSheet("QSlider:focus {\n"
"    outline: none;\n"
"    border: none;\n"
"}\n"
"\n"
"QSlider::groove:horizontal {\n"
"    border: 1px solid #00aa7f;\n"
"    height: 6px;\n"
"    background: #444;\n"
"    border-radius: 3px;\n"
"}\n"
"\n"
"QSlider::handle:horizontal {\n"
"    background: #00aa7f;\n"
"    border: 1px solid #5c5c5c;\n"
"    width: 14px;\n"
"    margin: -4px 0; /* để align với groove */\n"
"    border-radius: 7px;\n"
"}\n"
"")
        self.horizontalSlider.setOrientation(QtCore.Qt.Horizontal)
        self.horizontalSlider.setObjectName("horizontalSlider")
        self.label_6 = QtWidgets.QLabel(self.groupBox_8)
        self.label_6.setGeometry(QtCore.QRect(16, 22, 201, 16))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.label_6.setFont(font)
        self.label_6.setStyleSheet("QLabel {\n"
"    background: transparent;\n"
"}")
        self.label_6.setObjectName("label_6")
        self.label_2 = QtWidgets.QLabel(self.groupBox_8)
        self.label_2.setGeometry(QtCore.QRect(16, 58, 101, 16))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.label_2.setFont(font)
        self.label_2.setStyleSheet("QLabel {\n"
"    background: transparent;\n"
"}")
        self.label_2.setObjectName("label_2")
        self.groupBox_9 = QtWidgets.QGroupBox(self.groupBox_3)
        self.groupBox_9.setGeometry(QtCore.QRect(510, 10, 120, 151))
        self.groupBox_9.setTitle("")
        self.groupBox_9.setObjectName("groupBox_9")
        self.open_card_Button = QtWidgets.QPushButton(self.groupBox_9)
        self.open_card_Button.setGeometry(QtCore.QRect(10, 111, 101, 23))
        self.open_card_Button.setStyleSheet("QPushButton {\n"
"    background-color: #939393;      /* Xám đậm */\n"
"    color: #F0F0F0;                 /* Chữ trắng xám */\n"
"    border: 1px solid #444;         /* Viền xám than */\n"
"    border-radius: 4px;             /* Bo góc nhẹ */\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color:#7c7c7c;      /* Hover sáng nhẹ */\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: #2e688d;      /* Nhấn: tối hơn */\n"
"}\n"
"")
        self.open_card_Button.setObjectName("open_card_Button")
        self.pushButton = QtWidgets.QPushButton(self.groupBox_9)
        self.pushButton.setGeometry(QtCore.QRect(10, 24, 101, 23))
        self.pushButton.setStyleSheet("QPushButton {\n"
"    background-color: #939393;      /* Xám đậm */\n"
"    color: #F0F0F0;                 /* Chữ trắng xám */\n"
"    border: 1px solid #444;         /* Viền xám than */\n"
"    border-radius: 4px;             /* Bo góc nhẹ */\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color:#7c7c7c;      /* Hover sáng nhẹ */\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: #2e688d;      /* Nhấn: tối hơn */\n"
"}\n"
"")
        self.pushButton.setObjectName("pushButton")
        self.open_mail_Button = QtWidgets.QPushButton(self.groupBox_9)
        self.open_mail_Button.setGeometry(QtCore.QRect(10, 68, 101, 23))
        self.open_mail_Button.setStyleSheet("QPushButton {\n"
"    background-color: #939393;      /* Xám đậm */\n"
"    color: #F0F0F0;                 /* Chữ trắng xám */\n"
"    border: 1px solid #444;         /* Viền xám than */\n"
"    border-radius: 4px;             /* Bo góc nhẹ */\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color:#7c7c7c;      /* Hover sáng nhẹ */\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: #2e688d;      /* Nhấn: tối hơn */\n"
"}\n"
"")
        self.open_mail_Button.setObjectName("open_mail_Button")
        self.tabWidget_Proxy.addTab(self.tab_2, "")
        self.tab_3 = QtWidgets.QWidget()
        self.tab_3.setObjectName("tab_3")
        self.groupBox_proxy = QtWidgets.QGroupBox(self.tab_3)
        self.groupBox_proxy.setGeometry(QtCore.QRect(9, 139, 641, 281))
        self.groupBox_proxy.setStyleSheet("QGroupBox {\n"
"    border: 1.2px solid #c0c0c0;\n"
"    border-radius: 8px;\n"
"    padding: 10px;\n"
"    margin-top: 10px; /* khoảng cách cho tiêu đề */\n"
"}\n"
"QGroupBox::title {\n"
"    subcontrol-origin: margin;\n"
"    subcontrol-position: top left;\n"
"    padding: 0 20  px;\n"
"\n"
"}")
        self.groupBox_proxy.setObjectName("groupBox_proxy")
        self.listView = QtWidgets.QListView(self.groupBox_proxy)
        self.listView.setGeometry(QtCore.QRect(10, 30, 621, 241))
        self.listView.setStyleSheet("QListView {\n"
"    border: 1.2px solid #cccccc;\n"
"    border-radius: 10px;\n"
"    padding: 5px;\n"
"    background-color: white;\n"
"}")
        self.listView.setObjectName("listView")
        self.groupBox_4 = QtWidgets.QGroupBox(self.tab_3)
        self.groupBox_4.setGeometry(QtCore.QRect(10, 4, 641, 131))
        self.groupBox_4.setStyleSheet("QGroupBox {\n"
"    border: 1.2px solid #c0c0c0;\n"
"    border-radius: 8px;\n"
"    padding: 10px;\n"
"    margin-top: 10px; /* khoảng cách cho tiêu đề */\n"
"    background-color: #f7f7f7;\n"
"}\n"
"QGroupBox::title {\n"
"    subcontrol-origin: margin;\n"
"    subcontrol-position: top left;\n"
"    padding: 0 5px;\n"
"}")
        self.groupBox_4.setTitle("")
        self.groupBox_4.setObjectName("groupBox_4")
        self.pushButton_8 = QtWidgets.QPushButton(self.groupBox_4)
        self.pushButton_8.setGeometry(QtCore.QRect(502, 95, 121, 23))
        self.pushButton_8.setStyleSheet("QPushButton {\n"
"    background-color: #939393;      /* Xám đậm */\n"
"    color: #F0F0F0;                 /* Chữ trắng xám */\n"
"    border: 1px solid #444;         /* Viền xám than */\n"
"    border-radius: 4px;             /* Bo góc nhẹ */\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color:#7c7c7c;      /* Hover sáng nhẹ */\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: #2e688d;      /* Nhấn: tối hơn */\n"
"}\n"
"")
        self.pushButton_8.setObjectName("pushButton_8")
        self.pushButton_5 = QtWidgets.QPushButton(self.groupBox_4)
        self.pushButton_5.setGeometry(QtCore.QRect(352, 23, 121, 23))
        self.pushButton_5.setStyleSheet("QPushButton {\n"
"    background-color: #939393;      /* Xám đậm */\n"
"    color: #F0F0F0;                 /* Chữ trắng xám */\n"
"    border: 1px solid #444;         /* Viền xám than */\n"
"    border-radius: 4px;             /* Bo góc nhẹ */\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color:#7c7c7c;      /* Hover sáng nhẹ */\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: #2e688d;      /* Nhấn: tối hơn */\n"
"}\n"
"")
        self.pushButton_5.setObjectName("pushButton_5")
        self.pushButton_2 = QtWidgets.QPushButton(self.groupBox_4)
        self.pushButton_2.setGeometry(QtCore.QRect(352, 95, 121, 23))
        self.pushButton_2.setStyleSheet("QPushButton {\n"
"    background-color: #939393;      /* Xám đậm */\n"
"    color: #F0F0F0;                 /* Chữ trắng xám */\n"
"    border: 1px solid #444;         /* Viền xám than */\n"
"    border-radius: 4px;             /* Bo góc nhẹ */\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color:#7c7c7c;      /* Hover sáng nhẹ */\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: #2e688d;      /* Nhấn: tối hơn */\n"
"}\n"
"")
        self.pushButton_2.setObjectName("pushButton_2")
        self.pushButton_7 = QtWidgets.QPushButton(self.groupBox_4)
        self.pushButton_7.setGeometry(QtCore.QRect(502, 59, 121, 23))
        self.pushButton_7.setStyleSheet("QPushButton {\n"
"    color: #F0F0F0;\n"
"    border: 1px solid #444;\n"
"    border-radius: 4px;\n"
"}\n"
"\n"
"/* OFF (mặc định): màu đỏ */\n"
"QPushButton:!checked {\n"
"    background-color: #c0392b;\n"
"}\n"
"\n"
"/* Hover khi OFF */\n"
"QPushButton:!checked:hover {\n"
"    background-color: #e74c3c;\n"
"}\n"
"\n"
"/* ON (checked): màu xanh */\n"
"QPushButton:checked {\n"
"    background-color: rgb(16, 147, 99);\n"
"}\n"
"\n"
"/* Hover khi ON */\n"
"QPushButton:checked:hover {\n"
"    background-color: #7c7c7c;\n"
"}\n"
"\n"
"/* Khi nhấn */\n"
"QPushButton:pressed {\n"
"    background-color: #2e688d;\n"
"}\n"
"")
        self.pushButton_7.setCheckable(True)
        self.pushButton_7.setObjectName("pushButton_7")
        self.pushButton_6 = QtWidgets.QPushButton(self.groupBox_4)
        self.pushButton_6.setGeometry(QtCore.QRect(502, 23, 121, 23))
        self.pushButton_6.setStyleSheet("QPushButton {\n"
"    background-color: #939393;      /* Xám đậm */\n"
"    color: #F0F0F0;                 /* Chữ trắng xám */\n"
"    border: 1px solid #444;         /* Viền xám than */\n"
"    border-radius: 4px;             /* Bo góc nhẹ */\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color:#7c7c7c;      /* Hover sáng nhẹ */\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: #2e688d;      /* Nhấn: tối hơn */\n"
"}\n"
"")
        self.pushButton_6.setObjectName("pushButton_6")
        self.pushButton_4 = QtWidgets.QPushButton(self.groupBox_4)
        self.pushButton_4.setGeometry(QtCore.QRect(352, 59, 121, 23))
        self.pushButton_4.setStyleSheet("QPushButton {\n"
"    background-color: #939393;      /* Xám đậm */\n"
"    color: #F0F0F0;                 /* Chữ trắng xám */\n"
"    border: 1px solid #444;         /* Viền xám than */\n"
"    border-radius: 4px;             /* Bo góc nhẹ */\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color:#7c7c7c;      /* Hover sáng nhẹ */\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: #2e688d;      /* Nhấn: tối hơn */\n"
"}\n"
"")
        self.pushButton_4.setObjectName("pushButton_4")
        self.label_9 = QtWidgets.QLabel(self.groupBox_4)
        self.label_9.setGeometry(QtCore.QRect(13, 92, 231, 16))
        self.label_9.setObjectName("label_9")
        self.label_8 = QtWidgets.QLabel(self.groupBox_4)
        self.label_8.setGeometry(QtCore.QRect(13, 69, 221, 16))
        self.label_8.setObjectName("label_8")
        self.tabWidget_Proxy.addTab(self.tab_3, "")
        self.frame_7 = QtWidgets.QFrame(self.centralwidget)
        self.frame_7.setGeometry(QtCore.QRect(660, 0, 141, 461))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.frame_7.sizePolicy().hasHeightForWidth())
        self.frame_7.setSizePolicy(sizePolicy)
        self.frame_7.setMinimumSize(QtCore.QSize(141, 461))
        self.frame_7.setMaximumSize(QtCore.QSize(461, 461))
        self.frame_7.setAutoFillBackground(False)
        self.frame_7.setStyleSheet("background-color: #bfbfbf;  /* ví dụ: màu be nhạt */\n"
"")
        self.frame_7.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_7.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_7.setObjectName("frame_7")
        self.paste_pass_Button = QtWidgets.QPushButton(self.frame_7)
        self.paste_pass_Button.setGeometry(QtCore.QRect(15, 180, 101, 35))
        self.paste_pass_Button.setStyleSheet("QPushButton {\n"
"    background-color: #5b5b5b;      /* Xám đậm */\n"
"    color: #F0F0F0;                 /* Chữ trắng xám */\n"
"    border: 1px solid #444;         /* Viền xám than */\n"
"    border-radius: 8px;             /* Bo góc nhẹ */\n"
"    padding: 6px 12px;              /* Đệm */\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #919191;      /* Hover sáng nhẹ */\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: #2e688d;      /* Nhấn: tối hơn */\n"
"}\n"
"")
        self.paste_pass_Button.setObjectName("paste_pass_Button")
        self.view_Button = QtWidgets.QPushButton(self.frame_7)
        self.view_Button.setGeometry(QtCore.QRect(16, 112, 101, 27))
        self.view_Button.setStyleSheet("QPushButton {\n"
"    background-color: #5b5b5b;      /* Xám đậm */\n"
"    color: #F0F0F0;                 /* Chữ trắng xám */\n"
"    border: 1px solid #444;         /* Viền xám than */\n"
"    border-radius: 8px;             /* Bo góc nhẹ */\n"
"    padding: 6px 12px;              /* Đệm */\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #919191;      /* Hover sáng nhẹ */\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: #2e688d;      /* Nhấn: tối hơn */\n"
"}\n"
"")
        self.view_Button.setObjectName("view_Button")
        self.pase_data_Button = QtWidgets.QPushButton(self.frame_7)
        self.pase_data_Button.setGeometry(QtCore.QRect(15, 272, 101, 35))
        self.pase_data_Button.setStyleSheet("QPushButton {\n"
"    background-color: #5b5b5b;      /* Xám đậm */\n"
"    color: #F0F0F0;                 /* Chữ trắng xám */\n"
"    border: 1px solid #444;         /* Viền xám than */\n"
"    border-radius: 8px;             /* Bo góc nhẹ */\n"
"    padding: 6px 12px;              /* Đệm */\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #919191;      /* Hover sáng nhẹ */\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: #2e688d;      /* Nhấn: tối hơn */\n"
"}\n"
"")
        self.pase_data_Button.setObjectName("pase_data_Button")
        self.pase_authen_Button = QtWidgets.QPushButton(self.frame_7)
        self.pase_authen_Button.setGeometry(QtCore.QRect(15, 226, 101, 35))
        self.pase_authen_Button.setStyleSheet("QPushButton {\n"
"    background-color: #5b5b5b;      /* Xám đậm */\n"
"    color: #F0F0F0;                 /* Chữ trắng xám */\n"
"    border: 1px solid #444;         /* Viền xám than */\n"
"    border-radius: 8px;             /* Bo góc nhẹ */\n"
"    padding: 6px 12px;              /* Đệm */\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #919191;      /* Hover sáng nhẹ */\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: #2e688d;      /* Nhấn: tối hơn */\n"
"}\n"
"")
        self.pase_authen_Button.setObjectName("pase_authen_Button")
        self.checkBox_enter = QtWidgets.QCheckBox(self.frame_7)
        self.checkBox_enter.setGeometry(QtCore.QRect(8, 150, 121, 17))
        self.checkBox_enter.setObjectName("checkBox_enter")
        self.home_Button = QtWidgets.QPushButton(self.frame_7)
        self.home_Button.setGeometry(QtCore.QRect(15, 336, 101, 27))
        self.home_Button.setStyleSheet("QPushButton {\n"
"    background-color: #5b5b5b;      /* Xám đậm */\n"
"    color: #F0F0F0;                 /* Chữ trắng xám */\n"
"    border: 1px solid #444;         /* Viền xám than */\n"
"    border-radius: 8px;             /* Bo góc nhẹ */\n"
"    padding: 6px 12px;              /* Đệm */\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #919191;      /* Hover sáng nhẹ */\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: #2e688d;      /* Nhấn: tối hơn */\n"
"}\n"
"")
        self.home_Button.setObjectName("home_Button")
        self.start_stop_Button = QtWidgets.QPushButton(self.frame_7)
        self.start_stop_Button.setGeometry(QtCore.QRect(15, 402, 101, 45))
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(True)
        font.setWeight(75)
        self.start_stop_Button.setFont(font)
        self.start_stop_Button.setStyleSheet("QPushButton {\n"
"    background-color: #007455;      /* Xám đậm */\n"
"    color: #f1f1f1;                 /* Chữ trắng xám */\n"
"    border: 1px solid #343434;         /* Viền xám than */\n"
"    border-radius: 8px;             /* Bo góc nhẹ */\n"
"    padding: 6px 12px;              /* Đệm */\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #00b383;      /* Hover sáng nhẹ */\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: #2e688d;      /* Nhấn: tối hơn */\n"
"}\n"
"QPushButton:checked {\n"
"    background-color: red;\n"
"}")
        self.start_stop_Button.setCheckable(True)
        self.start_stop_Button.setObjectName("start_stop_Button")
        self.groupBox_2 = QtWidgets.QGroupBox(self.frame_7)
        self.groupBox_2.setGeometry(QtCore.QRect(6, 7, 121, 91))
        self.groupBox_2.setStyleSheet("QGroupBox {\n"
"    border: 1.2px solid #8d8d8d;\n"
"    border-radius: 5px;\n"
"}\n"
"")
        self.groupBox_2.setTitle("")
        self.groupBox_2.setObjectName("groupBox_2")
        self.label_Error = QtWidgets.QLabel(self.groupBox_2)
        self.label_Error.setGeometry(QtCore.QRect(75, 67, 41, 16))
        self.label_Error.setStyleSheet("color: red;  ")
        self.label_Error.setObjectName("label_Error")
        self.label_suscess = QtWidgets.QLabel(self.groupBox_2)
        self.label_suscess.setGeometry(QtCore.QRect(5, 49, 47, 13))
        self.label_suscess.setObjectName("label_suscess")
        self.label_errro = QtWidgets.QLabel(self.groupBox_2)
        self.label_errro.setGeometry(QtCore.QRect(5, 69, 47, 13))
        self.label_errro.setObjectName("label_errro")
        self.label_total_card = QtWidgets.QLabel(self.groupBox_2)
        self.label_total_card.setGeometry(QtCore.QRect(75, 5, 41, 16))
        self.label_total_card.setStyleSheet("color: #0f0f0f;")
        self.label_total_card.setObjectName("label_total_card")
        self.label_da_xu_ly = QtWidgets.QLabel(self.groupBox_2)
        self.label_da_xu_ly.setGeometry(QtCore.QRect(5, 26, 51, 16))
        self.label_da_xu_ly.setObjectName("label_da_xu_ly")
        self.label_Total_card = QtWidgets.QLabel(self.groupBox_2)
        self.label_Total_card.setGeometry(QtCore.QRect(5, 5, 61, 16))
        self.label_Total_card.setObjectName("label_Total_card")
        self.label_processed = QtWidgets.QLabel(self.groupBox_2)
        self.label_processed.setGeometry(QtCore.QRect(75, 25, 41, 16))
        self.label_processed.setStyleSheet("color:#9700e2;")
        self.label_processed.setObjectName("label_processed")
        self.label_Success = QtWidgets.QLabel(self.groupBox_2)
        self.label_Success.setGeometry(QtCore.QRect(75, 47, 41, 16))
        self.label_Success.setStyleSheet("color:#004eeb;")
        self.label_Success.setObjectName("label_Success")
        MainWindow.setCentralWidget(self.centralwidget)

        self.retranslateUi(MainWindow)
        self.tabWidget_Proxy.setCurrentIndex(0)
        QtCore.QMetaObject.connectSlotsByName(MainWindow)

    def retranslateUi(self, MainWindow):
        _translate = QtCore.QCoreApplication.translate
        MainWindow.setWindowTitle(_translate("MainWindow", "MainWindow"))
        item = self.tableWidget.horizontalHeaderItem(1)
        item.setText(_translate("MainWindow", "Devices"))
        item = self.tableWidget.horizontalHeaderItem(2)
        item.setText(_translate("MainWindow", "Pass"))
        item = self.tableWidget.horizontalHeaderItem(3)
        item.setText(_translate("MainWindow", "Authen"))
        item = self.tableWidget.horizontalHeaderItem(4)
        item.setText(_translate("MainWindow", "Data"))
        item = self.tableWidget.horizontalHeaderItem(5)
        item.setText(_translate("MainWindow", "Log"))
        self.xoa_the_close_Button.setText(_translate("MainWindow", "..."))
        self.tabWidget_Proxy.setTabText(self.tabWidget_Proxy.indexOf(self.tab), _translate("MainWindow", "Auto_K"))
        self.groupBox.setTitle(_translate("MainWindow", "Setting auto"))
        self.comboBox_4.setItemText(0, _translate("MainWindow", "Mua Game"))
        self.comboBox_wallet_ads.setItemText(0, _translate("MainWindow", "Add Wallet"))
        self.comboBox_wallet_ads.setItemText(1, _translate("MainWindow", "Add Ads"))
        self.comboBox_restore.setItemText(0, _translate("MainWindow", "Restore"))
        self.comboBox_restore.setItemText(1, _translate("MainWindow", "Changer"))
        self.comboBox_del_close.setItemText(0, _translate("MainWindow", "Xoá thẻ close"))
        self.comboBox_del_close.setItemText(1, _translate("MainWindow", "Đóng hồ sơ"))
        self.label_5.setText(_translate("MainWindow", "Số lần add thẻ"))
        self.label_4.setText(_translate("MainWindow", "Chọn gói mua"))
        self.comboBox.setItemText(0, _translate("MainWindow", "ChPlay"))
        self.label.setText(_translate("MainWindow", "Port Doma "))
        self.label_3.setText(_translate("MainWindow", "Giới hạn số lần add thất bại"))
        self.licenses.setText(_translate("MainWindow", "Licenses Info 🔑"))
        self.label_6.setText(_translate("MainWindow", "Kích thước View"))
        self.label_2.setText(_translate("MainWindow", "Số tab 1 hàng"))
        self.open_card_Button.setText(_translate("MainWindow", "Data"))
        self.pushButton.setText(_translate("MainWindow", "📂 Open Data"))
        self.open_mail_Button.setText(_translate("MainWindow", "EMail"))
        self.tabWidget_Proxy.setTabText(self.tabWidget_Proxy.indexOf(self.tab_2), _translate("MainWindow", "Setting"))
        self.groupBox_proxy.setTitle(_translate("MainWindow", "Dvices gán proxy:"))
        self.pushButton_8.setText(_translate("MainWindow", "🛑 Open Chrome"))
        self.pushButton_5.setText(_translate("MainWindow", "🧹 Dừng  Proxy"))
        self.pushButton_2.setText(_translate("MainWindow", "🎯  Đổi Proxy"))
        self.pushButton_7.setText(_translate("MainWindow", "🎨 Auto-Assign: ON"))
        self.pushButton_6.setText(_translate("MainWindow", "📋 Config Proxy"))
        self.pushButton_4.setText(_translate("MainWindow", "🧪 Check Proxy"))
        self.label_9.setText(_translate("MainWindow", "Ports:"))
        self.label_8.setText(_translate("MainWindow", "IP Sever :"))
        self.tabWidget_Proxy.setTabText(self.tabWidget_Proxy.indexOf(self.tab_3), _translate("MainWindow", "Proxy"))
        self.paste_pass_Button.setText(_translate("MainWindow", "Paste Pass"))
        self.view_Button.setText(_translate("MainWindow", "View"))
        self.pase_data_Button.setText(_translate("MainWindow", "Paste Data"))
        self.pase_authen_Button.setText(_translate("MainWindow", "Paste Authen"))
        self.checkBox_enter.setText(_translate("MainWindow", "Enter sau khi paste"))
        self.home_Button.setText(_translate("MainWindow", "🏠 Home"))
        self.start_stop_Button.setText(_translate("MainWindow", "Start"))
        self.label_Error.setText(_translate("MainWindow", "0"))
        self.label_suscess.setText(_translate("MainWindow", "Success"))
        self.label_errro.setText(_translate("MainWindow", "Error"))
        self.label_total_card.setText(_translate("MainWindow", "0"))
        self.label_da_xu_ly.setText(_translate("MainWindow", "Đã xử lý"))
        self.label_Total_card.setText(_translate("MainWindow", "Tổng số thẻ"))
        self.label_processed.setText(_translate("MainWindow", "0"))
        self.label_Success.setText(_translate("MainWindow", "0"))
