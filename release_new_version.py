#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script tự động hóa việc release version mới của AutoK
Thực hiện tất cả các bước cần thiết
"""

import os
import sys
import json
import requests
import subprocess
from datetime import datetime

# Cấu hình server
SERVER_CONFIG = {
    'api_url': 'https://your-server.com/api/admin/update',
    'admin_key': 'your_secret_admin_key_here',
    'download_base_url': 'https://your-server.com/downloads/',
    'upload_command': 'scp {file} <EMAIL>:/path/to/downloads/'
}

def get_current_version():
    """Lấy version hiện tại"""
    try:
        with open('version.txt', 'r') as f:
            return f.read().strip()
    except Exception:
        return "1.0.0"

def update_version_file(new_version):
    """Cập nhật file version.txt"""
    try:
        with open('version.txt', 'w') as f:
            f.write(new_version)
        print(f"✅ Đã cập nhật version.txt thành {new_version}")
        return True
    except Exception as e:
        print(f"❌ Lỗi cập nhật version.txt: {e}")
        return False

def run_command(command, description):
    """Chạy command và hiển thị kết quả"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description} thành công")
            return True
        else:
            print(f"❌ {description} thất bại: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Lỗi khi {description}: {e}")
        return False

def create_packages(from_version, to_version, changelog):
    """Tạo cả delta và full packages"""
    print(f"\n📦 Tạo packages...")
    
    # Tạo delta package
    delta_cmd = f'python create_delta_update.py delta {from_version} {to_version} "{changelog}"'
    delta_success = run_command(delta_cmd, "Tạo delta package")
    
    # Tạo full package
    full_cmd = f'python create_update_package.py {to_version} "{changelog}"'
    full_success = run_command(full_cmd, "Tạo full package")
    
    return delta_success, full_success

def upload_files(version):
    """Upload files lên server"""
    print(f"\n🌐 Upload files lên server...")
    
    files_to_upload = [
        f"autok_v{version}.zip",
        f"autok_delta_v{version}.zip"
    ]
    
    upload_success = True
    
    for file in files_to_upload:
        if os.path.exists(file):
            upload_cmd = SERVER_CONFIG['upload_command'].format(file=file)
            if run_command(upload_cmd, f"Upload {file}"):
                print(f"✅ Đã upload {file}")
            else:
                print(f"❌ Không thể upload {file}")
                upload_success = False
        else:
            print(f"⚠️ File không tồn tại: {file}")
            upload_success = False
    
    return upload_success

def update_server_config(version, changelog):
    """Cập nhật config trên server qua API"""
    print(f"\n⚙️ Cập nhật server config...")
    
    data = {
        'admin_key': SERVER_CONFIG['admin_key'],
        'version': version,
        'download_url': SERVER_CONFIG['download_base_url'] + f"autok_v{version}.zip",
        'delta_url': SERVER_CONFIG['download_base_url'] + f"autok_delta_v{version}.zip",
        'update_type': 'delta',
        'changelog': changelog
    }
    
    try:
        response = requests.post(SERVER_CONFIG['api_url'], data=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ Đã cập nhật server config thành công")
                return True
            else:
                print(f"❌ Server trả về lỗi: {result.get('error', 'Unknown error')}")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Lỗi khi cập nhật server config: {e}")
        print("💡 Bạn có thể cập nhật thủ công bằng cách sửa file PHP trên server")
        return False

def create_release_notes(version, changelog):
    """Tạo file release notes"""
    release_notes = f"""# AutoK Release Notes

## Version {version}
**Release Date:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

### Changes:
{changelog}

### Files:
- `autok_v{version}.zip` - Full update package
- `autok_delta_v{version}.zip` - Delta update package (from previous version)

### Installation:
1. Download the appropriate package
2. Extract to AutoK directory
3. Run AutoK.exe

### Auto Update:
Users with auto-update enabled will be notified automatically.
"""
    
    with open(f"RELEASE_NOTES_v{version}.md", 'w', encoding='utf-8') as f:
        f.write(release_notes)
    
    print(f"✅ Đã tạo release notes: RELEASE_NOTES_v{version}.md")

def main():
    """Hàm chính"""
    print("🚀 AutoK Release Manager")
    print("=" * 50)
    
    if len(sys.argv) < 3:
        print("Usage: python release_new_version.py <new_version> <changelog>")
        print("\nExample:")
        print('python release_new_version.py 1.1.0 "Fixed addads stop issue\\nAdded anonymous Google\\nImproved auto-update"')
        return
    
    new_version = sys.argv[1]
    changelog = sys.argv[2]
    
    # Validate version format
    try:
        version_parts = new_version.split('.')
        if len(version_parts) != 3:
            raise ValueError("Version must be in format X.Y.Z")
        for part in version_parts:
            int(part)
    except ValueError as e:
        print(f"❌ Invalid version format: {e}")
        return
    
    current_version = get_current_version()
    
    print(f"📋 Release Information:")
    print(f"   Current Version: {current_version}")
    print(f"   New Version: {new_version}")
    print(f"   Changelog: {changelog}")
    print()
    
    # Confirm
    confirm = input("Bạn có chắc chắn muốn release version này? (y/N): ")
    if confirm.lower() != 'y':
        print("❌ Đã hủy release")
        return
    
    # Step 1: Update version file
    if not update_version_file(new_version):
        return
    
    # Step 2: Create manifest for new version (if first time)
    manifest_file = f"manifest_v{current_version}.json"
    if not os.path.exists(manifest_file):
        print(f"📝 Tạo manifest cho version {current_version}...")
        if not run_command(f'python create_delta_update.py manifest {current_version}', 
                          f"Tạo manifest {current_version}"):
            return
    
    # Step 3: Create packages
    delta_success, full_success = create_packages(current_version, new_version, changelog)
    
    if not (delta_success and full_success):
        print("❌ Không thể tạo packages. Dừng release.")
        return
    
    # Step 4: Upload files (optional, có thể skip nếu upload thủ công)
    upload_choice = input("\nBạn có muốn tự động upload files lên server? (y/N): ")
    if upload_choice.lower() == 'y':
        if not upload_files(new_version):
            print("⚠️ Upload thất bại, nhưng có thể tiếp tục với upload thủ công")
    
    # Step 5: Update server config
    update_choice = input("Bạn có muốn tự động cập nhật server config? (y/N): ")
    if update_choice.lower() == 'y':
        update_server_config(new_version, changelog)
    
    # Step 6: Create release notes
    create_release_notes(new_version, changelog)
    
    # Step 7: Create new manifest for next release
    print(f"\n📝 Tạo manifest cho version {new_version}...")
    run_command(f'python create_delta_update.py manifest {new_version}', 
                f"Tạo manifest {new_version}")
    
    print(f"\n🎉 Release {new_version} hoàn thành!")
    print(f"\n📋 Các file đã tạo:")
    print(f"   - autok_v{new_version}.zip (Full package)")
    print(f"   - autok_delta_v{new_version}.zip (Delta package)")
    print(f"   - manifest_v{new_version}.json (Manifest)")
    print(f"   - RELEASE_NOTES_v{new_version}.md (Release notes)")
    
    print(f"\n📋 Bước tiếp theo:")
    if upload_choice.lower() != 'y':
        print(f"   1. Upload files lên server:")
        print(f"      - autok_v{new_version}.zip")
        print(f"      - autok_delta_v{new_version}.zip")
    
    if update_choice.lower() != 'y':
        print(f"   2. Cập nhật server config với:")
        print(f"      - Version: {new_version}")
        print(f"      - Download URL: {SERVER_CONFIG['download_base_url']}autok_v{new_version}.zip")
        print(f"      - Delta URL: {SERVER_CONFIG['download_base_url']}autok_delta_v{new_version}.zip")
    
    print(f"   3. Test auto-update với app version cũ")
    print(f"   4. Thông báo cho users về version mới")

if __name__ == "__main__":
    main()
