# 🔒 Hướng dẫn Bảo mật AutoK

## Tổng quan
Tài liệu này hướng dẫn cách bảo mật ứng dụng AutoK chống lại dịch ngược (reverse engineering) và các mối đe dọa bảo mật khác.

## 🛡️ C<PERSON>c lớp bảo mật được triển khai

### 1. **Code Obfuscation (PyArmor)**
- Làm rối mã nguồn Python
- Mã hóa bytecode
- Bảo vệ chống debug
- License protection

### 2. **Binary Compilation (Nuitka)**
- Biên dịch Python thành C++
- Tạo executable độc lập
- Tối ưu hóa performance
- <PERSON>h<PERSON> dịch ngược hơn

### 3. **Runtime Security**
- Anti-debugging checks
- Integrity verification
- License validation
- Encrypted sensitive data

### 4. **File Protection**
- UPX compression
- Binary packing
- Resource encryption

## 🚀 Cách sử dụng

### Bước 1: Cài đặt công cụ bảo mật
```bash
python setup_security.py
```

### Bước 2: Build ứng dụng bảo mật
```bash
python build_secure.py
```

### Bước 3: Kiểm tra kết quả
File exe bảo mật sẽ được tạo trong thư mục `dist_secure/`

## 📋 Chi tiết các công cụ

### PyArmor
- **Mức độ**: Advanced Level 2
- **Tính năng**: JIT protection, String mixing, Restrict mode
- **License**: Có thể set thời hạn sử dụng

### Nuitka
- **Mode**: Standalone + Onefile
- **Optimization**: Size optimized
- **Console**: Disabled (GUI only)

### Security Core
- **Encryption**: AES-256 với PBKDF2
- **Anti-debug**: Timing checks, Debugger detection
- **Integrity**: File hash verification

## 🔧 Cấu hình nâng cao

### Tùy chỉnh PyArmor
```bash
# Mức bảo mật cao nhất
pyarmor obfuscate --advanced 2 --restrict 4 --enable-jit --mix-str AutoK.py

# Với license có thời hạn
pyarmor licenses --expired 2025-12-31 --bind-disk mylicense
pyarmor obfuscate --with-license licenses/mylicense/license.lic AutoK.py
```

### Tùy chỉnh Nuitka
```bash
# Build với plugin PyQt5
nuitka --standalone --onefile --enable-plugin=pyqt5 AutoK.py

# Thêm icon và metadata
nuitka --windows-icon-from-ico=icon.ico --windows-company-name="Your Company" AutoK.py
```

## 🛡️ Mức độ bảo mật

### Cơ bản (Basic)
- ✅ PyInstaller + UPX
- ✅ Basic obfuscation
- **Thời gian dịch ngược**: 1-2 giờ

### Trung bình (Intermediate)  
- ✅ PyArmor Standard
- ✅ Nuitka compilation
- ✅ Runtime checks
- **Thời gian dịch ngược**: 1-2 ngày

### Cao (Advanced)
- ✅ PyArmor Advanced + JIT
- ✅ Nuitka + UPX
- ✅ Anti-debugging
- ✅ Integrity checks
- ✅ License protection
- **Thời gian dịch ngược**: 1-2 tuần

### Chuyên nghiệp (Professional)
- ✅ Tất cả tính năng Advanced
- ✅ Custom packer
- ✅ Server-side validation
- ✅ Hardware binding
- ✅ Code splitting
- **Thời gian dịch ngược**: 1-3 tháng

## ⚠️ Lưu ý quan trọng

### Backup Source Code
- **LUÔN** backup source code gốc
- PyArmor obfuscation là **không thể đảo ngược**
- Lưu trữ source code ở nơi an toàn

### Testing
- Test kỹ file exe sau khi build
- Kiểm tra tất cả tính năng
- Test trên nhiều máy khác nhau

### Performance
- File exe có thể lớn hơn (50-200MB)
- Startup time có thể chậm hơn (2-5 giây)
- RAM usage có thể cao hơn

### Compatibility
- Test trên Windows 7/8/10/11
- Kiểm tra antivirus false positive
- Test với/không có admin rights

## 🔍 Kiểm tra bảo mật

### Tools để test
```bash
# Kiểm tra strings
strings AutoK_Secure.exe | grep -i "password\|key\|secret"

# Kiểm tra imports
objdump -p AutoK_Secure.exe | grep "DLL Name"

# Kiểm tra packer
detect-it-easy AutoK_Secure.exe
```

### Checklist bảo mật
- [ ] Không có strings nhạy cảm trong binary
- [ ] Không có debug symbols
- [ ] Anti-debugging hoạt động
- [ ] License check hoạt động
- [ ] Integrity check hoạt động
- [ ] File size hợp lý (<200MB)

## 🆘 Troubleshooting

### PyArmor errors
```bash
# Clear cache
pyarmor clean

# Reinstall
pip uninstall pyarmor
pip install pyarmor
```

### Nuitka errors
```bash
# Install C++ compiler
# Windows: Visual Studio Build Tools
# Linux: gcc, g++
```

### Runtime errors
- Kiểm tra file paths
- Kiểm tra permissions
- Kiểm tra dependencies

## 📞 Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra log files
2. Test với source code gốc
3. Kiểm tra dependencies
4. Liên hệ support team

---
**Lưu ý**: Bảo mật 100% là không thể. Mục tiêu là làm cho việc dịch ngược trở nên khó khăn và tốn thời gian đến mức không đáng để thực hiện.
